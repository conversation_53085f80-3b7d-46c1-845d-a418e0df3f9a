[{"directory": "C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/.cxx/Debug/4212f3t5/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/yoga\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\C_\\Users\\Elisee\\Documents\\Mientior_livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\NativeModules\\WorkletsModuleProxy.cpp.o -c \"C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\NativeModules\\WorkletsModuleProxy.cpp\"", "file": "C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\NativeModules\\WorkletsModuleProxy.cpp"}, {"directory": "C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/.cxx/Debug/4212f3t5/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/yoga\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\C_\\Users\\Elisee\\Documents\\Mientior_livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\NativeModules\\WorkletsModuleProxySpec.cpp.o -c \"C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\NativeModules\\WorkletsModuleProxySpec.cpp\"", "file": "C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\NativeModules\\WorkletsModuleProxySpec.cpp"}, {"directory": "C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/.cxx/Debug/4212f3t5/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/yoga\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\C_\\Users\\Elisee\\Documents\\Mientior_livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Registries\\EventHandlerRegistry.cpp.o -c \"C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Registries\\EventHandlerRegistry.cpp\"", "file": "C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Registries\\EventHandlerRegistry.cpp"}, {"directory": "C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/.cxx/Debug/4212f3t5/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/yoga\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\C_\\Users\\Elisee\\Documents\\Mientior_livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Registries\\WorkletRuntimeRegistry.cpp.o -c \"C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Registries\\WorkletRuntimeRegistry.cpp\"", "file": "C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Registries\\WorkletRuntimeRegistry.cpp"}, {"directory": "C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/.cxx/Debug/4212f3t5/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/yoga\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\acc6d5c989e3e398e3d79b196b996379\\Shareables.cpp.o -c \"C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\SharedItems\\Shareables.cpp\"", "file": "C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\SharedItems\\Shareables.cpp"}, {"directory": "C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/.cxx/Debug/4212f3t5/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/yoga\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\f55d245126d8cdd46c0c91a7a4da027d\\Tools\\AsyncQueue.cpp.o -c \"C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\AsyncQueue.cpp\"", "file": "C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\AsyncQueue.cpp"}, {"directory": "C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/.cxx/Debug/4212f3t5/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/yoga\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\848e9dab9ab52d33b2228e7855dc069f\\JSISerializer.cpp.o -c \"C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\JSISerializer.cpp\"", "file": "C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\JSISerializer.cpp"}, {"directory": "C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/.cxx/Debug/4212f3t5/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/yoga\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\f55d245126d8cdd46c0c91a7a4da027d\\Tools\\JSLogger.cpp.o -c \"C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\JSLogger.cpp\"", "file": "C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\JSLogger.cpp"}, {"directory": "C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/.cxx/Debug/4212f3t5/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/yoga\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\f55d245126d8cdd46c0c91a7a4da027d\\Tools\\JSScheduler.cpp.o -c \"C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\JSScheduler.cpp\"", "file": "C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\JSScheduler.cpp"}, {"directory": "C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/.cxx/Debug/4212f3t5/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/yoga\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\848e9dab9ab52d33b2228e7855dc069f\\ReanimatedJSIUtils.cpp.o -c \"C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\ReanimatedJSIUtils.cpp\"", "file": "C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\ReanimatedJSIUtils.cpp"}, {"directory": "C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/.cxx/Debug/4212f3t5/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/yoga\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\848e9dab9ab52d33b2228e7855dc069f\\ReanimatedVersion.cpp.o -c \"C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\ReanimatedVersion.cpp\"", "file": "C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\ReanimatedVersion.cpp"}, {"directory": "C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/.cxx/Debug/4212f3t5/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/yoga\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\f55d245126d8cdd46c0c91a7a4da027d\\Tools\\UIScheduler.cpp.o -c \"C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\UIScheduler.cpp\"", "file": "C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\UIScheduler.cpp"}, {"directory": "C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/.cxx/Debug/4212f3t5/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/yoga\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\C_\\Users\\Elisee\\Documents\\Mientior_livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\WorkletEventHandler.cpp.o -c \"C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\WorkletEventHandler.cpp\"", "file": "C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\Tools\\WorkletEventHandler.cpp"}, {"directory": "C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/.cxx/Debug/4212f3t5/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/yoga\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\C_\\Users\\Elisee\\Documents\\Mientior_livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\WorkletRuntime\\RNRuntimeWorkletDecorator.cpp.o -c \"C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\WorkletRuntime\\RNRuntimeWorkletDecorator.cpp\"", "file": "C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\WorkletRuntime\\RNRuntimeWorkletDecorator.cpp"}, {"directory": "C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/.cxx/Debug/4212f3t5/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/yoga\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\C_\\Users\\Elisee\\Documents\\Mientior_livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\WorkletRuntime\\ReanimatedHermesRuntime.cpp.o -c \"C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\WorkletRuntime\\ReanimatedHermesRuntime.cpp\"", "file": "C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\WorkletRuntime\\ReanimatedHermesRuntime.cpp"}, {"directory": "C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/.cxx/Debug/4212f3t5/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/yoga\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\11ac37f919a2eba3c8fcb77b57fd582f\\ReanimatedRuntime.cpp.o -c \"C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\WorkletRuntime\\ReanimatedRuntime.cpp\"", "file": "C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\WorkletRuntime\\ReanimatedRuntime.cpp"}, {"directory": "C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/.cxx/Debug/4212f3t5/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/yoga\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\11ac37f919a2eba3c8fcb77b57fd582f\\WorkletRuntime.cpp.o -c \"C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\WorkletRuntime\\WorkletRuntime.cpp\"", "file": "C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\WorkletRuntime\\WorkletRuntime.cpp"}, {"directory": "C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/.cxx/Debug/4212f3t5/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/yoga\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\C_\\Users\\Elisee\\Documents\\Mientior_livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\WorkletRuntime\\WorkletRuntimeDecorator.cpp.o -c \"C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\WorkletRuntime\\WorkletRuntimeDecorator.cpp\"", "file": "C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\worklets\\WorkletRuntime\\WorkletRuntimeDecorator.cpp"}, {"directory": "C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/.cxx/Debug/4212f3t5/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/yoga\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\android\\AndroidUIScheduler.cpp.o -c \"C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\worklets\\android\\AndroidUIScheduler.cpp\"", "file": "C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\worklets\\android\\AndroidUIScheduler.cpp"}, {"directory": "C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/.cxx/Debug/4212f3t5/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/yoga\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\android\\PlatformLogger.cpp.o -c \"C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\worklets\\android\\PlatformLogger.cpp\"", "file": "C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\worklets\\android\\PlatformLogger.cpp"}, {"directory": "C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/.cxx/Debug/4212f3t5/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/yoga\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\android\\WorkletsModule.cpp.o -c \"C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\worklets\\android\\WorkletsModule.cpp\"", "file": "C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\worklets\\android\\WorkletsModule.cpp"}, {"directory": "C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/.cxx/Debug/4212f3t5/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dworklets_EXPORTS -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/yoga\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\worklets\\CMakeFiles\\worklets.dir\\android\\WorkletsOnLoad.cpp.o -c \"C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\worklets\\android\\WorkletsOnLoad.cpp\"", "file": "C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\worklets\\android\\WorkletsOnLoad.cpp"}, {"directory": "C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/.cxx/Debug/4212f3t5/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/yoga\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\reanimated\\CMakeFiles\\reanimated.dir\\C_\\Users\\Elisee\\Documents\\Mientior_livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\AnimatedSensor\\AnimatedSensorModule.cpp.o -c \"C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\AnimatedSensor\\AnimatedSensorModule.cpp\"", "file": "C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\AnimatedSensor\\AnimatedSensorModule.cpp"}, {"directory": "C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/.cxx/Debug/4212f3t5/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/yoga\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\reanimated\\CMakeFiles\\reanimated.dir\\8ea38f2f8737d3b26ff34b1616826e80\\PropsRegistry.cpp.o -c \"C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\Fabric\\PropsRegistry.cpp\"", "file": "C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\Fabric\\PropsRegistry.cpp"}, {"directory": "C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/.cxx/Debug/4212f3t5/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/yoga\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\reanimated\\CMakeFiles\\reanimated.dir\\C_\\Users\\Elisee\\Documents\\Mientior_livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\Fabric\\ReanimatedCommitHook.cpp.o -c \"C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\Fabric\\ReanimatedCommitHook.cpp\"", "file": "C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\Fabric\\ReanimatedCommitHook.cpp"}, {"directory": "C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/.cxx/Debug/4212f3t5/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/yoga\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\reanimated\\CMakeFiles\\reanimated.dir\\C_\\Users\\Elisee\\Documents\\Mientior_livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\Fabric\\ReanimatedMountHook.cpp.o -c \"C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\Fabric\\ReanimatedMountHook.cpp\"", "file": "C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\Fabric\\ReanimatedMountHook.cpp"}, {"directory": "C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/.cxx/Debug/4212f3t5/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/yoga\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\reanimated\\CMakeFiles\\reanimated.dir\\C_\\Users\\Elisee\\Documents\\Mientior_livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\Fabric\\ShadowTreeCloner.cpp.o -c \"C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\Fabric\\ShadowTreeCloner.cpp\"", "file": "C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\Fabric\\ShadowTreeCloner.cpp"}, {"directory": "C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/.cxx/Debug/4212f3t5/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/yoga\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\reanimated\\CMakeFiles\\reanimated.dir\\C_\\Users\\Elisee\\Documents\\Mientior_livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\LayoutAnimations\\LayoutAnimationsManager.cpp.o -c \"C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\LayoutAnimations\\LayoutAnimationsManager.cpp\"", "file": "C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\LayoutAnimations\\LayoutAnimationsManager.cpp"}, {"directory": "C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/.cxx/Debug/4212f3t5/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/yoga\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\reanimated\\CMakeFiles\\reanimated.dir\\C_\\Users\\Elisee\\Documents\\Mientior_livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\LayoutAnimations\\LayoutAnimationsProxy.cpp.o -c \"C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\LayoutAnimations\\LayoutAnimationsProxy.cpp\"", "file": "C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\LayoutAnimations\\LayoutAnimationsProxy.cpp"}, {"directory": "C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/.cxx/Debug/4212f3t5/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/yoga\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\reanimated\\CMakeFiles\\reanimated.dir\\C_\\Users\\Elisee\\Documents\\Mientior_livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\LayoutAnimations\\LayoutAnimationsUtils.cpp.o -c \"C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\LayoutAnimations\\LayoutAnimationsUtils.cpp\"", "file": "C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\LayoutAnimations\\LayoutAnimationsUtils.cpp"}, {"directory": "C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/.cxx/Debug/4212f3t5/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/yoga\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\reanimated\\CMakeFiles\\reanimated.dir\\C_\\Users\\Elisee\\Documents\\Mientior_livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\NativeModules\\ReanimatedModuleProxy.cpp.o -c \"C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\NativeModules\\ReanimatedModuleProxy.cpp\"", "file": "C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\NativeModules\\ReanimatedModuleProxy.cpp"}, {"directory": "C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/.cxx/Debug/4212f3t5/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/yoga\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\reanimated\\CMakeFiles\\reanimated.dir\\C_\\Users\\Elisee\\Documents\\Mientior_livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\NativeModules\\ReanimatedModuleProxySpec.cpp.o -c \"C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\NativeModules\\ReanimatedModuleProxySpec.cpp\"", "file": "C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\NativeModules\\ReanimatedModuleProxySpec.cpp"}, {"directory": "C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/.cxx/Debug/4212f3t5/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/yoga\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\reanimated\\CMakeFiles\\reanimated.dir\\C_\\Users\\Elisee\\Documents\\Mientior_livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\RuntimeDecorators\\RNRuntimeDecorator.cpp.o -c \"C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\RuntimeDecorators\\RNRuntimeDecorator.cpp\"", "file": "C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\RuntimeDecorators\\RNRuntimeDecorator.cpp"}, {"directory": "C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/.cxx/Debug/4212f3t5/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/yoga\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\reanimated\\CMakeFiles\\reanimated.dir\\C_\\Users\\Elisee\\Documents\\Mientior_livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\RuntimeDecorators\\UIRuntimeDecorator.cpp.o -c \"C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\RuntimeDecorators\\UIRuntimeDecorator.cpp\"", "file": "C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\RuntimeDecorators\\UIRuntimeDecorator.cpp"}, {"directory": "C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/.cxx/Debug/4212f3t5/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/yoga\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\reanimated\\CMakeFiles\\reanimated.dir\\b147319084345b3534fe8072ab18a9c2\\FeaturesConfig.cpp.o -c \"C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\Tools\\FeaturesConfig.cpp\"", "file": "C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\Common\\cpp\\reanimated\\Tools\\FeaturesConfig.cpp"}, {"directory": "C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/.cxx/Debug/4212f3t5/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/yoga\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\reanimated\\CMakeFiles\\reanimated.dir\\android\\JNIHelper.cpp.o -c \"C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\reanimated\\android\\JNIHelper.cpp\"", "file": "C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\reanimated\\android\\JNIHelper.cpp"}, {"directory": "C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/.cxx/Debug/4212f3t5/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/yoga\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\reanimated\\CMakeFiles\\reanimated.dir\\android\\LayoutAnimations.cpp.o -c \"C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\reanimated\\android\\LayoutAnimations.cpp\"", "file": "C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\reanimated\\android\\LayoutAnimations.cpp"}, {"directory": "C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/.cxx/Debug/4212f3t5/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/yoga\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\reanimated\\CMakeFiles\\reanimated.dir\\android\\NativeProxy.cpp.o -c \"C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\reanimated\\android\\NativeProxy.cpp\"", "file": "C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\reanimated\\android\\NativeProxy.cpp"}, {"directory": "C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/.cxx/Debug/4212f3t5/armeabi-v7a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=armv7-none-linux-androideabi24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreanimated_EXPORTS -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/../Common/cpp\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/callinvoker\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/runtimeexecutor\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/yoga\" -I\"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx\" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20 -o src\\main\\cpp\\reanimated\\CMakeFiles\\reanimated.dir\\android\\OnLoad.cpp.o -c \"C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\reanimated\\android\\OnLoad.cpp\"", "file": "C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\reanimated\\android\\OnLoad.cpp"}]