// ===============================================
// SERVICE DE GESTION DES PROMOTIONS
// ===============================================

import AsyncStorage from '@react-native-async-storage/async-storage';
import { supabase } from './supabase';
import { 
  Promotion, 
  PromotionRaw, 
  PromotionsQueryParams, 
  PromotionsResponse,
  PROMOTION_CONSTANTS,
  isPromotionActive 
} from '../types/promotion';

/**
 * Service pour gérer les promotions avec Supabase
 */
export class PromotionService {
  private static instance: PromotionService;
  private cache: Map<string, { data: Promotion[]; timestamp: number }> = new Map();

  static getInstance(): PromotionService {
    if (!PromotionService.instance) {
      PromotionService.instance = new PromotionService();
    }
    return PromotionService.instance;
  }

  /**
   * Récupérer les promotions actives depuis Supabase
   */
  async getActivePromotions(params: PromotionsQueryParams = {}): Promise<Promotion[]> {
    try {
      const {
        limit = PROMOTION_CONSTANTS.DEFAULT_LIMIT,
        service_type_id,
        is_active = true,
        include_expired = false
      } = params;

      console.log('🎁 PromotionService: Fetching active promotions', {
        limit,
        service_type_id,
        include_expired,
        current_time: new Date().toISOString()
      });

      if (!supabase) {
        console.warn('⚠️ Supabase client not available for promotions');
        // Essayer de récupérer depuis le cache
        const cachedPromotions = await this.getCachedPromotions('active');
        return cachedPromotions;
      }

      let query = supabase
        .from('promotions')
        .select('*')
        .eq('is_active', is_active)
        .order('created_at', { ascending: false })
        .limit(limit);

      // Filtrer par type de service si spécifié
      if (service_type_id) {
        query = query.eq('service_type_id', service_type_id);
      }

      // Filtrer par date si on n'inclut pas les promotions expirées
      if (!include_expired) {
        const now = new Date().toISOString();
        // Promotions qui ont commencé (start_date <= now) ET qui ne sont pas expirées (end_date >= now OU end_date IS NULL)
        query = query.lte('start_date', now).or(`end_date.is.null,end_date.gte.${now}`);
      }

      const { data, error } = await query;

      if (error) {
        console.error('❌ PromotionService: Error fetching promotions', error);
        throw new Error(`Erreur lors du chargement des promotions: ${error.message}`);
      }

      if (!data) {
        console.log('📭 PromotionService: No promotions found');
        return [];
      }

      console.log(`📊 PromotionService: Raw data from Supabase:`, {
        count: data.length,
        promotions: data.map(p => ({ id: p.id, title: p.title, is_active: p.is_active }))
      });

      const promotions = data.map(this.transformPromotionData);
      
      // Filtrer côté client pour s'assurer que les promotions sont vraiment actives
      const activePromotions = include_expired 
        ? promotions 
        : promotions.filter(isPromotionActive);

      console.log(`✅ PromotionService: Found ${activePromotions.length} active promotions`);
      
      // Mettre en cache les résultats
      await this.cachePromotions('active', activePromotions);
      
      return activePromotions;

    } catch (error) {
      console.error('❌ PromotionService: Error in getActivePromotions', error);
      
      // Essayer de récupérer depuis le cache en cas d'erreur
      const cachedPromotions = await this.getCachedPromotions('active');
      if (cachedPromotions.length > 0) {
        console.log('📦 PromotionService: Returning cached promotions');
        return cachedPromotions;
      }
      
      throw error;
    }
  }

  /**
   * Récupérer une promotion par ID
   */
  async getPromotionById(id: string): Promise<Promotion | null> {
    try {
      console.log('🎁 PromotionService: Fetching promotion by ID', id);

      if (!supabase) {
        console.warn('⚠️ Supabase client not available for promotion by ID');
        return null;
      }

      const { data, error } = await supabase
        .from('promotions')
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        console.error('❌ PromotionService: Error fetching promotion by ID', error);
        return null;
      }

      if (!data) {
        console.log('📭 PromotionService: Promotion not found');
        return null;
      }

      return this.transformPromotionData(data);

    } catch (error) {
      console.error('❌ PromotionService: Error in getPromotionById', error);
      return null;
    }
  }

  /**
   * Récupérer les promotions par type de service
   */
  async getPromotionsByServiceType(serviceTypeId: string): Promise<Promotion[]> {
    return this.getActivePromotions({ service_type_id: serviceTypeId });
  }

  /**
   * Transformer les données brutes de Supabase en objet Promotion
   */
  private transformPromotionData = (raw: PromotionRaw): Promotion => {
    return {
      id: raw.id,
      title: raw.title,
      description: raw.description || undefined,
      image_url: raw.image_url || PROMOTION_CONSTANTS.DEFAULT_IMAGE,
      discount_percentage: raw.discount_percentage || undefined,
      start_date: raw.start_date,
      end_date: raw.end_date || undefined,
      is_active: raw.is_active,
      service_type_id: raw.service_type_id || undefined,
      created_at: raw.created_at,
      updated_at: raw.updated_at,
    };
  };

  /**
   * Mettre en cache les promotions dans AsyncStorage
   */
  private async cachePromotions(key: string, promotions: Promotion[]): Promise<void> {
    try {
      const cacheData = {
        data: promotions,
        timestamp: Date.now(),
      };
      
      await AsyncStorage.setItem(`promotions_${key}`, JSON.stringify(cacheData));
      this.cache.set(key, cacheData);
      
      console.log(`📦 PromotionService: Cached ${promotions.length} promotions with key ${key}`);
    } catch (error) {
      console.error('❌ PromotionService: Error caching promotions', error);
    }
  }

  /**
   * Récupérer les promotions depuis le cache
   */
  private async getCachedPromotions(key: string): Promise<Promotion[]> {
    try {
      // Vérifier d'abord le cache en mémoire
      const memoryCache = this.cache.get(key);
      if (memoryCache && Date.now() - memoryCache.timestamp < PROMOTION_CONSTANTS.CACHE_TTL) {
        console.log(`📦 PromotionService: Returning promotions from memory cache`);
        return memoryCache.data;
      }

      // Vérifier le cache AsyncStorage
      const cachedData = await AsyncStorage.getItem(`promotions_${key}`);
      if (cachedData) {
        const parsed = JSON.parse(cachedData);
        
        // Vérifier si le cache n'est pas expiré
        if (Date.now() - parsed.timestamp < PROMOTION_CONSTANTS.CACHE_TTL) {
          console.log(`📦 PromotionService: Returning promotions from AsyncStorage cache`);
          this.cache.set(key, parsed); // Remettre en cache mémoire
          return parsed.data;
        }
      }

      return [];
    } catch (error) {
      console.error('❌ PromotionService: Error getting cached promotions', error);
      return [];
    }
  }

  /**
   * Vider le cache des promotions
   */
  async clearCache(): Promise<void> {
    try {
      this.cache.clear();
      
      // Supprimer tous les caches de promotions d'AsyncStorage
      const keys = await AsyncStorage.getAllKeys();
      const promotionKeys = keys.filter(key => key.startsWith('promotions_'));
      
      if (promotionKeys.length > 0) {
        await AsyncStorage.multiRemove(promotionKeys);
      }
      
      console.log('🧹 PromotionService: Cache cleared');
    } catch (error) {
      console.error('❌ PromotionService: Error clearing cache', error);
    }
  }

  /**
   * Rafraîchir les promotions (vider le cache et recharger)
   */
  async refreshPromotions(): Promise<Promotion[]> {
    await this.clearCache();
    return this.getActivePromotions();
  }

  /**
   * Vérifier si une promotion est valide et active
   */
  isPromotionValid(promotion: Promotion): boolean {
    return isPromotionActive(promotion);
  }

  /**
   * Obtenir l'URL d'image par défaut pour les promotions
   */
  getDefaultImageUrl(): string {
    return PROMOTION_CONSTANTS.DEFAULT_IMAGE;
  }

  /**
   * Formater le texte de réduction
   */
  formatDiscountText(discount?: number): string {
    if (!discount || discount === 0) return 'Offre spéciale';
    return `${discount}% de réduction`;
  }

  /**
   * Obtenir les statistiques des promotions
   */
  async getPromotionStats(): Promise<{ active: number; total: number }> {
    try {
      if (!supabase) {
        console.warn('⚠️ Supabase client not available for promotion stats');
        return { active: 0, total: 0 };
      }

      const { data, error } = await supabase
        .from('promotions')
        .select('is_active, start_date, end_date');

      if (error || !data) {
        return { active: 0, total: 0 };
      }

      const now = new Date();
      let activeCount = 0;

      data.forEach(promo => {
        if (promo.is_active) {
          const startDate = new Date(promo.start_date);
          const endDate = promo.end_date ? new Date(promo.end_date) : null;
          
          if (startDate <= now && (!endDate || endDate >= now)) {
            activeCount++;
          }
        }
      });

      return {
        active: activeCount,
        total: data.length
      };
    } catch (error) {
      console.error('❌ PromotionService: Error getting promotion stats', error);
      return { active: 0, total: 0 };
    }
  }
}

// Export de l'instance singleton
export const promotionService = PromotionService.getInstance();

// Export par défaut
export default promotionService;
