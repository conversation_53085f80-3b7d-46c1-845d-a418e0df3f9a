#!/usr/bin/env node

/**
 * Script de vérification de la configuration Google Maps
 * pour l'application Mientior Livraison
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Vérification de la configuration Google Maps API...\n');

// 1. Vérification de la clé API dans les fichiers
const checkApiKeyConsistency = () => {
  console.log('1. 📋 Vérification de la cohérence des clés API:');
  
  const files = [
    { path: '.env', pattern: /GOOGLE_MAPS_API_KEY=(.+)/ },
    { path: 'android/app/src/main/AndroidManifest.xml', pattern: /com\.google\.android\.geo\.API_KEY.*value="([^"]+)"/ },
    { path: 'src/config/environment.ts', pattern: /googleMapsApiKey:\s*'([^']+)'/ }
  ];
  
  const apiKeys = {};
  
  files.forEach(file => {
    try {
      const content = fs.readFileSync(file.path, 'utf8');
      const match = content.match(file.pattern);
      if (match) {
        apiKeys[file.path] = match[1];
        console.log(`   ✅ ${file.path}: ${match[1]}`);
      } else {
        console.log(`   ❌ ${file.path}: Clé API non trouvée`);
      }
    } catch (error) {
      console.log(`   ❌ ${file.path}: Fichier non trouvé`);
    }
  });
  
  // Vérifier la cohérence
  const uniqueKeys = [...new Set(Object.values(apiKeys))];
  if (uniqueKeys.length === 1) {
    console.log(`   ✅ Toutes les clés API sont cohérentes: ${uniqueKeys[0]}\n`);
    return uniqueKeys[0];
  } else {
    console.log(`   ❌ Incohérence détectée! ${uniqueKeys.length} clés différentes trouvées\n`);
    return null;
  }
};

// 2. Vérification du nom de package
const checkPackageName = () => {
  console.log('2. 📦 Vérification du nom de package:');
  
  try {
    const buildGradle = fs.readFileSync('android/app/build.gradle', 'utf8');
    const match = buildGradle.match(/applicationId\s+['"]([^'"]+)['"]/);
    
    if (match) {
      const packageName = match[1];
      console.log(`   ✅ Nom de package trouvé: ${packageName}\n`);
      return packageName;
    } else {
      console.log(`   ❌ Nom de package non trouvé dans build.gradle\n`);
      return null;
    }
  } catch (error) {
    console.log(`   ❌ Erreur lors de la lecture de build.gradle: ${error.message}\n`);
    return null;
  }
};

// 3. Vérification des permissions
const checkPermissions = () => {
  console.log('3. 🔐 Vérification des permissions de localisation:');
  
  try {
    const manifest = fs.readFileSync('android/app/src/main/AndroidManifest.xml', 'utf8');
    
    const permissions = [
      'ACCESS_FINE_LOCATION',
      'ACCESS_COARSE_LOCATION'
    ];
    
    permissions.forEach(permission => {
      if (manifest.includes(permission)) {
        console.log(`   ✅ Permission ${permission} trouvée`);
      } else {
        console.log(`   ❌ Permission ${permission} manquante`);
      }
    });
    
    console.log('');
  } catch (error) {
    console.log(`   ❌ Erreur lors de la lecture du AndroidManifest.xml: ${error.message}\n`);
  }
};

// 4. Vérification des dépendances
const checkDependencies = () => {
  console.log('4. 📚 Vérification des dépendances:');
  
  try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    
    const requiredDeps = [
      'react-native-maps',
      'expo-location'
    ];
    
    requiredDeps.forEach(dep => {
      if (packageJson.dependencies[dep]) {
        console.log(`   ✅ ${dep}: ${packageJson.dependencies[dep]}`);
      } else {
        console.log(`   ❌ ${dep}: Non installé`);
      }
    });
    
    console.log('');
  } catch (error) {
    console.log(`   ❌ Erreur lors de la lecture de package.json: ${error.message}\n`);
  }
};

// 5. Vérification de la configuration des cartes
const checkMapsConfig = () => {
  console.log('5. 🗺️ Vérification de la configuration des cartes:');
  
  try {
    const envContent = fs.readFileSync('src/config/environment.ts', 'utf8');
    
    if (envContent.includes('MAPS_CONFIG')) {
      console.log('   ✅ Configuration MAPS_CONFIG trouvée');
    } else {
      console.log('   ❌ Configuration MAPS_CONFIG manquante');
    }
    
    if (envContent.includes('defaultRegion')) {
      console.log('   ✅ Région par défaut configurée');
    } else {
      console.log('   ❌ Région par défaut manquante');
    }
    
    console.log('');
  } catch (error) {
    console.log(`   ❌ Erreur lors de la lecture de environment.ts: ${error.message}\n`);
  }
};

// 6. Recommandations
const showRecommendations = (apiKey, packageName) => {
  console.log('6. 💡 Recommandations pour la Google Cloud Console:');
  console.log('');
  
  if (apiKey) {
    console.log(`   🔑 Clé API à configurer: ${apiKey}`);
  }
  
  if (packageName) {
    console.log(`   📦 Nom de package à autoriser: ${packageName}`);
  }
  
  console.log('   🌍 APIs à activer:');
  console.log('      - Maps SDK for Android');
  console.log('      - Maps SDK for iOS');
  console.log('      - Places API');
  console.log('      - Directions API');
  console.log('      - Geocoding API');
  console.log('');
  
  console.log('   🔒 Configuration des restrictions:');
  console.log('      - Type: Applications Android');
  console.log(`      - Nom de package: ${packageName || 'com.livraisonafrique.mobile'}`);
  console.log('      - Empreinte SHA-1: [À générer avec keytool]');
  console.log('');
  
  console.log('   📍 Commande pour générer l\'empreinte SHA-1:');
  console.log('      keytool -list -v -keystore android/app/debug.keystore -alias androiddebugkey -storepass android -keypass android');
  console.log('');
};

// Exécution des vérifications
const main = () => {
  const apiKey = checkApiKeyConsistency();
  const packageName = checkPackageName();
  checkPermissions();
  checkDependencies();
  checkMapsConfig();
  showRecommendations(apiKey, packageName);
  
  console.log('🎯 Vérification terminée!');
  console.log('');
  console.log('📋 Actions recommandées:');
  console.log('   1. Vérifier que la clé API est active dans Google Cloud Console');
  console.log('   2. Configurer les restrictions d\'API avec le bon nom de package');
  console.log('   3. Ajouter l\'empreinte SHA-1 du certificat de débogage');
  console.log('   4. Tester l\'affichage de la carte dans l\'application');
};

main();
