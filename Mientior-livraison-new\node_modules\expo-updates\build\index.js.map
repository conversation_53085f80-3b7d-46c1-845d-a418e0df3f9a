{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": "AAAA,cAAc,WAAW,CAAC;AAC1B,cAAc,kBAAkB,CAAC;AACjC,cAAc,iBAAiB,CAAC;AAChC,cAAc,cAAc,CAAC;AAC7B,cAAc,oBAAoB,CAAC;AACnC,cAAc,2BAA2B,CAAC", "sourcesContent": ["export * from './Updates';\nexport * from './UpdatesEmitter';\nexport * from './Updates.types';\nexport * from './UseUpdates';\nexport * from './UseUpdates.types';\nexport * from './ExpoUpdatesModule.types';\n"]}