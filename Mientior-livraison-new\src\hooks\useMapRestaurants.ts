import { useState, useEffect, useCallback, useRef } from 'react';
import { restaurantService } from '../services/restaurantService';
import { MAPS_CONFIG } from '../config/environment';

export interface RestaurantMarkerData {
  id: string;
  business_name: string;
  business_type: 'restaurant' | 'colis' | 'marchandises' | 'pharmacie' | 'epicerie' | 'autres';
  rating: number;
  coordinates?: { latitude: number; longitude: number };
  distance?: number;
  duration?: number;
  routeCoordinates?: { latitude: number; longitude: number }[];
}

interface UseMapRestaurantsProps {
  currentLocation: { latitude: number; longitude: number } | null;
  filterType: string | null;
}

interface UseMapRestaurantsReturn {
  restaurants: RestaurantMarkerData[];
  loading: boolean;
  error: string | null;
  refreshRestaurants: () => Promise<void>;
  clearError: () => void;
}

export const useMapRestaurants = ({
  currentLocation,
  filterType,
}: UseMapRestaurantsProps): UseMapRestaurantsReturn => {
  const [restaurants, setRestaurants] = useState<RestaurantMarkerData[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);
  const cacheRef = useRef<Map<string, { data: RestaurantMarkerData[]; timestamp: number }>>(new Map());

  // Cache duration: 5 minutes
  const CACHE_DURATION = 5 * 60 * 1000;

  const generateCacheKey = useCallback((location: { latitude: number; longitude: number }, filter: string | null) => {
    const lat = Math.round(location.latitude * 1000) / 1000;
    const lng = Math.round(location.longitude * 1000) / 1000;
    return `${lat},${lng},${filter || 'all'}`;
  }, []);

  const isValidCache = useCallback((timestamp: number) => {
    return Date.now() - timestamp < CACHE_DURATION;
  }, []);

  const loadRestaurants = useCallback(async (forceRefresh = false) => {
    if (!currentLocation) return;

    // Annuler la requête précédente si elle existe
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    const cacheKey = generateCacheKey(currentLocation, filterType);
    const cachedData = cacheRef.current.get(cacheKey);

    // Utiliser le cache si disponible et valide (sauf si forceRefresh)
    if (!forceRefresh && cachedData && isValidCache(cachedData.timestamp)) {
      console.log('🚀 Using cached restaurants data');
      setRestaurants(cachedData.data);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Créer un nouveau AbortController pour cette requête
      abortControllerRef.current = new AbortController();

      const filters = filterType ? { business_type: filterType as any } : undefined;
      const restaurantList = await restaurantService.getRestaurants({
        ...filters,
        user_location: currentLocation,
        is_open: true,
        is_verified: true,
      });

      // Vérifier si la requête n'a pas été annulée
      if (abortControllerRef.current?.signal.aborted) {
        return;
      }

      // Ajouter les coordonnées par défaut si manquantes
      const restaurantsWithCoords = restaurantList.map(restaurant => ({
        ...restaurant,
        coordinates: restaurant.coordinates || {
          latitude: MAPS_CONFIG.defaultRegion.latitude + (Math.random() - 0.5) * 0.02,
          longitude: MAPS_CONFIG.defaultRegion.longitude + (Math.random() - 0.5) * 0.02,
        },
      }));

      // Mettre en cache les données
      cacheRef.current.set(cacheKey, {
        data: restaurantsWithCoords,
        timestamp: Date.now(),
      });

      setRestaurants(restaurantsWithCoords);
      console.log(`✅ Loaded ${restaurantsWithCoords.length} restaurants`);
    } catch (error: any) {
      if (error.name !== 'AbortError') {
        console.error('❌ Erreur chargement restaurants:', error);
        setError('Erreur lors du chargement des restaurants');
      }
    } finally {
      setLoading(false);
      abortControllerRef.current = null;
    }
  }, [currentLocation, filterType, generateCacheKey, isValidCache]);

  const refreshRestaurants = useCallback(async () => {
    await loadRestaurants(true);
  }, [loadRestaurants]);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Charger les restaurants quand la position ou le filtre change
  useEffect(() => {
    if (currentLocation) {
      loadRestaurants();
    }
  }, [currentLocation, filterType, loadRestaurants]);

  // Nettoyer l'AbortController au démontage
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  // Nettoyer le cache périodiquement
  useEffect(() => {
    const cleanupInterval = setInterval(() => {
      const now = Date.now();
      for (const [key, value] of cacheRef.current.entries()) {
        if (!isValidCache(value.timestamp)) {
          cacheRef.current.delete(key);
        }
      }
    }, CACHE_DURATION);

    return () => clearInterval(cleanupInterval);
  }, [isValidCache]);

  return {
    restaurants,
    loading,
    error,
    refreshRestaurants,
    clearError,
  };
};
