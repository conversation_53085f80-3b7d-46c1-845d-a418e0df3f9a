import AsyncStorage from '@react-native-async-storage/async-storage';
import { supabase } from './supabase';
import {
  Category,
  CategoryRaw,
  CategoriesQueryParams,
  CATEGORY_CONSTANTS,
  isCategoryActive,
  createAllCategory,
  sortCategories
} from '../types/category';

export interface CategoryFilters {
  is_active?: boolean;
  limit?: number;
}

class CategoryService {
  private static instance: CategoryService;
  private cache: Map<string, { data: Category[]; timestamp: number }> = new Map();

  static getInstance(): CategoryService {
    if (!CategoryService.instance) {
      CategoryService.instance = new CategoryService();
    }
    return CategoryService.instance;
  }

  /**
   * R<PERSON>cup<PERSON> toutes les catégories actives depuis Supabase
   */
  async getCategories(filters?: CategoryFilters): Promise<Category[]> {
    return this.getActiveCategories({
      limit: filters?.limit,
      is_active: filters?.is_active,
    });
  }

  /**
   * R<PERSON>cupérer les catégories actives depuis Supabase
   */
  async getActiveCategories(params: CategoriesQueryParams = {}): Promise<Category[]> {
    try {
      const {
        limit = CATEGORY_CONSTANTS.DEFAULT_LIMIT,
        is_active = true,
        include_inactive = false
      } = params;

      console.log('📂 CategoryService: Fetching active categories', {
        limit,
        is_active,
        include_inactive,
        current_time: new Date().toISOString()
      });

      if (!supabase) {
        console.warn('Supabase client not available for categories');
        // Retourner les catégories depuis le cache si disponible
        const cachedCategories = await this.getCachedCategories('active');
        return cachedCategories;
      }

      let query = supabase
        .from('categories')
        .select('*')
        .order('sort_order', { ascending: true })
        .order('name', { ascending: true })
        .limit(limit);

      // Filtrer par statut actif si spécifié
      if (!include_inactive) {
        query = query.eq('is_active', is_active);
      }

      const { data, error } = await query;

      if (error) {
        console.error('❌ CategoryService: Error fetching categories', error);
        throw new Error(`Erreur lors du chargement des catégories: ${error.message}`);
      }

      if (!data) {
        console.log('📭 CategoryService: No categories found');
        return [];
      }

      console.log(`📊 CategoryService: Raw data from Supabase:`, {
        count: data.length,
        categories: data.map(c => ({ id: c.id, name: c.name, is_active: c.is_active }))
      });

      const categories = data.map(this.transformCategoryData);

      // Filtrer côté client pour s'assurer que les catégories sont vraiment actives
      const activeCategories = include_inactive
        ? categories
        : categories.filter(isCategoryActive);

      // Trier les catégories
      const sortedCategories = sortCategories(activeCategories);

      console.log(`✅ CategoryService: Found ${sortedCategories.length} active categories`);

      // Mettre en cache les résultats
      await this.cacheCategories('active', sortedCategories);

      return sortedCategories;

    } catch (error) {
      console.error('❌ CategoryService: Error in getActiveCategories', error);

      // Essayer de récupérer depuis le cache en cas d'erreur
      const cachedCategories = await this.getCachedCategories('active');
      if (cachedCategories.length > 0) {
        console.log('📦 CategoryService: Returning cached categories');
        return cachedCategories;
      }

      throw error;
    }
  }

  /**
   * Récupère une catégorie par ID
   */
  async getCategoryById(categoryId: string): Promise<Category | null> {
    try {
      console.log('📂 CategoryService: Fetching category by ID', categoryId);

      // Vérifier si c'est la catégorie "Tous"
      if (categoryId === CATEGORY_CONSTANTS.ALL_CATEGORY_ID) {
        return createAllCategory();
      }

      if (!supabase) {
        console.warn('Supabase client not available for category by ID');
        return null;
      }

      const { data, error } = await supabase
        .from('categories')
        .select('*')
        .eq('id', categoryId)
        .single();

      if (error) {
        console.error('❌ CategoryService: Error fetching category by ID', error);
        return null;
      }

      if (!data) {
        console.log('📭 CategoryService: Category not found');
        return null;
      }

      return this.transformCategoryData(data);

    } catch (error) {
      console.error('❌ CategoryService: Error in getCategoryById', error);
      return null;
    }
  }

  /**
   * Récupérer les catégories avec la catégorie "Tous" incluse
   */
  async getCategoriesWithAll(params: CategoriesQueryParams = {}): Promise<Category[]> {
    const categories = await this.getActiveCategories(params);

    // Vérifier si "Tous" est déjà inclus
    const hasAllCategory = categories.some(cat => cat.name === CATEGORY_CONSTANTS.ALL_CATEGORY_NAME);

    if (!hasAllCategory) {
      // Ajouter la catégorie "Tous" au début
      const allCategory = createAllCategory();
      return [allCategory, ...categories];
    }

    return categories;
  }

  /**
   * Transformer les données brutes de Supabase en objet Category
   */
  private transformCategoryData = (raw: CategoryRaw): Category => {
    return {
      id: raw.id,
      name: raw.name,
      description: raw.description || undefined,
      icon_name: raw.icon_name || CATEGORY_CONSTANTS.DEFAULT_ICON,
      color: raw.color || CATEGORY_CONSTANTS.DEFAULT_COLOR,
      image_url: raw.image_url || undefined,
      is_active: raw.is_active,
      sort_order: raw.sort_order || 0,
      created_at: raw.created_at,
      updated_at: raw.updated_at,
    };
  };

  /**
   * Crée une nouvelle catégorie
   */
  async createCategory(categoryData: Omit<Category, 'id' | 'created_at' | 'updated_at'>): Promise<Category | null> {
    try {
      if (!supabase) {
        console.error('❌ Supabase not available');
        return null;
      }

      const { data, error } = await supabase
        .from('categories')
        .insert({
          name: categoryData.name,
          icon_name: categoryData.icon_name,
          color: categoryData.color,
          description: categoryData.description,
          image_url: categoryData.image_url,
          is_active: categoryData.is_active,
          sort_order: categoryData.sort_order,
        })
        .select()
        .single();

      if (error || !data) {
        console.error('❌ Error creating category:', error);
        return null;
      }

      const safeName = data.name || 'Catégorie';
      console.log('✅ Category created successfully:', safeName);
      return this.transformCategoryData(data);

    } catch (error) {
      console.error('❌ Error in createCategory:', error);
      return null;
    }
  }

  /**
   * Met à jour une catégorie
   */
  async updateCategory(categoryId: string, updates: Partial<Omit<Category, 'id' | 'created_at' | 'updated_at'>>): Promise<boolean> {
    try {
      if (!supabase) {
        console.error('❌ Supabase not available');
        return false;
      }

      const { error } = await supabase
        .from('categories')
        .update({
          ...updates,
          updated_at: new Date().toISOString(),
        })
        .eq('id', categoryId);

      if (error) {
        console.error('❌ Error updating category:', error);
        return false;
      }

      console.log('✅ Category updated successfully:', categoryId);
      return true;

    } catch (error) {
      console.error('❌ Error in updateCategory:', error);
      return false;
    }
  }

  /**
   * Supprime une catégorie (désactive plutôt que supprimer)
   */
  async deleteCategory(categoryId: string): Promise<boolean> {
    try {
      if (!supabase) {
        console.error('❌ Supabase not available');
        return false;
      }

      // Désactiver plutôt que supprimer
      const { error } = await supabase
        .from('categories')
        .update({
          is_active: false,
          updated_at: new Date().toISOString(),
        })
        .eq('id', categoryId);

      if (error) {
        console.error('❌ Error deleting category:', error);
        return false;
      }

      console.log('✅ Category deleted successfully:', categoryId);
      return true;

    } catch (error) {
      console.error('❌ Error in deleteCategory:', error);
      return false;
    }
  }

  /**
   * Mettre en cache les catégories dans AsyncStorage
   */
  private async cacheCategories(key: string, categories: Category[]): Promise<void> {
    try {
      const cacheData = {
        data: categories,
        timestamp: Date.now(),
      };

      await AsyncStorage.setItem(`categories_${key}`, JSON.stringify(cacheData));
      this.cache.set(key, cacheData);

      console.log(`📦 CategoryService: Cached ${categories.length} categories with key ${key}`);
    } catch (error) {
      console.error('❌ CategoryService: Error caching categories', error);
    }
  }

  /**
   * Récupérer les catégories depuis le cache
   */
  private async getCachedCategories(key: string): Promise<Category[]> {
    try {
      // Vérifier d'abord le cache en mémoire
      const memoryCache = this.cache.get(key);
      if (memoryCache && Date.now() - memoryCache.timestamp < CATEGORY_CONSTANTS.CACHE_TTL) {
        console.log(`📦 CategoryService: Returning categories from memory cache`);
        return memoryCache.data;
      }

      // Vérifier le cache AsyncStorage
      const cachedData = await AsyncStorage.getItem(`categories_${key}`);
      if (cachedData) {
        const parsed = JSON.parse(cachedData);

        // Vérifier si le cache n'est pas expiré
        if (Date.now() - parsed.timestamp < CATEGORY_CONSTANTS.CACHE_TTL) {
          console.log(`📦 CategoryService: Returning categories from AsyncStorage cache`);
          this.cache.set(key, parsed); // Remettre en cache mémoire
          return parsed.data;
        }
      }

      return [];
    } catch (error) {
      console.error('❌ CategoryService: Error getting cached categories', error);
      return [];
    }
  }

  /**
   * Vider le cache des catégories
   */
  async clearCache(): Promise<void> {
    try {
      this.cache.clear();

      // Supprimer tous les caches de catégories d'AsyncStorage
      const keys = await AsyncStorage.getAllKeys();
      const categoryKeys = keys.filter(key => key.startsWith('categories_'));

      if (categoryKeys.length > 0) {
        await AsyncStorage.multiRemove(categoryKeys);
      }

      console.log('🧹 CategoryService: Cache cleared');
    } catch (error) {
      console.error('❌ CategoryService: Error clearing cache', error);
    }
  }

  /**
   * Rafraîchir les catégories (vider le cache et recharger)
   */
  async refreshCategories(): Promise<Category[]> {
    await this.clearCache();
    return this.getActiveCategories();
  }
}

// Export de l'instance singleton
export const categoryService = CategoryService.getInstance();

// Export par défaut
export default categoryService;
