<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="expo.modules.devlauncher" >

    <uses-sdk android:minSdkVersion="24" />

    <queries>
        <package android:name="host.exp.exponent" />
    </queries>

    <application>
        <activity
            android:name="expo.modules.devlauncher.launcher.DevLauncherActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:theme="@style/Theme.DevLauncher.LauncherActivity" >
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="expo-dev-launcher" />
            </intent-filter>
        </activity>
        <activity
            android:name="expo.modules.devlauncher.launcher.errors.DevLauncherErrorActivity"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.DevLauncher.ErrorActivity" />
    </application>

</manifest>