{"logs": [{"outputFile": "com.eliseedev.mientiorlivraison.app-mergeDebugResources-71:/values-af/values-af.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ff199f796fece94789ea9bd928f0e62a\\transformed\\browser-1.6.0\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,160,262,375", "endColumns": "104,101,112,99", "endOffsets": "155,257,370,470"}, "to": {"startLines": "85,140,141,142", "startColumns": "4,4,4,4", "startOffsets": "7431,11475,11577,11690", "endColumns": "104,101,112,99", "endOffsets": "7531,11572,11685,11785"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8fb4e20b948a58ead7b50b7ad3e15312\\transformed\\appcompat-1.7.0\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,309,415,500,603,721,798,874,965,1058,1153,1247,1346,1439,1534,1633,1728,1822,1903,2010,2115,2212,2320,2423,2525,2679,2777", "endColumns": "107,95,105,84,102,117,76,75,90,92,94,93,98,92,94,98,94,93,80,106,104,96,107,102,101,153,97,80", "endOffsets": "208,304,410,495,598,716,793,869,960,1053,1148,1242,1341,1434,1529,1628,1723,1817,1898,2005,2110,2207,2315,2418,2520,2674,2772,2853"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,215", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "958,1066,1162,1268,1353,1456,1574,1651,1727,1818,1911,2006,2100,2199,2292,2387,2486,2581,2675,2756,2863,2968,3065,3173,3276,3378,3532,17357", "endColumns": "107,95,105,84,102,117,76,75,90,92,94,93,98,92,94,98,94,93,80,106,104,96,107,102,101,153,97,80", "endOffsets": "1061,1157,1263,1348,1451,1569,1646,1722,1813,1906,2001,2095,2194,2287,2382,2481,2576,2670,2751,2858,2963,3060,3168,3271,3373,3527,3625,17433"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2ab0477328e0ae05829c4cc214e49234\\transformed\\play-services-base-18.2.0\\res\\values-af\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,448,570,676,822,940,1057,1155,1317,1421,1574,1697,1832,1982,2044,2103", "endColumns": "102,151,121,105,145,117,116,97,161,103,152,122,134,149,61,58,74", "endOffsets": "295,447,569,675,821,939,1056,1154,1316,1420,1573,1696,1831,1981,2043,2102,2177"}, "to": {"startLines": "67,68,69,70,71,72,73,74,76,77,78,79,80,81,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5235,5342,5498,5624,5734,5884,6006,6127,6372,6538,6646,6803,6930,7069,7223,7289,7352", "endColumns": "106,155,125,109,149,121,120,101,165,107,156,126,138,153,65,62,78", "endOffsets": "5337,5493,5619,5729,5879,6001,6122,6224,6533,6641,6798,6925,7064,7218,7284,7347,7426"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4407803e7a2d8d3f17c090093cb28e53\\transformed\\exoplayer-ui-2.18.1\\res\\values-af\\values-af.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,286,500,689,776,864,944,1031,1117,1188,1255,1353,1446,1516,1580,1642,1711,1826,1940,2053,2125,2207,2281,2347,2434,2522,2585,2650,2703,2761,2809,2870,2935,3007,3072,3140,3198,3256,3322,3386,3452,3504,3563,3636,3709", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,86,87,79,86,85,70,66,97,92,69,63,61,68,114,113,112,71,81,73,65,86,87,62,64,52,57,47,60,64,71,64,67,57,57,65,63,65,51,58,72,72,54", "endOffsets": "281,495,684,771,859,939,1026,1112,1183,1250,1348,1441,1511,1575,1637,1706,1821,1935,2048,2120,2202,2276,2342,2429,2517,2580,2645,2698,2756,2804,2865,2930,3002,3067,3135,3193,3251,3317,3381,3447,3499,3558,3631,3704,3759"}, "to": {"startLines": "2,11,15,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,381,595,7660,7747,7835,7915,8002,8088,8159,8226,8324,8417,8487,8551,8613,8682,8797,8911,9024,9096,9178,9252,9318,9405,9493,9556,10273,10326,10384,10432,10493,10558,10630,10695,10763,10821,10879,10945,11009,11075,11127,11186,11259,11332", "endLines": "10,14,18,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "endColumns": "17,12,12,86,87,79,86,85,70,66,97,92,69,63,61,68,114,113,112,71,81,73,65,86,87,62,64,52,57,47,60,64,71,64,67,57,57,65,63,65,51,58,72,72,54", "endOffsets": "376,590,779,7742,7830,7910,7997,8083,8154,8221,8319,8412,8482,8546,8608,8677,8792,8906,9019,9091,9173,9247,9313,9400,9488,9551,9616,10321,10379,10427,10488,10553,10625,10690,10758,10816,10874,10940,11004,11070,11122,11181,11254,11327,11382"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f8296c347bb3d1ec471e3402800c1db5\\transformed\\material-1.12.0\\res\\values-af\\values-af.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,274,355,435,513,608,696,796,910,991,1051,1115,1203,1269,1332,1418,1480,1541,1599,1665,1728,1783,1901,1958,2020,2075,2144,2263,2351,2426,2519,2604,2687,2826,2909,2990,3118,3205,3282,3340,3391,3457,3526,3602,3673,3749,3823,3902,3975,4046,4149,4236,4307,4396,4486,4558,4633,4720,4771,4850,4917,4998,5082,5144,5208,5271,5341,5445,5548,5644,5744,5806,5861,5938,6021,6097", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,80,79,77,94,87,99,113,80,59,63,87,65,62,85,61,60,57,65,62,54,117,56,61,54,68,118,87,74,92,84,82,138,82,80,127,86,76,57,50,65,68,75,70,75,73,78,72,70,102,86,70,88,89,71,74,86,50,78,66,80,83,61,63,62,69,103,102,95,99,61,54,76,82,75,72", "endOffsets": "269,350,430,508,603,691,791,905,986,1046,1110,1198,1264,1327,1413,1475,1536,1594,1660,1723,1778,1896,1953,2015,2070,2139,2258,2346,2421,2514,2599,2682,2821,2904,2985,3113,3200,3277,3335,3386,3452,3521,3597,3668,3744,3818,3897,3970,4041,4144,4231,4302,4391,4481,4553,4628,4715,4766,4845,4912,4993,5077,5139,5203,5266,5336,5440,5543,5639,5739,5801,5856,5933,6016,6092,6165"}, "to": {"startLines": "19,51,52,53,54,55,63,64,65,86,87,139,144,147,149,150,151,152,153,154,155,156,157,158,159,160,161,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,216,217,218", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "784,3703,3784,3864,3942,4037,4857,4957,5071,7536,7596,11387,11862,12081,12213,12299,12361,12422,12480,12546,12609,12664,12782,12839,12901,12956,13025,13369,13457,13532,13625,13710,13793,13932,14015,14096,14224,14311,14388,14446,14497,14563,14632,14708,14779,14855,14929,15008,15081,15152,15255,15342,15413,15502,15592,15664,15739,15826,15877,15956,16023,16104,16188,16250,16314,16377,16447,16551,16654,16750,16850,16912,16967,17438,17521,17597", "endLines": "22,51,52,53,54,55,63,64,65,86,87,139,144,147,149,150,151,152,153,154,155,156,157,158,159,160,161,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,216,217,218", "endColumns": "12,80,79,77,94,87,99,113,80,59,63,87,65,62,85,61,60,57,65,62,54,117,56,61,54,68,118,87,74,92,84,82,138,82,80,127,86,76,57,50,65,68,75,70,75,73,78,72,70,102,86,70,88,89,71,74,86,50,78,66,80,83,61,63,62,69,103,102,95,99,61,54,76,82,75,72", "endOffsets": "953,3779,3859,3937,4032,4120,4952,5066,5147,7591,7655,11470,11923,12139,12294,12356,12417,12475,12541,12604,12659,12777,12834,12896,12951,13020,13139,13452,13527,13620,13705,13788,13927,14010,14091,14219,14306,14383,14441,14492,14558,14627,14703,14774,14850,14924,15003,15076,15147,15250,15337,15408,15497,15587,15659,15734,15821,15872,15951,16018,16099,16183,16245,16309,16372,16442,16546,16649,16745,16845,16907,16962,17039,17516,17592,17665"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\306a6bcd4bd045cfd61a3c5eb43578e4\\transformed\\core-1.13.1\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,353,451,558,667,787", "endColumns": "97,101,97,97,106,108,119,100", "endOffsets": "148,250,348,446,553,662,782,883"}, "to": {"startLines": "56,57,58,59,60,61,62,227", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4125,4223,4325,4423,4521,4628,4737,18292", "endColumns": "97,101,97,97,106,108,119,100", "endOffsets": "4218,4320,4418,4516,4623,4732,4852,18388"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\1b1d6e3a1eafdeb637916b2c04d0a37e\\transformed\\exoplayer-core-2.18.1\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,184,250,317,392,462,551,635", "endColumns": "68,59,65,66,74,69,88,83,71", "endOffsets": "119,179,245,312,387,457,546,630,702"}, "to": {"startLines": "112,113,114,115,116,117,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "9621,9690,9750,9816,9883,9958,10028,10117,10201", "endColumns": "68,59,65,66,74,69,88,83,71", "endOffsets": "9685,9745,9811,9878,9953,10023,10112,10196,10268"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\59ddc64970c204fac172d4da5009756f\\transformed\\react-android-0.79.2-debug\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,211,283,352,436,505,575,652,730,813,892,964,1043,1122,1196,1280,1364,1443,1513,1583,1665,1740,1816,1888", "endColumns": "72,82,71,68,83,68,69,76,77,82,78,71,78,78,73,83,83,78,69,69,81,74,75,71,73", "endOffsets": "123,206,278,347,431,500,570,647,725,808,887,959,1038,1117,1191,1275,1359,1438,1508,1578,1660,1735,1811,1883,1957"}, "to": {"startLines": "50,66,143,145,146,148,162,163,164,211,212,213,214,219,220,221,222,223,224,225,226,228,229,230,231", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3630,5152,11790,11928,11997,12144,13144,13214,13291,17044,17127,17206,17278,17670,17749,17823,17907,17991,18070,18140,18210,18393,18468,18544,18616", "endColumns": "72,82,71,68,83,68,69,76,77,82,78,71,78,78,73,83,83,78,69,69,81,74,75,71,73", "endOffsets": "3698,5230,11857,11992,12076,12208,13209,13286,13364,17122,17201,17273,17352,17744,17818,17902,17986,18065,18135,18205,18287,18463,18539,18611,18685"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cea641498eb0b29f919daa30d9671bd0\\transformed\\play-services-basement-18.3.0\\res\\values-af\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "138", "endOffsets": "333"}, "to": {"startLines": "75", "startColumns": "4", "startOffsets": "6229", "endColumns": "142", "endOffsets": "6367"}}]}]}