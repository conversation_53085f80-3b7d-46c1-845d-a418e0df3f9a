import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import { Platform } from 'react-native';
import Constants from 'expo-constants';
import { supabase, notificationService as dbNotificationService } from './supabase';

// Configuration des notifications
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: true,
    shouldShowBanner: true,
    shouldShowList: true,
  }),
});



export class NotificationService {
  private static expoPushToken: string | null = null;
  private static notificationListener: any = null;
  private static responseListener: any = null;

  static async initialize(userId: string): Promise<string | null> {
    try {
      // Vérifier si c'est un device physique
      if (!Device.isDevice) {
        console.warn('Les notifications push ne fonctionnent que sur des appareils physiques');
        return null;
      }

      // Demander les permissions
      const hasPermissions = await this.requestPermissions();
      if (!hasPermissions) {
        return null;
      }

      // Obtenir le token Expo Push
      const token = await this.getExpoPushToken();
      if (!token) {
        return null;
      }

      this.expoPushToken = token;

      // Enregistrer le token en base de données
      await this.registerPushToken(userId, token);

      // Configurer les canaux Android
      if (Platform.OS === 'android') {
        await this.setupAndroidChannels();
      }

      // Écouter les notifications
      this.setupNotificationListeners();

      console.log('Service de notifications initialisé avec succès');
      return token;

    } catch (error) {
      console.error('Erreur lors de l\'initialisation des notifications:', error);
      return null;
    }
  }

  static async requestPermissions(): Promise<boolean> {
    try {
      if (Device.isDevice) {
        const { status: existingStatus } = await Notifications.getPermissionsAsync();
        let finalStatus = existingStatus;

        if (existingStatus !== 'granted') {
          const { status } = await Notifications.requestPermissionsAsync();
          finalStatus = status;
        }

        if (finalStatus !== 'granted') {
          console.log('Permission de notifications refusée');
          return false;
        }

        return true;
      } else {
        console.log('Doit utiliser un appareil physique pour les notifications push');
        return false;
      }
    } catch (error) {
      console.error('Erreur lors de la demande de permissions:', error);
      return false;
    }
  }

  private static async registerPushToken(userId: string, token: string): Promise<void> {
    try {
      if (!supabase) {
        console.warn('⚠️ Supabase client not available for push token registration');
        return;
      }

      const platform = Platform.OS as 'ios' | 'android';

      const { error } = await supabase
        .from('push_tokens')
        .upsert({
          user_id: userId,
          token,
          platform,
          is_active: true,
          updated_at: new Date().toISOString(),
        });

      if (error) throw error;
    } catch (error) {
      console.error('Erreur lors de l\'enregistrement du token:', error);
    }
  }

  private static async setupAndroidChannels(): Promise<void> {
    // Canal par défaut
    await Notifications.setNotificationChannelAsync('default', {
      name: 'Notifications générales',
      importance: Notifications.AndroidImportance.DEFAULT,
      vibrationPattern: [0, 250, 250, 250],
      lightColor: '#FF231F7C',
    });

    // Canal pour les commandes
    await Notifications.setNotificationChannelAsync('orders', {
      name: 'Commandes',
      importance: Notifications.AndroidImportance.HIGH,
      vibrationPattern: [0, 250, 250, 250],
      sound: 'default',
    });

    // Canal pour les livraisons
    await Notifications.setNotificationChannelAsync('delivery', {
      name: 'Livraisons',
      importance: Notifications.AndroidImportance.HIGH,
      vibrationPattern: [0, 500, 250, 500],
      sound: 'default',
    });

    // Canal pour les promotions
    await Notifications.setNotificationChannelAsync('promotions', {
      name: 'Promotions',
      importance: Notifications.AndroidImportance.DEFAULT,
      vibrationPattern: [0, 250],
      sound: 'default',
    });
  }

  private static setupNotificationListeners(): void {
    // Écouter les notifications reçues
    this.notificationListener = Notifications.addNotificationReceivedListener(
      this.handleNotificationReceived.bind(this)
    );

    // Écouter les interactions avec les notifications
    this.responseListener = Notifications.addNotificationResponseReceivedListener(
      this.handleNotificationResponse.bind(this)
    );
  }

  private static handleNotificationReceived(notification: Notifications.Notification): void {
    console.log('Notification reçue:', notification);

    // Mettre à jour le badge
    const badge = notification.request.content.badge || 1;
    Notifications.setBadgeCountAsync(badge);
  }

  private static handleNotificationResponse(response: Notifications.NotificationResponse): void {
    console.log('Interaction avec notification:', response);

    const data = response.notification.request.content.data;

    // Gérer la navigation basée sur le type de notification
    if (data?.type && data?.navigationData) {
      // Ici, vous pourriez utiliser un service de navigation global
      // ou émettre un événement pour gérer la navigation
      console.log('Navigation vers:', data.navigationData);
    }
  }

  static async getExpoPushToken(): Promise<string | null> {
    try {
      const hasPermissions = await this.requestPermissions();
      if (!hasPermissions) {
        return null;
      }

      const token = (await Notifications.getExpoPushTokenAsync({
        projectId: Constants.expoConfig?.extra?.eas?.projectId,
      })).data;

      console.log('Expo Push Token:', token);
      return token;
    } catch (error) {
      console.error('Erreur lors de l\'obtention du token:', error);
      return null;
    }
  }

  static async sendPushNotification(
    userIds: string[],
    title: string,
    body: string,
    data?: any
  ): Promise<void> {
    try {
      if (!supabase) {
        console.warn('⚠️ Supabase client not available for push notifications');
        return;
      }

      // Récupérer les tokens des utilisateurs
      const { data: tokens, error } = await supabase
        .from('push_tokens')
        .select('token')
        .in('user_id', userIds)
        .eq('is_active', true);

      if (error) throw error;

      if (!tokens || tokens.length === 0) {
        console.log('Aucun token trouvé pour les utilisateurs');
        return;
      }

      // Préparer les messages
      const messages = tokens.map(tokenData => ({
        to: tokenData.token,
        sound: 'default',
        title,
        body,
        data: data || {},
      }));

      // Envoyer via l'API Expo Push Notifications
      const response = await fetch('https://exp.host/--/api/v2/push/send', {
        method: 'POST',
        headers: {
          Accept: 'application/json',
          'Accept-encoding': 'gzip, deflate',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(messages),
      });

      const result = await response.json();
      console.log('Résultat envoi push notifications:', result);

      // Enregistrer les notifications en base de données
      for (const userId of userIds) {
        await dbNotificationService.create({
          user_id: userId,
          titre: title,
          contenu: body,
          type: data?.type || 'systeme',
          data_json: data,
          is_read: false,
        });
      }

    } catch (error) {
      console.error('Erreur lors de l\'envoi de push notifications:', error);
      throw error;
    }
  }

  static async setBadgeCount(count: number): Promise<void> {
    try {
      await Notifications.setBadgeCountAsync(count);
    } catch (error) {
      console.error('Erreur lors de la mise à jour du badge:', error);
    }
  }

  static async cancelNotification(notificationId: string): Promise<void> {
    try {
      await Notifications.cancelScheduledNotificationAsync(notificationId);
    } catch (error) {
      console.error('Erreur lors de l\'annulation de notification:', error);
    }
  }

  static async cancelAllNotifications(): Promise<void> {
    try {
      await Notifications.cancelAllScheduledNotificationsAsync();
    } catch (error) {
      console.error('Erreur lors de l\'annulation de toutes les notifications:', error);
    }
  }

  static async sendLocalNotification(title: string, body: string, data?: any) {
    try {
      await Notifications.scheduleNotificationAsync({
        content: {
          title,
          body,
          data,
          sound: 'default',
        },
        trigger: null, // Envoyer immédiatement
      });
    } catch (error) {
      console.error('Erreur lors de l\'envoi de la notification locale:', error);
    }
  }

  static async scheduleNotification(
    title: string,
    body: string,
    seconds: number,
    data?: any
  ) {
    try {
      await Notifications.scheduleNotificationAsync({
        content: {
          title,
          body,
          data,
          sound: 'default',
        },
        trigger: {
          seconds
        } as Notifications.TimeIntervalTriggerInput,
      });
    } catch (error) {
      console.error('Erreur lors de la programmation de la notification:', error);
    }
  }

  // Notifications spécifiques à l'application de livraison
  static async notifyOrderUpdate(orderStatus: string, orderNumber: string) {
    const messages = {
      'confirmee': 'Votre commande a été confirmée !',
      'en_preparation': 'Votre commande est en préparation',
      'prete': 'Votre commande est prête à être récupérée',
      'en_cours_livraison': 'Votre commande est en cours de livraison',
      'livree': 'Votre commande a été livrée avec succès !',
    };

    const message = messages[orderStatus as keyof typeof messages] || 'Mise à jour de votre commande';

    await this.sendLocalNotification(
      'Commande #' + orderNumber,
      message,
      { type: 'order_update', orderNumber, status: orderStatus }
    );
  }

  static async notifyDeliveryAssignment(deliveryDetails: any) {
    await this.sendLocalNotification(
      'Nouvelle livraison assignée',
      `Récupération chez ${deliveryDetails.merchantName}`,
      { type: 'delivery_assignment', ...deliveryDetails }
    );
  }

  static async notifyNewOrder(orderDetails: any) {
    await this.sendLocalNotification(
      'Nouvelle commande reçue',
      `Commande #${orderDetails.orderNumber} - ${orderDetails.total}€`,
      { type: 'new_order', ...orderDetails }
    );
  }

  // Nouvelles méthodes pour les notifications avancées
  static async notifyOrderStatusChange(
    userId: string,
    orderId: string,
    status: string,
    restaurantName: string
  ): Promise<void> {
    const statusMessages = {
      'confirmee': `Votre commande chez ${restaurantName} a été confirmée`,
      'en_preparation': `Votre commande chez ${restaurantName} est en préparation`,
      'prete': `Votre commande chez ${restaurantName} est prête`,
      'en_livraison': `Votre commande est en cours de livraison`,
      'livree': `Votre commande a été livrée avec succès`,
      'annulee': `Votre commande chez ${restaurantName} a été annulée`,
    };

    const message = statusMessages[status as keyof typeof statusMessages] ||
                   `Mise à jour de votre commande: ${status}`;

    await this.sendPushNotification(
      [userId],
      'Mise à jour de commande',
      message,
      {
        type: 'order',
        orderId,
        status,
        navigationData: { screen: 'OrderDetails', params: { orderId } }
      }
    );
  }

  static async notifyDeliveryUpdate(
    userId: string,
    deliveryId: string,
    message: string,
    estimatedTime?: number
  ): Promise<void> {
    const body = estimatedTime
      ? `${message} (Arrivée estimée: ${estimatedTime} min)`
      : message;

    await this.sendPushNotification(
      [userId],
      'Mise à jour de livraison',
      body,
      {
        type: 'delivery',
        deliveryId,
        estimatedTime,
        navigationData: { screen: 'DeliveryTracking', params: { deliveryId } }
      }
    );
  }

  static async notifyPromotion(
    userIds: string[],
    title: string,
    description: string,
    promoCode?: string
  ): Promise<void> {
    const body = promoCode
      ? `${description} Code: ${promoCode}`
      : description;

    await this.sendPushNotification(
      userIds,
      title,
      body,
      {
        type: 'promotion',
        promoCode,
        navigationData: { screen: 'Promotions' }
      }
    );
  }

  static async notifyNewMessage(
    userId: string,
    senderName: string,
    message: string,
    deliveryId: string
  ): Promise<void> {
    await this.sendPushNotification(
      [userId],
      `Message de ${senderName}`,
      message,
      {
        type: 'chat',
        deliveryId,
        navigationData: { screen: 'Chat', params: { deliveryId } }
      }
    );
  }

  static cleanup(): void {
    if (this.notificationListener) {
      this.notificationListener.remove();
    }
    if (this.responseListener) {
      this.responseListener.remove();
    }
  }

  static getExpoPushTokenStatic(): string | null {
    return this.expoPushToken;
  }
}

// Listeners pour les notifications
export function addNotificationListeners() {
  // Notification reçue quand l'app est ouverte
  const notificationListener = Notifications.addNotificationReceivedListener(notification => {
    console.log('Notification reçue:', notification);
  });

  // Notification cliquée
  const responseListener = Notifications.addNotificationResponseReceivedListener(response => {
    console.log('Réponse à la notification:', response);
    const data = response.notification.request.content.data;
    
    // Gérer la navigation selon le type de notification
    if (data?.type === 'order_update') {
      // Naviguer vers l'écran de suivi de commande
      console.log('Naviguer vers le suivi de commande:', data.orderNumber);
    } else if (data?.type === 'delivery_assignment') {
      // Naviguer vers l'écran de livraison
      console.log('Naviguer vers la livraison assignée');
    } else if (data?.type === 'new_order') {
      // Naviguer vers l'écran de gestion des commandes
      console.log('Naviguer vers la nouvelle commande');
    }
  });

  return () => {
    notificationListener.remove();
    responseListener.remove();
  };
} 