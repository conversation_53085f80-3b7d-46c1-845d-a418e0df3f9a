{"modules": {"RNSVGSvgViewAndroid": {"type": "Component", "components": {"RNSVGSvgViewAndroid": {"excludedPlatforms": ["iOS"], "extendsProps": [{"type": "ReactNativeBuiltInType", "knownTypeName": "ReactNativeCoreViewProps"}], "events": [], "props": [{"name": "bb<PERSON><PERSON><PERSON>", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "bbHeight", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "minX", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 0}}, {"name": "minY", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 0}}, {"name": "vbWidth", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 0}}, {"name": "vbHeight", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 0}}, {"name": "align", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "meetOrSlice", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "color", "optional": true, "typeAnnotation": {"type": "ReservedPropTypeAnnotation", "name": "ColorPrimitive"}}, {"name": "pointerEvents", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "hasTVPreferredFocus", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": false}}, {"name": "borderBottomColor", "optional": true, "typeAnnotation": {"type": "ReservedPropTypeAnnotation", "name": "ColorPrimitive"}}, {"name": "nextFocusDown", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "borderRightColor", "optional": true, "typeAnnotation": {"type": "ReservedPropTypeAnnotation", "name": "ColorPrimitive"}}, {"name": "nextFocusRight", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "borderLeftColor", "optional": true, "typeAnnotation": {"type": "ReservedPropTypeAnnotation", "name": "ColorPrimitive"}}, {"name": "borderColor", "optional": true, "typeAnnotation": {"type": "ReservedPropTypeAnnotation", "name": "ColorPrimitive"}}, {"name": "removeClippedSubviews", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": false}}, {"name": "nextFocusForward", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "nextFocusUp", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "accessible", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": false}}, {"name": "borderStartColor", "optional": true, "typeAnnotation": {"type": "ReservedPropTypeAnnotation", "name": "ColorPrimitive"}}, {"name": "borderEndColor", "optional": true, "typeAnnotation": {"type": "ReservedPropTypeAnnotation", "name": "ColorPrimitive"}}, {"name": "focusable", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": false}}, {"name": "nativeBackgroundAndroid", "optional": true, "typeAnnotation": {"type": "ObjectTypeAnnotation", "properties": [{"name": "type", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "color", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 0}}, {"name": "borderless", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": false}}, {"name": "rippleRadius", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 0}}]}}, {"name": "nativeForegroundAndroid", "optional": true, "typeAnnotation": {"type": "ObjectTypeAnnotation", "properties": [{"name": "type", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "color", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 0}}, {"name": "borderless", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": false}}, {"name": "rippleRadius", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 0}}]}}, {"name": "backfaceVisibility", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "borderStyle", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "needsOffscreenAlphaCompositing", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": false}}, {"name": "hitSlop", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "borderTopColor", "optional": true, "typeAnnotation": {"type": "ReservedPropTypeAnnotation", "name": "ColorPrimitive"}}, {"name": "nextFocusLeft", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "borderBlockColor", "optional": true, "typeAnnotation": {"type": "ReservedPropTypeAnnotation", "name": "ColorPrimitive"}}, {"name": "borderBlockEndColor", "optional": true, "typeAnnotation": {"type": "ReservedPropTypeAnnotation", "name": "ColorPrimitive"}}, {"name": "borderBlockStartColor", "optional": true, "typeAnnotation": {"type": "ReservedPropTypeAnnotation", "name": "ColorPrimitive"}}, {"name": "borderRadius", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "borderTopLeftRadius", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "borderTopRightRadius", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "borderBottomRightRadius", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "borderBottomLeftRadius", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "borderTopStartRadius", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "borderTopEndRadius", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "borderBottomStartRadius", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "borderBottomEndRadius", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "borderEndEndRadius", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "borderEndStartRadius", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "borderStartEndRadius", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "borderStartStartRadius", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}], "commands": []}}}, "RNSVGCircle": {"type": "Component", "components": {"RNSVGCircle": {"interfaceOnly": true, "extendsProps": [{"type": "ReactNativeBuiltInType", "knownTypeName": "ReactNativeCoreViewProps"}], "events": [], "props": [{"name": "name", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "opacity", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 1}}, {"name": "matrix", "optional": true, "typeAnnotation": {"type": "ArrayTypeAnnotation", "elementType": {"type": "FloatTypeAnnotation"}}}, {"name": "mask", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "markerStart", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "markerMid", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "markerEnd", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "clipPath", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "clipRule", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "responsible", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": false}}, {"name": "display", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "pointerEvents", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "color", "optional": true, "typeAnnotation": {"type": "ReservedPropTypeAnnotation", "name": "ColorPrimitive"}}, {"name": "fill", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "fillOpacity", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 1}}, {"name": "fillRule", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 1}}, {"name": "stroke", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "strokeOpacity", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 1}}, {"name": "strokeWidth", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "strokeLinecap", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "strokeLinejoin", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "strokeDashoffset", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 0}}, {"name": "strokeMiterlimit", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 0}}, {"name": "vectorEffect", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "propList", "optional": true, "typeAnnotation": {"type": "ArrayTypeAnnotation", "elementType": {"type": "StringTypeAnnotation"}}}, {"name": "filter", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "cx", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "cy", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "r", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}], "commands": []}}}, "RNSVGClipPath": {"type": "Component", "components": {"RNSVGClipPath": {"interfaceOnly": true, "extendsProps": [{"type": "ReactNativeBuiltInType", "knownTypeName": "ReactNativeCoreViewProps"}], "events": [], "props": [{"name": "name", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "opacity", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 1}}, {"name": "matrix", "optional": true, "typeAnnotation": {"type": "ArrayTypeAnnotation", "elementType": {"type": "FloatTypeAnnotation"}}}, {"name": "mask", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "markerStart", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "markerMid", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "markerEnd", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "clipPath", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "clipRule", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "responsible", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": false}}, {"name": "display", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "pointerEvents", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "color", "optional": true, "typeAnnotation": {"type": "ReservedPropTypeAnnotation", "name": "ColorPrimitive"}}, {"name": "fill", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "fillOpacity", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 1}}, {"name": "fillRule", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 1}}, {"name": "stroke", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "strokeOpacity", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 1}}, {"name": "strokeWidth", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "strokeLinecap", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "strokeLinejoin", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "strokeDashoffset", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 0}}, {"name": "strokeMiterlimit", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 0}}, {"name": "vectorEffect", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "propList", "optional": true, "typeAnnotation": {"type": "ArrayTypeAnnotation", "elementType": {"type": "StringTypeAnnotation"}}}, {"name": "filter", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "fontSize", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "fontWeight", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "font", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}], "commands": []}}}, "RNSVGDefs": {"type": "Component", "components": {"RNSVGDefs": {"interfaceOnly": true, "extendsProps": [{"type": "ReactNativeBuiltInType", "knownTypeName": "ReactNativeCoreViewProps"}], "events": [], "props": [{"name": "name", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "opacity", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 1}}, {"name": "matrix", "optional": true, "typeAnnotation": {"type": "ArrayTypeAnnotation", "elementType": {"type": "FloatTypeAnnotation"}}}, {"name": "mask", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "markerStart", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "markerMid", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "markerEnd", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "clipPath", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "clipRule", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "responsible", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": false}}, {"name": "display", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "pointerEvents", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}], "commands": []}}}, "RNSVGEllipse": {"type": "Component", "components": {"RNSVGEllipse": {"interfaceOnly": true, "extendsProps": [{"type": "ReactNativeBuiltInType", "knownTypeName": "ReactNativeCoreViewProps"}], "events": [], "props": [{"name": "name", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "opacity", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 1}}, {"name": "matrix", "optional": true, "typeAnnotation": {"type": "ArrayTypeAnnotation", "elementType": {"type": "FloatTypeAnnotation"}}}, {"name": "mask", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "markerStart", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "markerMid", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "markerEnd", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "clipPath", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "clipRule", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "responsible", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": false}}, {"name": "display", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "pointerEvents", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "color", "optional": true, "typeAnnotation": {"type": "ReservedPropTypeAnnotation", "name": "ColorPrimitive"}}, {"name": "fill", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "fillOpacity", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 1}}, {"name": "fillRule", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 1}}, {"name": "stroke", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "strokeOpacity", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 1}}, {"name": "strokeWidth", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "strokeLinecap", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "strokeLinejoin", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "strokeDashoffset", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 0}}, {"name": "strokeMiterlimit", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 0}}, {"name": "vectorEffect", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "propList", "optional": true, "typeAnnotation": {"type": "ArrayTypeAnnotation", "elementType": {"type": "StringTypeAnnotation"}}}, {"name": "filter", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "cx", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "cy", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "rx", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "ry", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}], "commands": []}}}, "RNSVGFeBlend": {"type": "Component", "components": {"RNSVGFeBlend": {"interfaceOnly": true, "extendsProps": [{"type": "ReactNativeBuiltInType", "knownTypeName": "ReactNativeCoreViewProps"}], "events": [], "props": [{"name": "x", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "y", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "width", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "height", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "result", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "in1", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "in2", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "mode", "optional": true, "typeAnnotation": {"type": "StringEnumTypeAnnotation", "default": "normal", "options": ["unknown", "normal", "multiply", "screen", "darken", "lighten"]}}], "commands": []}}}, "RNSVGFeColorMatrix": {"type": "Component", "components": {"RNSVGFeColorMatrix": {"interfaceOnly": true, "extendsProps": [{"type": "ReactNativeBuiltInType", "knownTypeName": "ReactNativeCoreViewProps"}], "events": [], "props": [{"name": "x", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "y", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "width", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "height", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "result", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "in1", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "type", "optional": true, "typeAnnotation": {"type": "StringEnumTypeAnnotation", "default": "matrix", "options": ["matrix", "saturate", "hueRotate", "luminanceToAlpha"]}}, {"name": "values", "optional": true, "typeAnnotation": {"type": "ArrayTypeAnnotation", "elementType": {"type": "FloatTypeAnnotation"}}}], "commands": []}}}, "RNSVGFeComposite": {"type": "Component", "components": {"RNSVGFeComposite": {"interfaceOnly": true, "extendsProps": [{"type": "ReactNativeBuiltInType", "knownTypeName": "ReactNativeCoreViewProps"}], "events": [], "props": [{"name": "x", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "y", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "width", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "height", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "result", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "in1", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "in2", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "operator1", "optional": true, "typeAnnotation": {"type": "StringEnumTypeAnnotation", "default": "over", "options": ["over", "in", "out", "atop", "xor", "arithmetic"]}}, {"name": "k1", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 0}}, {"name": "k2", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 0}}, {"name": "k3", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 0}}, {"name": "k4", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 0}}], "commands": []}}}, "RNSVGFeFlood": {"type": "Component", "components": {"RNSVGFeFlood": {"interfaceOnly": true, "extendsProps": [{"type": "ReactNativeBuiltInType", "knownTypeName": "ReactNativeCoreViewProps"}], "events": [], "props": [{"name": "x", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "y", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "width", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "height", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "result", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "floodColor", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "floodOpacity", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 1}}], "commands": []}}}, "RNSVGFeGaussianBlur": {"type": "Component", "components": {"RNSVGFeGaussianBlur": {"interfaceOnly": true, "extendsProps": [{"type": "ReactNativeBuiltInType", "knownTypeName": "ReactNativeCoreViewProps"}], "events": [], "props": [{"name": "x", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "y", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "width", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "height", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "result", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "in1", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "stdDeviationX", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 0}}, {"name": "stdDeviationY", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 0}}, {"name": "edgeMode", "optional": true, "typeAnnotation": {"type": "StringEnumTypeAnnotation", "default": "none", "options": ["duplicate", "wrap", "none"]}}], "commands": []}}}, "RNSVGFeMerge": {"type": "Component", "components": {"RNSVGFeMerge": {"interfaceOnly": true, "extendsProps": [{"type": "ReactNativeBuiltInType", "knownTypeName": "ReactNativeCoreViewProps"}], "events": [], "props": [{"name": "x", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "y", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "width", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "height", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "result", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "nodes", "optional": true, "typeAnnotation": {"type": "ArrayTypeAnnotation", "elementType": {"type": "StringTypeAnnotation"}}}], "commands": []}}}, "RNSVGFeOffset": {"type": "Component", "components": {"RNSVGFeOffset": {"interfaceOnly": true, "extendsProps": [{"type": "ReactNativeBuiltInType", "knownTypeName": "ReactNativeCoreViewProps"}], "events": [], "props": [{"name": "x", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "y", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "width", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "height", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "result", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "in1", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "dx", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "dy", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}], "commands": []}}}, "RNSVGFilter": {"type": "Component", "components": {"RNSVGFilter": {"interfaceOnly": true, "extendsProps": [{"type": "ReactNativeBuiltInType", "knownTypeName": "ReactNativeCoreViewProps"}], "events": [], "props": [{"name": "name", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "x", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "y", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "height", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "width", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "filterUnits", "optional": true, "typeAnnotation": {"type": "StringEnumTypeAnnotation", "default": "objectBoundingBox", "options": ["userSpaceOnUse", "objectBoundingBox"]}}, {"name": "primitiveUnits", "optional": true, "typeAnnotation": {"type": "StringEnumTypeAnnotation", "default": "userSpaceOnUse", "options": ["userSpaceOnUse", "objectBoundingBox"]}}], "commands": []}}}, "RNSVGForeignObject": {"type": "Component", "components": {"RNSVGForeignObject": {"interfaceOnly": true, "extendsProps": [{"type": "ReactNativeBuiltInType", "knownTypeName": "ReactNativeCoreViewProps"}], "events": [], "props": [{"name": "name", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "opacity", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 1}}, {"name": "matrix", "optional": true, "typeAnnotation": {"type": "ArrayTypeAnnotation", "elementType": {"type": "FloatTypeAnnotation"}}}, {"name": "mask", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "markerStart", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "markerMid", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "markerEnd", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "clipPath", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "clipRule", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "responsible", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": false}}, {"name": "display", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "pointerEvents", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "color", "optional": true, "typeAnnotation": {"type": "ReservedPropTypeAnnotation", "name": "ColorPrimitive"}}, {"name": "fill", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "fillOpacity", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 1}}, {"name": "fillRule", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 1}}, {"name": "stroke", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "strokeOpacity", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 1}}, {"name": "strokeWidth", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "strokeLinecap", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "strokeLinejoin", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "strokeDashoffset", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 0}}, {"name": "strokeMiterlimit", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 0}}, {"name": "vectorEffect", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "propList", "optional": true, "typeAnnotation": {"type": "ArrayTypeAnnotation", "elementType": {"type": "StringTypeAnnotation"}}}, {"name": "filter", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "fontSize", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "fontWeight", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "font", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "x", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "y", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "height", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "width", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}], "commands": []}}}, "RNSVGGroup": {"type": "Component", "components": {"RNSVGGroup": {"interfaceOnly": true, "extendsProps": [{"type": "ReactNativeBuiltInType", "knownTypeName": "ReactNativeCoreViewProps"}], "events": [], "props": [{"name": "name", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "opacity", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 1}}, {"name": "matrix", "optional": true, "typeAnnotation": {"type": "ArrayTypeAnnotation", "elementType": {"type": "FloatTypeAnnotation"}}}, {"name": "mask", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "markerStart", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "markerMid", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "markerEnd", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "clipPath", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "clipRule", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "responsible", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": false}}, {"name": "display", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "pointerEvents", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "color", "optional": true, "typeAnnotation": {"type": "ReservedPropTypeAnnotation", "name": "ColorPrimitive"}}, {"name": "fill", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "fillOpacity", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 1}}, {"name": "fillRule", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 1}}, {"name": "stroke", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "strokeOpacity", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 1}}, {"name": "strokeWidth", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "strokeLinecap", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "strokeLinejoin", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "strokeDashoffset", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 0}}, {"name": "strokeMiterlimit", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 0}}, {"name": "vectorEffect", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "propList", "optional": true, "typeAnnotation": {"type": "ArrayTypeAnnotation", "elementType": {"type": "StringTypeAnnotation"}}}, {"name": "filter", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "fontSize", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "fontWeight", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "font", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}], "commands": []}}}, "RNSVGImage": {"type": "Component", "components": {"RNSVGImage": {"interfaceOnly": true, "extendsProps": [{"type": "ReactNativeBuiltInType", "knownTypeName": "ReactNativeCoreViewProps"}], "events": [{"name": "onLoad", "optional": true, "bubblingType": "direct", "typeAnnotation": {"type": "EventTypeAnnotation", "argument": {"type": "ObjectTypeAnnotation", "properties": [{"name": "source", "optional": false, "typeAnnotation": {"type": "ObjectTypeAnnotation", "properties": [{"name": "width", "optional": false, "typeAnnotation": {"type": "FloatTypeAnnotation"}}, {"name": "height", "optional": false, "typeAnnotation": {"type": "FloatTypeAnnotation"}}, {"name": "uri", "optional": false, "typeAnnotation": {"type": "StringTypeAnnotation"}}]}}]}}}], "props": [{"name": "name", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "opacity", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 1}}, {"name": "matrix", "optional": true, "typeAnnotation": {"type": "ArrayTypeAnnotation", "elementType": {"type": "FloatTypeAnnotation"}}}, {"name": "mask", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "markerStart", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "markerMid", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "markerEnd", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "clipPath", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "clipRule", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "responsible", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": false}}, {"name": "display", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "pointerEvents", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "color", "optional": true, "typeAnnotation": {"type": "ReservedPropTypeAnnotation", "name": "ColorPrimitive"}}, {"name": "fill", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "fillOpacity", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 1}}, {"name": "fillRule", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 1}}, {"name": "stroke", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "strokeOpacity", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 1}}, {"name": "strokeWidth", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "strokeLinecap", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "strokeLinejoin", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "strokeDashoffset", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 0}}, {"name": "strokeMiterlimit", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 0}}, {"name": "vectorEffect", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "propList", "optional": true, "typeAnnotation": {"type": "ArrayTypeAnnotation", "elementType": {"type": "StringTypeAnnotation"}}}, {"name": "filter", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "x", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "y", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "width", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "height", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "src", "optional": true, "typeAnnotation": {"type": "ReservedPropTypeAnnotation", "name": "ImageSourcePrimitive"}}, {"name": "align", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "meetOrSlice", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}], "commands": []}}}, "RNSVGSvgView": {"type": "Component", "components": {"RNSVGSvgView": {"excludedPlatforms": ["android"], "extendsProps": [{"type": "ReactNativeBuiltInType", "knownTypeName": "ReactNativeCoreViewProps"}], "events": [], "props": [{"name": "bb<PERSON><PERSON><PERSON>", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "bbHeight", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "minX", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 0}}, {"name": "minY", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 0}}, {"name": "vbWidth", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 0}}, {"name": "vbHeight", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 0}}, {"name": "align", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "meetOrSlice", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "color", "optional": true, "typeAnnotation": {"type": "ReservedPropTypeAnnotation", "name": "ColorPrimitive"}}, {"name": "pointerEvents", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "hitSlop", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}], "commands": []}}}, "RNSVGLinearGradient": {"type": "Component", "components": {"RNSVGLinearGradient": {"interfaceOnly": true, "extendsProps": [{"type": "ReactNativeBuiltInType", "knownTypeName": "ReactNativeCoreViewProps"}], "events": [], "props": [{"name": "name", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "opacity", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 1}}, {"name": "matrix", "optional": true, "typeAnnotation": {"type": "ArrayTypeAnnotation", "elementType": {"type": "FloatTypeAnnotation"}}}, {"name": "mask", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "markerStart", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "markerMid", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "markerEnd", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "clipPath", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "clipRule", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "responsible", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": false}}, {"name": "display", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "pointerEvents", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "x1", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "y1", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "x2", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "y2", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "gradient", "optional": true, "typeAnnotation": {"type": "ArrayTypeAnnotation", "elementType": {"type": "FloatTypeAnnotation"}}}, {"name": "gradientUnits", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "gradientTransform", "optional": true, "typeAnnotation": {"type": "ArrayTypeAnnotation", "elementType": {"type": "FloatTypeAnnotation"}}}], "commands": []}}}, "RNSVGLine": {"type": "Component", "components": {"RNSVGLine": {"interfaceOnly": true, "extendsProps": [{"type": "ReactNativeBuiltInType", "knownTypeName": "ReactNativeCoreViewProps"}], "events": [], "props": [{"name": "name", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "opacity", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 1}}, {"name": "matrix", "optional": true, "typeAnnotation": {"type": "ArrayTypeAnnotation", "elementType": {"type": "FloatTypeAnnotation"}}}, {"name": "mask", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "markerStart", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "markerMid", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "markerEnd", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "clipPath", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "clipRule", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "responsible", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": false}}, {"name": "display", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "pointerEvents", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "color", "optional": true, "typeAnnotation": {"type": "ReservedPropTypeAnnotation", "name": "ColorPrimitive"}}, {"name": "fill", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "fillOpacity", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 1}}, {"name": "fillRule", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 1}}, {"name": "stroke", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "strokeOpacity", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 1}}, {"name": "strokeWidth", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "strokeLinecap", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "strokeLinejoin", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "strokeDashoffset", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 0}}, {"name": "strokeMiterlimit", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 0}}, {"name": "vectorEffect", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "propList", "optional": true, "typeAnnotation": {"type": "ArrayTypeAnnotation", "elementType": {"type": "StringTypeAnnotation"}}}, {"name": "filter", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "x1", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "y1", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "x2", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "y2", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}], "commands": []}}}, "RNSVGMarker": {"type": "Component", "components": {"RNSVGMarker": {"interfaceOnly": true, "extendsProps": [{"type": "ReactNativeBuiltInType", "knownTypeName": "ReactNativeCoreViewProps"}], "events": [], "props": [{"name": "name", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "opacity", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 1}}, {"name": "matrix", "optional": true, "typeAnnotation": {"type": "ArrayTypeAnnotation", "elementType": {"type": "FloatTypeAnnotation"}}}, {"name": "mask", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "markerStart", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "markerMid", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "markerEnd", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "clipPath", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "clipRule", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "responsible", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": false}}, {"name": "display", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "pointerEvents", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "color", "optional": true, "typeAnnotation": {"type": "ReservedPropTypeAnnotation", "name": "ColorPrimitive"}}, {"name": "fill", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "fillOpacity", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 1}}, {"name": "fillRule", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 1}}, {"name": "stroke", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "strokeOpacity", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 1}}, {"name": "strokeWidth", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "strokeLinecap", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "strokeLinejoin", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "strokeDashoffset", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 0}}, {"name": "strokeMiterlimit", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 0}}, {"name": "vectorEffect", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "propList", "optional": true, "typeAnnotation": {"type": "ArrayTypeAnnotation", "elementType": {"type": "StringTypeAnnotation"}}}, {"name": "filter", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "fontSize", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "fontWeight", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "font", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "refX", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "refY", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "markerHeight", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "marker<PERSON>id<PERSON>", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "markerUnits", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "orient", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "minX", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 0}}, {"name": "minY", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 0}}, {"name": "vbWidth", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 0}}, {"name": "vbHeight", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 0}}, {"name": "align", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "meetOrSlice", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}], "commands": []}}}, "RNSVGMask": {"type": "Component", "components": {"RNSVGMask": {"interfaceOnly": true, "extendsProps": [{"type": "ReactNativeBuiltInType", "knownTypeName": "ReactNativeCoreViewProps"}], "events": [], "props": [{"name": "name", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "opacity", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 1}}, {"name": "matrix", "optional": true, "typeAnnotation": {"type": "ArrayTypeAnnotation", "elementType": {"type": "FloatTypeAnnotation"}}}, {"name": "mask", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "markerStart", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "markerMid", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "markerEnd", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "clipPath", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "clipRule", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "responsible", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": false}}, {"name": "display", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "pointerEvents", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "color", "optional": true, "typeAnnotation": {"type": "ReservedPropTypeAnnotation", "name": "ColorPrimitive"}}, {"name": "fill", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "fillOpacity", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 1}}, {"name": "fillRule", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 1}}, {"name": "stroke", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "strokeOpacity", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 1}}, {"name": "strokeWidth", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "strokeLinecap", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "strokeLinejoin", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "strokeDashoffset", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 0}}, {"name": "strokeMiterlimit", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 0}}, {"name": "vectorEffect", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "propList", "optional": true, "typeAnnotation": {"type": "ArrayTypeAnnotation", "elementType": {"type": "StringTypeAnnotation"}}}, {"name": "filter", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "fontSize", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "fontWeight", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "font", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "x", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "y", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "height", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "width", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "maskUnits", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "maskContentUnits", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "maskType", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}], "commands": []}}}, "NativeSvgRenderableModule": {"type": "NativeModule", "aliasMap": {"Matrix": {"type": "ObjectTypeAnnotation", "properties": [{"name": "a", "optional": false, "typeAnnotation": {"type": "FloatTypeAnnotation"}}, {"name": "b", "optional": false, "typeAnnotation": {"type": "FloatTypeAnnotation"}}, {"name": "c", "optional": false, "typeAnnotation": {"type": "FloatTypeAnnotation"}}, {"name": "d", "optional": false, "typeAnnotation": {"type": "FloatTypeAnnotation"}}, {"name": "e", "optional": false, "typeAnnotation": {"type": "FloatTypeAnnotation"}}, {"name": "f", "optional": false, "typeAnnotation": {"type": "FloatTypeAnnotation"}}]}, "Point": {"type": "ObjectTypeAnnotation", "properties": [{"name": "x", "optional": false, "typeAnnotation": {"type": "FloatTypeAnnotation"}}, {"name": "y", "optional": false, "typeAnnotation": {"type": "FloatTypeAnnotation"}}]}, "Rect": {"type": "ObjectTypeAnnotation", "properties": [{"name": "x", "optional": false, "typeAnnotation": {"type": "FloatTypeAnnotation"}}, {"name": "y", "optional": false, "typeAnnotation": {"type": "FloatTypeAnnotation"}}, {"name": "width", "optional": false, "typeAnnotation": {"type": "FloatTypeAnnotation"}}, {"name": "height", "optional": false, "typeAnnotation": {"type": "FloatTypeAnnotation"}}]}}, "enumMap": {}, "spec": {"eventEmitters": [], "methods": [{"name": "isPointInFill", "optional": false, "typeAnnotation": {"type": "FunctionTypeAnnotation", "returnTypeAnnotation": {"type": "BooleanTypeAnnotation"}, "params": [{"name": "tag", "optional": false, "typeAnnotation": {"type": "NullableTypeAnnotation", "typeAnnotation": {"type": "DoubleTypeAnnotation"}}}, {"name": "options", "optional": true, "typeAnnotation": {"type": "GenericObjectTypeAnnotation"}}]}}, {"name": "isPointInStroke", "optional": false, "typeAnnotation": {"type": "FunctionTypeAnnotation", "returnTypeAnnotation": {"type": "BooleanTypeAnnotation"}, "params": [{"name": "tag", "optional": false, "typeAnnotation": {"type": "NullableTypeAnnotation", "typeAnnotation": {"type": "DoubleTypeAnnotation"}}}, {"name": "options", "optional": true, "typeAnnotation": {"type": "GenericObjectTypeAnnotation"}}]}}, {"name": "getTotalLength", "optional": false, "typeAnnotation": {"type": "FunctionTypeAnnotation", "returnTypeAnnotation": {"type": "FloatTypeAnnotation"}, "params": [{"name": "tag", "optional": false, "typeAnnotation": {"type": "NullableTypeAnnotation", "typeAnnotation": {"type": "DoubleTypeAnnotation"}}}]}}, {"name": "getPointAtLength", "optional": false, "typeAnnotation": {"type": "FunctionTypeAnnotation", "returnTypeAnnotation": {"type": "TypeAliasTypeAnnotation", "name": "Point"}, "params": [{"name": "tag", "optional": false, "typeAnnotation": {"type": "NullableTypeAnnotation", "typeAnnotation": {"type": "DoubleTypeAnnotation"}}}, {"name": "options", "optional": true, "typeAnnotation": {"type": "GenericObjectTypeAnnotation"}}]}}, {"name": "getBBox", "optional": false, "typeAnnotation": {"type": "FunctionTypeAnnotation", "returnTypeAnnotation": {"type": "TypeAliasTypeAnnotation", "name": "Rect"}, "params": [{"name": "tag", "optional": false, "typeAnnotation": {"type": "NullableTypeAnnotation", "typeAnnotation": {"type": "DoubleTypeAnnotation"}}}, {"name": "options", "optional": true, "typeAnnotation": {"type": "GenericObjectTypeAnnotation"}}]}}, {"name": "getCTM", "optional": false, "typeAnnotation": {"type": "FunctionTypeAnnotation", "returnTypeAnnotation": {"type": "TypeAliasTypeAnnotation", "name": "Matrix"}, "params": [{"name": "tag", "optional": false, "typeAnnotation": {"type": "NullableTypeAnnotation", "typeAnnotation": {"type": "DoubleTypeAnnotation"}}}]}}, {"name": "getScreenCTM", "optional": false, "typeAnnotation": {"type": "FunctionTypeAnnotation", "returnTypeAnnotation": {"type": "TypeAliasTypeAnnotation", "name": "Matrix"}, "params": [{"name": "tag", "optional": false, "typeAnnotation": {"type": "NullableTypeAnnotation", "typeAnnotation": {"type": "DoubleTypeAnnotation"}}}]}}, {"name": "getRawResource", "optional": false, "typeAnnotation": {"type": "FunctionTypeAnnotation", "returnTypeAnnotation": {"type": "PromiseTypeAnnotation", "elementType": {"type": "StringTypeAnnotation"}}, "params": [{"name": "name", "optional": false, "typeAnnotation": {"type": "StringTypeAnnotation"}}]}}]}, "moduleName": "RNSVGRenderableModule"}, "NativeSvgViewModule": {"type": "NativeModule", "aliasMap": {}, "enumMap": {}, "spec": {"eventEmitters": [], "methods": [{"name": "toDataURL", "optional": false, "typeAnnotation": {"type": "FunctionTypeAnnotation", "returnTypeAnnotation": {"type": "VoidTypeAnnotation"}, "params": [{"name": "tag", "optional": false, "typeAnnotation": {"type": "NullableTypeAnnotation", "typeAnnotation": {"type": "DoubleTypeAnnotation"}}}, {"name": "options", "optional": true, "typeAnnotation": {"type": "GenericObjectTypeAnnotation"}}, {"name": "callback", "optional": true, "typeAnnotation": {"type": "FunctionTypeAnnotation", "returnTypeAnnotation": {"type": "VoidTypeAnnotation"}, "params": [{"name": "base64", "optional": false, "typeAnnotation": {"type": "StringTypeAnnotation"}}]}}]}}]}, "moduleName": "RNSVGSvgViewModule"}, "RNSVGPath": {"type": "Component", "components": {"RNSVGPath": {"interfaceOnly": true, "extendsProps": [{"type": "ReactNativeBuiltInType", "knownTypeName": "ReactNativeCoreViewProps"}], "events": [], "props": [{"name": "name", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "opacity", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 1}}, {"name": "matrix", "optional": true, "typeAnnotation": {"type": "ArrayTypeAnnotation", "elementType": {"type": "FloatTypeAnnotation"}}}, {"name": "mask", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "markerStart", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "markerMid", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "markerEnd", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "clipPath", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "clipRule", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "responsible", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": false}}, {"name": "display", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "pointerEvents", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "color", "optional": true, "typeAnnotation": {"type": "ReservedPropTypeAnnotation", "name": "ColorPrimitive"}}, {"name": "fill", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "fillOpacity", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 1}}, {"name": "fillRule", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 1}}, {"name": "stroke", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "strokeOpacity", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 1}}, {"name": "strokeWidth", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "strokeLinecap", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "strokeLinejoin", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "strokeDashoffset", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 0}}, {"name": "strokeMiterlimit", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 0}}, {"name": "vectorEffect", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "propList", "optional": true, "typeAnnotation": {"type": "ArrayTypeAnnotation", "elementType": {"type": "StringTypeAnnotation"}}}, {"name": "filter", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "d", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}], "commands": []}}}, "RNSVGPattern": {"type": "Component", "components": {"RNSVGPattern": {"interfaceOnly": true, "extendsProps": [{"type": "ReactNativeBuiltInType", "knownTypeName": "ReactNativeCoreViewProps"}], "events": [], "props": [{"name": "name", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "opacity", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 1}}, {"name": "matrix", "optional": true, "typeAnnotation": {"type": "ArrayTypeAnnotation", "elementType": {"type": "FloatTypeAnnotation"}}}, {"name": "mask", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "markerStart", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "markerMid", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "markerEnd", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "clipPath", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "clipRule", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "responsible", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": false}}, {"name": "display", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "pointerEvents", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "color", "optional": true, "typeAnnotation": {"type": "ReservedPropTypeAnnotation", "name": "ColorPrimitive"}}, {"name": "fill", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "fillOpacity", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 1}}, {"name": "fillRule", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 1}}, {"name": "stroke", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "strokeOpacity", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 1}}, {"name": "strokeWidth", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "strokeLinecap", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "strokeLinejoin", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "strokeDashoffset", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 0}}, {"name": "strokeMiterlimit", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 0}}, {"name": "vectorEffect", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "propList", "optional": true, "typeAnnotation": {"type": "ArrayTypeAnnotation", "elementType": {"type": "StringTypeAnnotation"}}}, {"name": "filter", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "fontSize", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "fontWeight", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "font", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "x", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "y", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "height", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "width", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "patternUnits", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "patternContentUnits", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "patternTransform", "optional": true, "typeAnnotation": {"type": "ArrayTypeAnnotation", "elementType": {"type": "FloatTypeAnnotation"}}}, {"name": "minX", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 0}}, {"name": "minY", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 0}}, {"name": "vbWidth", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 0}}, {"name": "vbHeight", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 0}}, {"name": "align", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "meetOrSlice", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}], "commands": []}}}, "RNSVGRadialGradient": {"type": "Component", "components": {"RNSVGRadialGradient": {"interfaceOnly": true, "extendsProps": [{"type": "ReactNativeBuiltInType", "knownTypeName": "ReactNativeCoreViewProps"}], "events": [], "props": [{"name": "name", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "opacity", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 1}}, {"name": "matrix", "optional": true, "typeAnnotation": {"type": "ArrayTypeAnnotation", "elementType": {"type": "FloatTypeAnnotation"}}}, {"name": "mask", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "markerStart", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "markerMid", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "markerEnd", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "clipPath", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "clipRule", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "responsible", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": false}}, {"name": "display", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "pointerEvents", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "fx", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "fy", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "cx", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "cy", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "rx", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "ry", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "gradient", "optional": true, "typeAnnotation": {"type": "ArrayTypeAnnotation", "elementType": {"type": "FloatTypeAnnotation"}}}, {"name": "gradientUnits", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "gradientTransform", "optional": true, "typeAnnotation": {"type": "ArrayTypeAnnotation", "elementType": {"type": "FloatTypeAnnotation"}}}], "commands": []}}}, "RNSVGRect": {"type": "Component", "components": {"RNSVGRect": {"interfaceOnly": true, "extendsProps": [{"type": "ReactNativeBuiltInType", "knownTypeName": "ReactNativeCoreViewProps"}], "events": [], "props": [{"name": "name", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "opacity", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 1}}, {"name": "matrix", "optional": true, "typeAnnotation": {"type": "ArrayTypeAnnotation", "elementType": {"type": "FloatTypeAnnotation"}}}, {"name": "mask", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "markerStart", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "markerMid", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "markerEnd", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "clipPath", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "clipRule", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "responsible", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": false}}, {"name": "display", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "pointerEvents", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "color", "optional": true, "typeAnnotation": {"type": "ReservedPropTypeAnnotation", "name": "ColorPrimitive"}}, {"name": "fill", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "fillOpacity", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 1}}, {"name": "fillRule", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 1}}, {"name": "stroke", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "strokeOpacity", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 1}}, {"name": "strokeWidth", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "strokeLinecap", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "strokeLinejoin", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "strokeDashoffset", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 0}}, {"name": "strokeMiterlimit", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 0}}, {"name": "vectorEffect", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "propList", "optional": true, "typeAnnotation": {"type": "ArrayTypeAnnotation", "elementType": {"type": "StringTypeAnnotation"}}}, {"name": "filter", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "x", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "y", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "height", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "width", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "rx", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "ry", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}], "commands": []}}}, "RNSVGSymbol": {"type": "Component", "components": {"RNSVGSymbol": {"interfaceOnly": true, "extendsProps": [{"type": "ReactNativeBuiltInType", "knownTypeName": "ReactNativeCoreViewProps"}], "events": [], "props": [{"name": "name", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "opacity", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 1}}, {"name": "matrix", "optional": true, "typeAnnotation": {"type": "ArrayTypeAnnotation", "elementType": {"type": "FloatTypeAnnotation"}}}, {"name": "mask", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "markerStart", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "markerMid", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "markerEnd", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "clipPath", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "clipRule", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "responsible", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": false}}, {"name": "display", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "pointerEvents", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "color", "optional": true, "typeAnnotation": {"type": "ReservedPropTypeAnnotation", "name": "ColorPrimitive"}}, {"name": "fill", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "fillOpacity", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 1}}, {"name": "fillRule", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 1}}, {"name": "stroke", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "strokeOpacity", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 1}}, {"name": "strokeWidth", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "strokeLinecap", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "strokeLinejoin", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "strokeDashoffset", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 0}}, {"name": "strokeMiterlimit", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 0}}, {"name": "vectorEffect", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "propList", "optional": true, "typeAnnotation": {"type": "ArrayTypeAnnotation", "elementType": {"type": "StringTypeAnnotation"}}}, {"name": "filter", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "fontSize", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "fontWeight", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "font", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "minX", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 0}}, {"name": "minY", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 0}}, {"name": "vbWidth", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 0}}, {"name": "vbHeight", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 0}}, {"name": "align", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "meetOrSlice", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}], "commands": []}}}, "RNSVGText": {"type": "Component", "components": {"RNSVGText": {"interfaceOnly": true, "extendsProps": [{"type": "ReactNativeBuiltInType", "knownTypeName": "ReactNativeCoreViewProps"}], "events": [], "props": [{"name": "name", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "opacity", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 1}}, {"name": "matrix", "optional": true, "typeAnnotation": {"type": "ArrayTypeAnnotation", "elementType": {"type": "FloatTypeAnnotation"}}}, {"name": "mask", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "markerStart", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "markerMid", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "markerEnd", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "clipPath", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "clipRule", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "responsible", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": false}}, {"name": "display", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "pointerEvents", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "color", "optional": true, "typeAnnotation": {"type": "ReservedPropTypeAnnotation", "name": "ColorPrimitive"}}, {"name": "fill", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "fillOpacity", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 1}}, {"name": "fillRule", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 1}}, {"name": "stroke", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "strokeOpacity", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 1}}, {"name": "strokeWidth", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "strokeLinecap", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "strokeLinejoin", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "strokeDashoffset", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 0}}, {"name": "strokeMiterlimit", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 0}}, {"name": "vectorEffect", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "propList", "optional": true, "typeAnnotation": {"type": "ArrayTypeAnnotation", "elementType": {"type": "StringTypeAnnotation"}}}, {"name": "filter", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "fontSize", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "fontWeight", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "font", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "dx", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "dy", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "x", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "y", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "rotate", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "inlineSize", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "textLength", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "baselineShift", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "lengthAdjust", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "alignmentBaseline", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "verticalAlign", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}], "commands": []}}}, "RNSVGTextPath": {"type": "Component", "components": {"RNSVGTextPath": {"interfaceOnly": true, "extendsProps": [{"type": "ReactNativeBuiltInType", "knownTypeName": "ReactNativeCoreViewProps"}], "events": [], "props": [{"name": "name", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "opacity", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 1}}, {"name": "matrix", "optional": true, "typeAnnotation": {"type": "ArrayTypeAnnotation", "elementType": {"type": "FloatTypeAnnotation"}}}, {"name": "mask", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "markerStart", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "markerMid", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "markerEnd", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "clipPath", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "clipRule", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "responsible", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": false}}, {"name": "display", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "pointerEvents", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "color", "optional": true, "typeAnnotation": {"type": "ReservedPropTypeAnnotation", "name": "ColorPrimitive"}}, {"name": "fill", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "fillOpacity", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 1}}, {"name": "fillRule", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 1}}, {"name": "stroke", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "strokeOpacity", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 1}}, {"name": "strokeWidth", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "strokeLinecap", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "strokeLinejoin", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "strokeDashoffset", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 0}}, {"name": "strokeMiterlimit", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 0}}, {"name": "vectorEffect", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "propList", "optional": true, "typeAnnotation": {"type": "ArrayTypeAnnotation", "elementType": {"type": "StringTypeAnnotation"}}}, {"name": "filter", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "fontSize", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "fontWeight", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "font", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "dx", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "dy", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "x", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "y", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "rotate", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "inlineSize", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "textLength", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "baselineShift", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "lengthAdjust", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "alignmentBaseline", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "verticalAlign", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "href", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "side", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "method", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "midLine", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "spacing", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "startOffset", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}], "commands": []}}}, "RNSVGTSpan": {"type": "Component", "components": {"RNSVGTSpan": {"interfaceOnly": true, "extendsProps": [{"type": "ReactNativeBuiltInType", "knownTypeName": "ReactNativeCoreViewProps"}], "events": [], "props": [{"name": "name", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "opacity", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 1}}, {"name": "matrix", "optional": true, "typeAnnotation": {"type": "ArrayTypeAnnotation", "elementType": {"type": "FloatTypeAnnotation"}}}, {"name": "mask", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "markerStart", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "markerMid", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "markerEnd", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "clipPath", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "clipRule", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "responsible", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": false}}, {"name": "display", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "pointerEvents", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "color", "optional": true, "typeAnnotation": {"type": "ReservedPropTypeAnnotation", "name": "ColorPrimitive"}}, {"name": "fill", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "fillOpacity", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 1}}, {"name": "fillRule", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 1}}, {"name": "stroke", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "strokeOpacity", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 1}}, {"name": "strokeWidth", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "strokeLinecap", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "strokeLinejoin", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "strokeDashoffset", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 0}}, {"name": "strokeMiterlimit", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 0}}, {"name": "vectorEffect", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "propList", "optional": true, "typeAnnotation": {"type": "ArrayTypeAnnotation", "elementType": {"type": "StringTypeAnnotation"}}}, {"name": "filter", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "fontSize", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "fontWeight", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "font", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "dx", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "dy", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "x", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "y", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "rotate", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "inlineSize", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "textLength", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "baselineShift", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "lengthAdjust", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "alignmentBaseline", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "verticalAlign", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "content", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}], "commands": []}}}, "RNSVGUse": {"type": "Component", "components": {"RNSVGUse": {"interfaceOnly": true, "extendsProps": [{"type": "ReactNativeBuiltInType", "knownTypeName": "ReactNativeCoreViewProps"}], "events": [], "props": [{"name": "name", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "opacity", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 1}}, {"name": "matrix", "optional": true, "typeAnnotation": {"type": "ArrayTypeAnnotation", "elementType": {"type": "FloatTypeAnnotation"}}}, {"name": "mask", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "markerStart", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "markerMid", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "markerEnd", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "clipPath", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "clipRule", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "responsible", "optional": true, "typeAnnotation": {"type": "BooleanTypeAnnotation", "default": false}}, {"name": "display", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "pointerEvents", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "color", "optional": true, "typeAnnotation": {"type": "ReservedPropTypeAnnotation", "name": "ColorPrimitive"}}, {"name": "fill", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "fillOpacity", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 1}}, {"name": "fillRule", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 1}}, {"name": "stroke", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "strokeOpacity", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 1}}, {"name": "strokeWidth", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "strokeLinecap", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "strokeLinejoin", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "strokeDashoffset", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 0}}, {"name": "strokeMiterlimit", "optional": true, "typeAnnotation": {"type": "FloatTypeAnnotation", "default": 0}}, {"name": "vectorEffect", "optional": true, "typeAnnotation": {"type": "Int32TypeAnnotation", "default": 0}}, {"name": "propList", "optional": true, "typeAnnotation": {"type": "ArrayTypeAnnotation", "elementType": {"type": "StringTypeAnnotation"}}}, {"name": "filter", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "href", "optional": true, "typeAnnotation": {"type": "StringTypeAnnotation", "default": null}}, {"name": "x", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "y", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "height", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}, {"name": "width", "optional": true, "typeAnnotation": {"type": "MixedTypeAnnotation"}}], "commands": []}}}}}