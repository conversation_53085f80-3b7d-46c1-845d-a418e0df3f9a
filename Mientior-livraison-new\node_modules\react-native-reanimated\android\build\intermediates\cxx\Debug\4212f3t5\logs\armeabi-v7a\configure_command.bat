@echo off
"C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\cmake.exe" ^
  "-HC:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\android" ^
  "-DCMAKE_SYSTEM_NAME=Android" ^
  "-DCMAKE_EXPORT_COMPILE_COMMANDS=ON" ^
  "-DCMAKE_SYSTEM_VERSION=24" ^
  "-DANDROID_PLATFORM=android-24" ^
  "-DANDROID_ABI=armeabi-v7a" ^
  "-DCMAKE_ANDROID_ARCH_ABI=armeabi-v7a" ^
  "-DANDROID_NDK=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006" ^
  "-DCMAKE_ANDROID_NDK=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006" ^
  "-DCMAKE_TOOLCHAIN_FILE=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\build\\cmake\\android.toolchain.cmake" ^
  "-DCMAKE_MAKE_PROGRAM=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe" ^
  "-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\android\\build\\intermediates\\cxx\\Debug\\4212f3t5\\obj\\armeabi-v7a" ^
  "-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\android\\build\\intermediates\\cxx\\Debug\\4212f3t5\\obj\\armeabi-v7a" ^
  "-DCMAKE_BUILD_TYPE=Debug" ^
  "-DCMAKE_FIND_ROOT_PATH=C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\4212f3t5\\prefab\\armeabi-v7a\\prefab" ^
  "-BC:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\4212f3t5\\armeabi-v7a" ^
  -GNinja ^
  "-DANDROID_STL=c++_shared" ^
  "-DREACT_NATIVE_MINOR_VERSION=79" ^
  "-DANDROID_TOOLCHAIN=clang" ^
  "-DREACT_NATIVE_DIR=C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native" ^
  "-DJS_RUNTIME=hermes" ^
  "-DJS_RUNTIME_DIR=C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native\\sdks\\hermes" ^
  "-DIS_NEW_ARCHITECTURE_ENABLED=true" ^
  "-DIS_REANIMATED_EXAMPLE_APP=false" ^
  "-DREANIMATED_VERSION=3.17.5" ^
  "-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON" ^
  "-DHERMES_ENABLE_DEBUGGER=1"
