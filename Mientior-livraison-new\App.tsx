import 'react-native-gesture-handler';
import React, { useEffect, useState } from 'react';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { NavigationContainer } from '@react-navigation/native';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { PaperProvider } from 'react-native-paper';
import { View, Text, ActivityIndicator } from 'react-native';

import AppNavigator from './src/navigation/AppNavigator';
import { QueryProvider } from './src/providers/QueryProvider';
import { colors } from './src/constants/theme';
import ReactErrorBoundary from './src/components/ReactErrorBoundary';

export default function App() {
  const [isAppReady, setIsAppReady] = useState(false);
  const [initializationError, setInitializationError] = useState<string | null>(null);

  useEffect(() => {
    // Initialisation simplifiée pour éviter les blocages
    const initializeApp = async () => {
      try {
        console.log('🚀 Starting Mientior Livraison app initialization...');

        // Délai minimal pour simuler l'initialisation
        await new Promise(resolve => setTimeout(resolve, 1500));

        console.log('✅ App initialization completed successfully');
        setIsAppReady(true);

      } catch (error) {
        console.error('❌ App initialization failed:', error);
        setInitializationError(error instanceof Error ? error.message : 'Unknown error');
        // Continue with app launch even if initialization fails
        setIsAppReady(true);
      }
    };

    initializeApp();
  }, []);

  // Écran de chargement pendant l'initialisation
  if (!isAppReady) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: '#fff' }}>
        <ActivityIndicator size="large" color="#0DCAA8" />
        <Text style={{ marginTop: 16, fontSize: 16, color: '#666' }}>
          Initialisation des services...
        </Text>
        {initializationError && (
          <Text style={{ marginTop: 8, fontSize: 12, color: '#FF6B35', textAlign: 'center', paddingHorizontal: 20 }}>
            Avertissement: {initializationError}
          </Text>
        )}
      </View>
    );
  }

  return (
    <ReactErrorBoundary>
      <GestureHandlerRootView style={{ flex: 1 }}>
        <SafeAreaProvider>
          <QueryProvider>
            <PaperProvider
              theme={{
                colors: {
                  primary: colors.primary[500],
                  secondary: colors.secondary[500],
                  surface: colors.surface.primary,
                  background: colors.background.primary,
                  error: colors.error,
                  onSurface: colors.text.primary,
                  onBackground: colors.text.primary,
                },
              }}
            >
              <NavigationContainer>
                <StatusBar style="dark" />
                <AppNavigator />
              </NavigationContainer>
            </PaperProvider>
          </QueryProvider>
        </SafeAreaProvider>
      </GestureHandlerRootView>
    </ReactErrorBoundary>
  );
}
