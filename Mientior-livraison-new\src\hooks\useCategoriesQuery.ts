import { useQuery } from '@tanstack/react-query';
import { useCallback, useEffect } from 'react';
import { categoryService } from '../services/categoryService';
import { useCategoryStore } from '../store/categoryStore';
import {
  Category,
  UseCategoriesResult,
  UseCategoriesOptions,
  CATEGORY_CONSTANTS
} from '../types/category';

/**
 * Clés de requête pour React Query
 */
export const categoryQueryKeys = {
  all: ['categories'] as const,
  active: () => [...categoryQueryKeys.all, 'active'] as const,
  withAll: () => [...categoryQueryKeys.all, 'with-all'] as const,
  byId: (id: string) => [...categoryQueryKeys.all, 'detail', id] as const,
  stats: () => [...categoryQueryKeys.all, 'stats'] as const,
};

/**
 * Hook principal pour récupérer les catégories actives
 */
export const useCategoriesQuery = (options: UseCategoriesOptions = {}): UseCategoriesResult => {
  const {
    limit = CATEGORY_CONSTANTS.DEFAULT_LIMIT,
    cache_time = CATEGORY_CONSTANTS.CACHE_TTL,
    stale_time = CATEGORY_CONSTANTS.STALE_TIME,
    enabled = true,
    include_all_category = true,
  } = options;

  // Use Zustand store for category selection state
  const {
    selectedCategory,
    selectCategory: selectCategoryStore,
    getFilteredCategories,
    filterPreferences
  } = useCategoryStore();

  // Construire la clé de requête
  const queryKey = include_all_category
    ? categoryQueryKeys.withAll()
    : categoryQueryKeys.active();

  const {
    data: categories = [],
    isLoading: loading,
    error,
    refetch,
    isRefetching,
  } = useQuery({
    queryKey,
    queryFn: async (): Promise<Category[]> => {
      console.log('📂 useCategoriesQuery: Fetching categories', { limit, include_all_category });

      if (include_all_category) {
        return categoryService.getCategoriesWithAll({ limit });
      }

      return categoryService.getActiveCategories({
        limit,
        is_active: true,
        include_inactive: false
      });
    },
    enabled,
    staleTime: stale_time,
    gcTime: cache_time, // Changed from cacheTime to gcTime
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });

  // Fonction de rafraîchissement
  const refresh = useCallback(async (): Promise<void> => {
    try {
      console.log('🔄 useCategoriesQuery: Refreshing categories');
      await refetch();
    } catch (error) {
      console.error('❌ useCategoriesQuery: Error refreshing categories', error);
      throw error;
    }
  }, [refetch]);

  // Fonction pour sélectionner une catégorie avec persistance
  const selectCategory = useCallback((category: Category): void => {
    console.log('📂 useCategoriesQuery: Category selected', category.name);
    selectCategoryStore(category);
  }, [selectCategoryStore]);

  // Apply filtering and sorting from store preferences
  const processedCategories = getFilteredCategories(categories);

  // Auto-select first category if none selected and categories are available
  useEffect(() => {
    if (!selectedCategory && processedCategories.length > 0) {
      const firstCategory = processedCategories[0];
      console.log('📂 useCategoriesQuery: Auto-selecting first category', firstCategory.name);
      selectCategory(firstCategory);
    }
  }, [selectedCategory, processedCategories, selectCategory]);

  return {
    categories: processedCategories,
    loading: loading || isRefetching,
    error: error?.message || null,
    refresh,
    selectedCategory: selectedCategory || undefined, // Convert null to undefined
    selectCategory,
    filterPreferences,
  };
};

/**
 * Hook pour récupérer une catégorie spécifique par ID
 */
export const useCategoryQuery = (id: string, enabled: boolean = true) => {
  const {
    data: category,
    isLoading: loading,
    error,
    refetch,
  } = useQuery({
    queryKey: categoryQueryKeys.byId(id),
    queryFn: () => categoryService.getCategoryById(id),
    enabled: enabled && !!id,
    staleTime: CATEGORY_CONSTANTS.STALE_TIME,
    gcTime: CATEGORY_CONSTANTS.CACHE_TTL, // Changed from cacheTime to gcTime
    retry: 1,
  });

  const refresh = useCallback(async (): Promise<void> => {
    await refetch();
  }, [refetch]);

  return {
    category,
    loading,
    error: error?.message || null,
    refresh,
  };
};

/**
 * Hook pour les actions de catégorie (tracking, analytics, etc.)
 */
export const useCategoryActions = () => {
  const trackCategoryView = useCallback((categoryId: string): void => {
    console.log('👁️ Category viewed:', categoryId);
    // Ici on pourrait ajouter du tracking analytics
  }, []);

  const trackCategorySelect = useCallback((categoryId: string): void => {
    console.log('👆 Category selected:', categoryId);
    // Ici on pourrait ajouter du tracking analytics
  }, []);

  const trackCategoryFilter = useCallback((categoryId: string, resultCount: number): void => {
    console.log('🔍 Category filtered:', categoryId, 'Results:', resultCount);
    // Ici on pourrait ajouter du tracking analytics
  }, []);

  return {
    trackCategoryView,
    trackCategorySelect,
    trackCategoryFilter,
  };
};

// Re-export des types principaux
export type {
  UseCategoriesResult,
  UseCategoriesOptions,
  Category,
} from '../types/category';
