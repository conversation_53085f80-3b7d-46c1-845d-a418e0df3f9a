/**
 * Automatically generated file. DO NOT MODIFY
 */
package expo.modules.updates;

public final class BuildConfig {
  public static final boolean DEBUG = Boolean.parseBoolean("true");
  public static final String LIBRARY_PACKAGE_NAME = "expo.modules.updates";
  public static final String BUILD_TYPE = "debug";
  // Field from default config.
  public static final boolean EX_UPDATES_ANDROID_DELAY_LOAD_APP = true;
  // Field from default config.
  public static final boolean EX_UPDATES_COPY_EMBEDDED_ASSETS = false;
  // Field from default config.
  public static final boolean EX_UPDATES_CUSTOM_INIT = false;
  // Field from default config.
  public static final boolean EX_UPDATES_NATIVE_DEBUG = false;
  // Field from default config.
  public static final boolean USE_DEV_CLIENT = true;
}
