{"version": 3, "file": "Updates.js", "sourceRoot": "", "sources": ["../src/Updates.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,mBAAmB,CAAC;AAE/C,OAAO,WAAW,MAAM,eAAe,CAAC;AAExC,OAAO,EAKL,8BAA8B,GAE/B,MAAM,iBAAiB,CAAC;AAEzB;;;;;;;;GAQG;AACH,MAAM,CAAC,MAAM,SAAS,GAAY,CAAC,CAAC,WAAW,CAAC,SAAS,CAAC;AAE1D;;;;;;GAMG;AACH,MAAM,CAAC,MAAM,QAAQ,GACnB,WAAW,CAAC,QAAQ,IAAI,OAAO,WAAW,CAAC,QAAQ,KAAK,QAAQ;IAC9D,CAAC,CAAC,WAAW,CAAC,QAAQ,CAAC,WAAW,EAAE;IACpC,CAAC,CAAC,IAAI,CAAC;AAEX;;;;GAIG;AACH,MAAM,CAAC,MAAM,OAAO,GAAkB,WAAW,CAAC,OAAO,IAAI,IAAI,CAAC;AAElE;;GAEG;AACH,MAAM,CAAC,MAAM,cAAc,GAAkB,WAAW,CAAC,cAAc,IAAI,IAAI,CAAC;AAEhF,MAAM,gCAAgC,GAGlC;IACF,MAAM,EAAE,8BAA8B,CAAC,OAAO;IAC9C,mBAAmB,EAAE,8BAA8B,CAAC,iBAAiB;IACrE,KAAK,EAAE,8BAA8B,CAAC,KAAK;IAC3C,SAAS,EAAE,8BAA8B,CAAC,SAAS;CACpD,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,MAAM,kBAAkB,GAC7B,gCAAgC,CAAC,WAAW,CAAC,kBAAkB,CAAC,IAAI,IAAI,CAAC;AAE3E,eAAe;AACf;;GAEG;AACH,MAAM,CAAC,MAAM,WAAW,GAAgB,WAAW,CAAC,WAAW,IAAI,EAAE,CAAC;AAEtE;;;;;;;;GAQG;AACH,MAAM,CAAC,MAAM,iBAAiB,GAAG,WAAW,CAAC,iBAAiB,CAAC;AAE/D;;;GAGG;AACH,MAAM,CAAC,MAAM,qBAAqB,GAAG,WAAW,CAAC,qBAAqB,CAAC;AAEvE;;GAEG;AACH,MAAM,CAAC,MAAM,cAAc,GAAG,WAAW,CAAC,cAAc,CAAC;AAEzD;;;GAGG;AACH,MAAM,CAAC,MAAM,gBAAgB,GAAY,WAAW,CAAC,gBAAgB,IAAI,KAAK,CAAC;AAE/E,eAAe;AACf;;GAEG;AACH,MAAM,CAAC,MAAM,qBAAqB,GAAY,WAAW,CAAC,qBAAqB,IAAI,KAAK,CAAC;AAEzF;;;;;;;;GAQG;AACH,MAAM,CAAC,MAAM,QAAQ,GACnB,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,QAAQ,CAAC;IAC5F,EAAE,CAAC;AAEL;;;;;GAKG;AACH,MAAM,CAAC,MAAM,SAAS,GAAgB,WAAW,CAAC,UAAU;IAC1D,CAAC,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC;IAClC,CAAC,CAAC,IAAI,CAAC;AAET;;;;GAIG;AACH,MAAM,wDAAwD,GAC5D,CAAC,CAAC,WAAW,CAAC,wDAAwD,CAAC;AAEzE;;GAEG;AACH,MAAM,oBAAoB,GACxB,OAAO,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;AAE1E,MAAM,yBAAyB,GAC7B,gIAAgI;IAChI,2CAA2C,CAAC;AAE9C;;;;;;;;;;;;;;;;;;;;;;;;;GAyBG;AACH,MAAM,CAAC,KAAK,UAAU,WAAW;IAC/B,IACE,CAAC,OAAO,IAAI,oBAAoB,CAAC;QACjC,CAAC,wDAAwD,EACzD,CAAC;QACD,MAAM,IAAI,UAAU,CAClB,sBAAsB,EACtB,8EAA8E,yBAAyB,EAAE,CAC1G,CAAC;IACJ,CAAC;IACD,MAAM,WAAW,CAAC,MAAM,EAAE,CAAC;AAC7B,CAAC;AAED;;;;;;;;;;;;;;GAcG;AACH,MAAM,CAAC,KAAK,UAAU,mBAAmB;IACvC,IACE,CAAC,OAAO,IAAI,oBAAoB,CAAC;QACjC,CAAC,wDAAwD,EACzD,CAAC;QACD,MAAM,IAAI,UAAU,CAClB,sBAAsB,EACtB,qDAAqD,yBAAyB,EAAE,CACjF,CAAC;IACJ,CAAC;IAED,MAAM,MAAM,GAAG,MAAM,WAAW,CAAC,mBAAmB,EAAE,CAAC;IACvD,IAAI,gBAAgB,IAAI,MAAM,EAAE,CAAC;QAC/B,MAAM,EAAE,cAAc,EAAE,GAAG,IAAI,EAAE,GAAG,MAAM,CAAC;QAC3C,OAAO;YACL,GAAG,IAAI;YACP,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC;SACrC,CAAC;IACJ,CAAC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;GAIG;AACH,MAAM,CAAC,KAAK,UAAU,mBAAmB;IACvC,OAAO,MAAM,WAAW,CAAC,mBAAmB,EAAE,CAAC;AACjD,CAAC;AAED;;;;;;GAMG;AACH,MAAM,CAAC,KAAK,UAAU,kBAAkB,CACtC,GAAW,EACX,KAAgC;IAEhC,OAAO,MAAM,WAAW,CAAC,kBAAkB,CAAC,GAAG,EAAE,KAAK,IAAI,IAAI,CAAC,CAAC;AAClE,CAAC;AAED;;;;;;;;GAQG;AACH,MAAM,CAAC,KAAK,UAAU,mBAAmB,CAAC,SAAiB,OAAO;IAChE,OAAO,MAAM,WAAW,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;AACvD,CAAC;AAED;;;;;;;;;;GAUG;AACH,MAAM,CAAC,KAAK,UAAU,oBAAoB;IACxC,MAAM,WAAW,CAAC,oBAAoB,EAAE,CAAC;AAC3C,CAAC;AAED;;;;;;;;;;;;;GAaG;AACH,MAAM,CAAC,KAAK,UAAU,gBAAgB;IACpC,IACE,CAAC,OAAO,IAAI,oBAAoB,CAAC;QACjC,CAAC,wDAAwD,EACzD,CAAC;QACD,MAAM,IAAI,UAAU,CAClB,sBAAsB,EACtB,iDAAiD,yBAAyB,EAAE,CAC7E,CAAC;IACJ,CAAC;IAED,MAAM,MAAM,GAAG,MAAM,WAAW,CAAC,gBAAgB,EAAE,CAAC;IACpD,IAAI,gBAAgB,IAAI,MAAM,EAAE,CAAC;QAC/B,MAAM,EAAE,cAAc,EAAE,GAAG,IAAI,EAAE,GAAG,MAAM,CAAC;QAC3C,OAAO;YACL,GAAG,IAAI;YACP,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC;SACrC,CAAC;IACJ,CAAC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;;;GAMG;AACH,MAAM,UAAU,qCAAqC,CACnD,cAAoF;IAEpF,WAAW,CAAC,qCAAqC,CAAC,cAAc,CAAC,CAAC;AACpE,CAAC", "sourcesContent": ["import { CodedError } from 'expo-modules-core';\n\nimport ExpoUpdates from './ExpoUpdates';\nimport { UpdatesCheckAutomaticallyNativeValue } from './ExpoUpdatesModule.types';\nimport {\n  LocalAssets,\n  Manifest,\n  UpdateCheckResult,\n  UpdateFetchResult,\n  UpdatesCheckAutomaticallyValue,\n  UpdatesLogEntry,\n} from './Updates.types';\n\n/**\n * Whether `expo-updates` is enabled. This may be false in a variety of cases including:\n * - enabled set to false in configuration\n * - missing or invalid URL in configuration\n * - missing runtime version or SDK version in configuration\n * - error accessing storage on device during initialization\n *\n * When false, the embedded update is loaded.\n */\nexport const isEnabled: boolean = !!ExpoUpdates.isEnabled;\n\n/**\n * The UUID that uniquely identifies the currently running update. The\n * UUID is represented in its canonical string form and will always use lowercase letters.\n * This value is `null` when running in a local development environment or any other environment where `expo-updates` is disabled.\n * @example\n * `\"xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx\"`\n */\nexport const updateId: string | null =\n  ExpoUpdates.updateId && typeof ExpoUpdates.updateId === 'string'\n    ? ExpoUpdates.updateId.toLowerCase()\n    : null;\n\n/**\n * The channel name of the current build, if configured for use with EAS Update. `null` otherwise.\n *\n * Expo Go and development builds are not set to a specific channel and can run any updates compatible with their native runtime. Therefore, this value will always be `null` when running an update on Expo Go or a development build.\n */\nexport const channel: string | null = ExpoUpdates.channel ?? null;\n\n/**\n * The runtime version of the current build.\n */\nexport const runtimeVersion: string | null = ExpoUpdates.runtimeVersion ?? null;\n\nconst _checkAutomaticallyMapNativeToJS: Record<\n  UpdatesCheckAutomaticallyNativeValue,\n  UpdatesCheckAutomaticallyValue\n> = {\n  ALWAYS: UpdatesCheckAutomaticallyValue.ON_LOAD,\n  ERROR_RECOVERY_ONLY: UpdatesCheckAutomaticallyValue.ON_ERROR_RECOVERY,\n  NEVER: UpdatesCheckAutomaticallyValue.NEVER,\n  WIFI_ONLY: UpdatesCheckAutomaticallyValue.WIFI_ONLY,\n};\n\n/**\n * Determines if and when `expo-updates` checks for and downloads updates automatically on startup.\n */\nexport const checkAutomatically: UpdatesCheckAutomaticallyValue | null =\n  _checkAutomaticallyMapNativeToJS[ExpoUpdates.checkAutomatically] ?? null;\n\n// @docsMissing\n/**\n * @hidden\n */\nexport const localAssets: LocalAssets = ExpoUpdates.localAssets ?? {};\n\n/**\n * `expo-updates` does its very best to always launch monotonically newer versions of your app so\n * you don't need to worry about backwards compatibility when you put out an update. In very rare\n * cases, it's possible that `expo-updates` may need to fall back to the update that's embedded in\n * the app binary, even after newer updates have been downloaded and run (an \"emergency launch\").\n * This boolean will be `true` if the app is launching under this fallback mechanism and `false`\n * otherwise. If you are concerned about backwards compatibility of future updates to your app, you\n * can use this constant to provide special behavior for this rare case.\n */\nexport const isEmergencyLaunch = ExpoUpdates.isEmergencyLaunch;\n\n/**\n * If `isEmergencyLaunch` is set to true, this will contain a string error message describing\n * what failed during initialization.\n */\nexport const emergencyLaunchReason = ExpoUpdates.emergencyLaunchReason;\n\n/**\n * Number of milliseconds it took to launch.\n */\nexport const launchDuration = ExpoUpdates.launchDuration;\n\n/**\n * This will be true if the currently running update is the one embedded in the build,\n * and not one downloaded from the updates server.\n */\nexport const isEmbeddedLaunch: boolean = ExpoUpdates.isEmbeddedLaunch || false;\n\n// @docsMissing\n/**\n * @hidden\n */\nexport const isUsingEmbeddedAssets: boolean = ExpoUpdates.isUsingEmbeddedAssets || false;\n\n/**\n * If `expo-updates` is enabled, this is the\n * [manifest](/versions/latest/sdk/constants/#manifest) (or\n * [classic manifest](/versions/latest/sdk/constants/#appmanifest))\n * object for the update that's currently running.\n *\n * In development mode, or any other environment in which `expo-updates` is disabled, this object is\n * empty.\n */\nexport const manifest: Partial<Manifest> =\n  (ExpoUpdates.manifestString ? JSON.parse(ExpoUpdates.manifestString) : ExpoUpdates.manifest) ??\n  {};\n\n/**\n * If `expo-updates` is enabled, this is a `Date` object representing the creation time of the update that's currently running (whether it was embedded or downloaded at runtime).\n *\n * In development mode, or any other environment in which `expo-updates` is disabled, this value is\n * null.\n */\nexport const createdAt: Date | null = ExpoUpdates.commitTime\n  ? new Date(ExpoUpdates.commitTime)\n  : null;\n\n/**\n * During non-expo development we block accessing the updates API methods on the JS side, but when developing in\n * Expo Go or a development client build, the controllers should have control over which API methods should\n * be allowed.\n */\nconst shouldDeferToNativeForAPIMethodAvailabilityInDevelopment =\n  !!ExpoUpdates.shouldDeferToNativeForAPIMethodAvailabilityInDevelopment;\n\n/**\n * Developer tool is set when a project is served by `expo start`.\n */\nconst isUsingDeveloperTool =\n  'extra' in manifest ? !!manifest.extra?.expoGo?.developer?.tool : false;\n\nconst manualUpdatesInstructions =\n  'To test usage of the expo-updates JS API in your app, make a release build with `npx expo run:ios --configuration Release` or ' +\n  '`npx expo run:android --variant Release`.';\n\n/**\n * Instructs the app to reload using the most recently downloaded version. This is useful for\n * triggering a newly downloaded update to launch without the user needing to manually restart the\n * app.\n * Unlike `Expo.reloadAppAsync()` provided by the `expo` package,\n * this function not only reloads the app but also changes the loaded JavaScript bundle to that of the most recently downloaded update.\n *\n * It is not recommended to place any meaningful logic after a call to `await\n * Updates.reloadAsync()`. This is because the promise is resolved after verifying that the app can\n * be reloaded, and immediately before posting an asynchronous task to the main thread to actually\n * reload the app. It is unsafe to make any assumptions about whether any more JS code will be\n * executed after the `Updates.reloadAsync` method call resolves, since that depends on the OS and\n * the state of the native module and main threads.\n *\n * This method cannot be used in Expo Go or development mode, and the returned promise will be rejected if you\n * try to do so. It also rejects when `expo-updates` is not enabled.\n *\n * @return A promise that fulfills right before the reload instruction is sent to the JS runtime, or\n * rejects if it cannot find a reference to the JS runtime. If the promise is rejected in production\n * mode, it most likely means you have installed the module incorrectly. Double check you've\n * followed the installation instructions. In particular, on iOS ensure that you set the `bridge`\n * property on `EXUpdatesAppController` with a pointer to the `RCTBridge` you want to reload, and on\n * Android ensure you either call `UpdatesController.initialize` with the instance of\n * `ReactApplication` you want to reload, or call `UpdatesController.setReactNativeHost` with the\n * proper instance of `ReactNativeHost`.\n */\nexport async function reloadAsync(): Promise<void> {\n  if (\n    (__DEV__ || isUsingDeveloperTool) &&\n    !shouldDeferToNativeForAPIMethodAvailabilityInDevelopment\n  ) {\n    throw new CodedError(\n      'ERR_UPDATES_DISABLED',\n      `You cannot use the Updates module in development mode in a production app. ${manualUpdatesInstructions}`\n    );\n  }\n  await ExpoUpdates.reload();\n}\n\n/**\n * Checks the server to see if a newly deployed update to your project is available. Does not\n * actually download the update. This method cannot be used in development mode, and the returned\n * promise will be rejected if you try to do so.\n *\n * Checking for an update uses a device's bandwidth and battery life like any network call.\n * Additionally, updates served by Expo may be rate limited. A good rule of thumb to check for\n * updates judiciously is to check when the user launches or foregrounds the app. Avoid polling for\n * updates in a frequent loop.\n *\n * @return A promise that fulfills with an [`UpdateCheckResult`](#updatecheckresult) object.\n *\n * The promise rejects in Expo Go or if the app is in development mode, or if there is an unexpected error or\n * timeout communicating with the server. It also rejects when `expo-updates` is not enabled.\n */\nexport async function checkForUpdateAsync(): Promise<UpdateCheckResult> {\n  if (\n    (__DEV__ || isUsingDeveloperTool) &&\n    !shouldDeferToNativeForAPIMethodAvailabilityInDevelopment\n  ) {\n    throw new CodedError(\n      'ERR_UPDATES_DISABLED',\n      `You cannot check for updates in development mode. ${manualUpdatesInstructions}`\n    );\n  }\n\n  const result = await ExpoUpdates.checkForUpdateAsync();\n  if ('manifestString' in result) {\n    const { manifestString, ...rest } = result;\n    return {\n      ...rest,\n      manifest: JSON.parse(manifestString),\n    };\n  }\n  return result;\n}\n\n/**\n * Retrieves the current extra params.\n *\n * This method cannot be used in Expo Go or development mode. It also rejects when `expo-updates` is not enabled.\n */\nexport async function getExtraParamsAsync(): Promise<Record<string, string>> {\n  return await ExpoUpdates.getExtraParamsAsync();\n}\n\n/**\n * Sets an extra param if value is non-null, otherwise unsets the param.\n * Extra params are sent as an [Expo Structured Field Value Dictionary](/technical-specs/expo-sfv-0/)\n * in the `Expo-Extra-Params` header of update requests. A compliant update server may use these params when selecting an update to serve.\n *\n * This method cannot be used in Expo Go or development mode. It also rejects when `expo-updates` is not enabled.\n */\nexport async function setExtraParamAsync(\n  key: string,\n  value: string | null | undefined\n): Promise<void> {\n  return await ExpoUpdates.setExtraParamAsync(key, value ?? null);\n}\n\n/**\n * Retrieves the most recent `expo-updates` log entries.\n *\n * @param maxAge Sets the max age of retrieved log entries in milliseconds. Default to `3600000` ms (1 hour).\n *\n * @return A promise that fulfills with an array of [`UpdatesLogEntry`](#updateslogentry) objects;\n *\n * The promise rejects if there is an unexpected error in retrieving the logs.\n */\nexport async function readLogEntriesAsync(maxAge: number = 3600000): Promise<UpdatesLogEntry[]> {\n  return await ExpoUpdates.readLogEntriesAsync(maxAge);\n}\n\n/**\n * Clears existing `expo-updates` log entries.\n *\n * > For now, this operation does nothing on the client.  Once log persistence has been\n * > implemented, this operation will actually remove existing logs.\n *\n * @return A promise that fulfills if the clear operation was successful.\n *\n * The promise rejects if there is an unexpected error in clearing the logs.\n *\n */\nexport async function clearLogEntriesAsync(): Promise<void> {\n  await ExpoUpdates.clearLogEntriesAsync();\n}\n\n/**\n * Downloads the most recently deployed update to your project from server to the device's local\n * storage. This method cannot be used in development mode, and the returned promise will be\n * rejected if you try to do so.\n *\n > **Note:** [`reloadAsync()`](#updatesreloadasync) can be called after promise resolution to\n * reload the app using the most recently downloaded version. Otherwise, the update will be applied\n * on the next app cold start.\n *\n * @return A promise that fulfills with an [`UpdateFetchResult`](#updatefetchresult) object.\n *\n * The promise rejects in Expo Go or if the app is in development mode, or if there is an unexpected error or\n * timeout communicating with the server. It also rejects when `expo-updates` is not enabled.\n */\nexport async function fetchUpdateAsync(): Promise<UpdateFetchResult> {\n  if (\n    (__DEV__ || isUsingDeveloperTool) &&\n    !shouldDeferToNativeForAPIMethodAvailabilityInDevelopment\n  ) {\n    throw new CodedError(\n      'ERR_UPDATES_DISABLED',\n      `You cannot fetch updates in development mode. ${manualUpdatesInstructions}`\n    );\n  }\n\n  const result = await ExpoUpdates.fetchUpdateAsync();\n  if ('manifestString' in result) {\n    const { manifestString, ...rest } = result;\n    return {\n      ...rest,\n      manifest: JSON.parse(manifestString),\n    };\n  }\n  return result;\n}\n\n/**\n * Overrides updates URL and reuqest headers in runtime from build time.\n * This method allows you to load specific updates from a URL that you provide.\n * Use this method at your own risk, as it may cause unexpected behavior.\n * [Learn more about use cases and limitations](https://docs.expo.dev/eas-update/override/).\n * @experimental\n */\nexport function setUpdateURLAndRequestHeadersOverride(\n  configOverride: { updateUrl: string; requestHeaders: Record<string, string> } | null\n): void {\n  ExpoUpdates.setUpdateURLAndRequestHeadersOverride(configOverride);\n}\n"]}