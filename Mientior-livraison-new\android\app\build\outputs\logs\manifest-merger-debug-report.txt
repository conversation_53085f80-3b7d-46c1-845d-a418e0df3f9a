-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:1:1-38:12
MERGED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:1:1-38:12
INJECTED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\debug\AndroidManifest.xml:1:1-7:12
MERGED from [:react-native-gesture-handler] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-gesture-handler\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-safe-area-context] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-screens] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-screens\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-webview] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-31:12
MERGED from [:react-native-edge-to-edge] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-edge-to-edge\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-async-storage_async-storage] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-maps] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-maps\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-performance] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-performance\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-15:12
MERGED from [:react-native-reanimated] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-reanimated\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-svg] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-svg\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-vector-icons] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-vector-icons\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-dev-launcher] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-32:12
MERGED from [:expo-dev-menu] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-24:12
MERGED from [:expo-dev-menu-interface] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-dev-menu-interface\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-document-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-document-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-20:12
MERGED from [:expo-image-loader] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-image-loader\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-image-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-59:12
MERGED from [:expo-modules-core] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-18:12
MERGED from [:expo-updates] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-updates\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-9:12
MERGED from [host.exp.exponent:expo.modules.av:15.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\9ba53ecbdb4837594108a38f8aaf5c2b\transformed\expo.modules.av-15.1.6\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.font:13.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\76894519d1352f370f7a165e6bb90dca\transformed\expo.modules.font-13.3.1\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.splashscreen:0.30.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\d3b099114f52f65dac6ace966b31b031\transformed\expo.modules.splashscreen-0.30.9\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\59ddc64970c204fac172d4da5009756f\transformed\react-android-0.79.2-debug\AndroidManifest.xml:2:1-24:12
MERGED from [:expo-constants] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-constants\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-dev-client] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-dev-client\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-eas-client] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-eas-client\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-file-system] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-33:12
MERGED from [:expo-manifests] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-manifests\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-json-utils] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-json-utils\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-location] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-location\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-17:12
MERGED from [:expo-structured-headers] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-structured-headers\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-updates-interface] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-updates-interface\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.application:6.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\526eed63bd79a2a9ba9b0a7ead38a0be\transformed\expo.modules.application-6.1.4\AndroidManifest.xml:2:1-7:12
MERGED from [expo.modules.asset:expo.modules.asset:11.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fbad6600b10fcc449ea50d94c23061f\transformed\expo.modules.asset-11.1.5\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.camera:16.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1c53cf14c7eb0e5643d47e3a95b72d0\transformed\expo.modules.camera-16.1.8\AndroidManifest.xml:2:1-16:12
MERGED from [host.exp.exponent:expo.modules.crypto:14.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\92ad5d01b864e43433e60378b11290f9\transformed\expo.modules.crypto-14.1.5\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.device:7.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7f6663c046472e84dadbf219d6a39ae0\transformed\expo.modules.device-7.1.4\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.haptics:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a47fc320c68687c1a718e9fbc28593c\transformed\expo.modules.haptics-14.1.4\AndroidManifest.xml:2:1-9:12
MERGED from [host.exp.exponent:expo.modules.keepawake:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\5678df72fac38fd8ea02d011c187cb4b\transformed\expo.modules.keepawake-14.1.4\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.lineargradient:14.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\317774afd492fd826f34b820def5d4bd\transformed\expo.modules.lineargradient-14.1.5\AndroidManifest.xml:2:1-7:12
MERGED from [host.exp.exponent:expo.modules.network:7.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\1660d72d87cc431f2e761d31424f03f3\transformed\expo.modules.network-7.1.5\AndroidManifest.xml:2:1-10:12
MERGED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:2:1-43:12
MERGED from [host.exp.exponent:expo.modules.sms:13.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\0531343fe10d3c1dea07e7b068516664\transformed\expo.modules.sms-13.1.4\AndroidManifest.xml:2:1-34:12
MERGED from [host.exp.exponent:expo.modules.speech:13.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\6f3f3fe67e7bb5816c905504e02db79c\transformed\expo.modules.speech-13.1.7\AndroidManifest.xml:2:1-15:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f8296c347bb3d1ec471e3402800c1db5\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8cc367fa8ce0cb8acb1d9a2937a5991c\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:2:1-38:12
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:17:1-145:12
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d1f7b5139d06634cc3f34d76f8142494\transformed\room-runtime-2.6.1\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d0a6ecdf0aff4ac27300243ee4f3450\transformed\room-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.camera:camera-mlkit-vision:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\106d4abcfa289e330f5f732bb66f1416\transformed\camera-mlkit-vision-1.4.1\AndroidManifest.xml:14:1-22:12
MERGED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\797066c703e3fc2659fba207b49a8e07\transformed\camera-extensions-1.4.1\AndroidManifest.xml:17:1-34:12
MERGED from [androidx.camera:camera-view:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e2de79c846eda1c5d16a048c6ee1e37\transformed\camera-view-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-video:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0727d4e1ef3966efe60e3bd863cea08a\transformed\camera-video-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-lifecycle:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b9a59f80a87d711a5ffaf7eb8c128bf\transformed\camera-lifecycle-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\024473314cb477a873a41df1f2dc78f6\transformed\camera-camera2-1.4.1\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\036671903f3c086cdacdad857c082bb6\transformed\camera-core-1.4.1\AndroidManifest.xml:17:1-36:12
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\380599f20dc879b965c86746b2c44cf2\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:17:1-66:12
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7a6de7bf0b1470187fab593af8c6930\transformed\firebase-installations-17.2.0\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4014643dd64b18ee06fdc929b4f60dfa\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cb6a07ae6c113830fda664a1a3f74969\transformed\firebase-common-21.0.0\AndroidManifest.xml:15:1-41:12
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5af770104b0f4c5b0cc62a5f02ee8adb\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.core:core-splashscreen:1.2.0-alpha02] C:\Users\<USER>\.gradle\caches\8.13\transforms\474386d8e7be06cc66831d67b9588d7f\transformed\core-splashscreen-1.2.0-alpha02\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\48e371bdf8a64584b3708853a06cc965\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0957acf0320ea66222e16f357b16f5c6\transformed\constraintlayout-2.0.1\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8fb4e20b948a58ead7b50b7ad3e15312\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac0dda40f5036986d8403279f04f501a\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:2:1-13:12
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d6792621c3f39dfaca06ce80f6b33f1\transformed\play-services-maps-18.2.0\AndroidManifest.xml:17:1-44:12
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\65ad962932dda2be7285ad5ff0cac28a\transformed\play-services-location-21.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7e13feca8295c54dbb8ebd825318ae5\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:2:1-23:12
MERGED from [com.google.mlkit:barcode-scanning:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\55a17c7390d823dc4ef06f880f3eb817\transformed\barcode-scanning-17.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c482101d03f7fe7ae56d665c96568830\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.mlkit:barcode-scanning-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\de3198b8b443d15a4e565b37e7e1d563\transformed\barcode-scanning-common-17.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8a12a52861f5caec9654dea7893e7e4e\transformed\vision-common-17.3.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c9f3d34fca54ef9c7958f49d384f7aba\transformed\common-18.9.0\AndroidManifest.xml:2:1-26:12
MERGED from [com.google.android.gms:play-services-base:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ab0477328e0ae05829c4cc214e49234\transformed\play-services-base-18.2.0\AndroidManifest.xml:16:1-24:12
MERGED from [androidx.fragment:fragment-ktx:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\20c743a7abee82ff00eb21bb5657d8ac\transformed\fragment-ktx-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1cb54ba7a96073311227ab0a47db11ff\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cea4a31bf1b24410556c1e17095429a9\transformed\glide-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9ce8d5174fe9c124eecfd6e35bcbacc0\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3be839182c659521a63fa617e8709656\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c0b084c6a67ddf100ea5ba3e68834f86\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e860328566f0fae28883e3e1c789e1e7\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:2:1-13:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c5c2f030b60fbad5693be9855d0dbe71\transformed\play-services-stats-17.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.mlkit:vision-interfaces:16.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ff2fbbe45984b9424aac1d4528ba5869\transformed\vision-interfaces-16.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3b856e30758c9f699ebd84b99bdd09a6\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:15:1-19:12
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\58e62418120637b707eea6f2a8aa6087\transformed\play-services-tasks-18.1.0\AndroidManifest.xml:2:1-6:12
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cea641498eb0b29f919daa30d9671bd0\transformed\play-services-basement-18.3.0\AndroidManifest.xml:16:1-26:12
MERGED from [androidx.fragment:fragment:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b87fc65a1354d1557c2c596a386f156e\transformed\fragment-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\25fc0610bb68c3c8dff3fe308edcbf91\transformed\activity-1.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\388e8c3dd256266ced8f35de3400115e\transformed\activity-ktx-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7060f5f04711df35f0554a7998cb34c0\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7d4269c44ba19209573180606829e5fb\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\61eab4c931962cd5b4f2baad1a98e42c\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\55ccd8a6d0c2317bd39cb3e23a8b5388\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.webkit:webkit:1.14.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b20eb650dfeaa7f22637fabe00fb0d4\transformed\webkit-1.14.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\be31e15b9f8850576dc84f6d337b4de2\transformed\autofill-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.facebook.fresco:animated-gif:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\074bb583a4794a74147dcaf6e6cbee2e\transformed\animated-gif-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:webpsupport:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce05230d41988a409f9516b9f3306d87\transformed\webpsupport-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:fresco:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0a1252810784b6addd419a8c9792e526\transformed\fresco-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c4bd2c8d482a029f69ffadfe68c7abed\transformed\imagepipeline-okhttp3-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:animated-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\862c7c3df9b738b05f6d46274c06617a\transformed\animated-base-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:animated-drawable:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\041b7ae1712387c8dd5960d9f372e732\transformed\animated-drawable-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:vito-options:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6096d38cdaff73aa972de6b6707813c6\transformed\vito-options-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:drawee:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c5639c82180c7bcf17e33ad9734cf384\transformed\drawee-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:nativeimagefilters:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\72eddf541678f353fd68a8469221f55e\transformed\nativeimagefilters-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9535f4ff6340b1a5af923b3ebb784298\transformed\memory-type-native-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-java:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b9b891b5905d77be1ddea8b155a076ca\transformed\memory-type-java-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c1ef3f4178e5a3aa490bc581880d7658\transformed\imagepipeline-native-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-ashmem:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f44ab799b9464a69c0034f0db541ac6e\transformed\memory-type-ashmem-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\46b46437de2beb342752c4d38d69d22a\transformed\imagepipeline-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\82e341dc7190d17e54c2ae081bc91dd4\transformed\nativeimagetranscoder-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0701e84f9b3f094cb163e59bacb96ef8\transformed\imagepipeline-base-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:urimod:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b52fa6429dc15f948ed3fadd1be29b49\transformed\urimod-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:vito-source:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a481df75262d5aafe498df81fa58feb\transformed\vito-source-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:middleware:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a54160f52c3f0e456c28aba102d8f08\transformed\middleware-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:ui-common:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\75ebfe97972548fde52f512d63d36859\transformed\ui-common-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:soloader:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1815db30808f5fa58b147cd854fcb383\transformed\soloader-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:fbcore:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0024be695326049e61c640cfd5222ec\transformed\fbcore-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.browser:browser:1.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ff199f796fece94789ea9bd928f0e62a\transformed\browser-1.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd4f01296266bad52a168d7ea8a8a811\transformed\core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd4e473968f9c57d3e4bc7ccd1383605\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\90ea4b92e43f121d19c9c04e6be6a5de\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0f316fa4aeda6489db0ee69d64bd3d7b\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c397dece186ebe2145e1da66121c87c\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\463c44255696161c6368323a9142d2ec\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e97b330b761ee3e761e320dde3414d40\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93defa7eb94a1f1ee72c0ca0f587e7d5\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\003636cc0a8fb63f6e38b6fa18505c26\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.exoplayer:exoplayer:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\73689df39770fdcd7616a1cd6340e5ee\transformed\exoplayer-2.18.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-dash:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\970b4431286e20e92ccd905d4f4ff19b\transformed\exoplayer-dash-2.18.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-hls:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\143c46925aa593820271ea2c3ac9f07d\transformed\exoplayer-hls-2.18.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-rtsp:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\147323c9757f936bf50f92805f9d17ff\transformed\exoplayer-rtsp-2.18.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-smoothstreaming:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\438ac7e1fb74bb6aff04c26dd77f57d2\transformed\exoplayer-smoothstreaming-2.18.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-core:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1b1d6e3a1eafdeb637916b2c04d0a37e\transformed\exoplayer-core-2.18.1\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a13766dc6d0438e6cff1b3d0ad48ab1\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4279b3c49ba46da5202fb0b313827593\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8236f30b9ad1cf2a84fab1fcd4549768\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.exoplayer:exoplayer-ui:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4407803e7a2d8d3f17c090093cb28e53\transformed\exoplayer-ui-2.18.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b473aa377bb69d3a5a331b37a35a6c4\transformed\recyclerview-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\07a66c49e5f9dc73d772c18c9aa0278c\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f25ac7b0e07eb0ad51ef05ac5c4cfb00\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.media:media:1.4.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\807c1c9c9def292ba3b209c6e060146f\transformed\media-1.4.3\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\306a6bcd4bd045cfd61a3c5eb43578e4\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\905d82f854a796b225cd9d8a92d03bfe\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7aa886086c77d026b1a2e723b8c7a258\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-service:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b7be05393bec07f8e724f73a4c2c414\transformed\lifecycle-service-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a7c0c4c7b6f361441770bb006a5736d\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\5c6a9353de243fb8621eac0d0cef3eb2\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c6fccd578e6c1f899999a9b5e08a6662\transformed\lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\5f09fbd5cc8fa956dbfe02ed0b475cce\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce3ca40a8dd11cd0ee824c7cf6469991\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b257d7638de1f9c517230d70a67d45ed\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\41b496c921314be4bab74a3a36df7b14\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc20679afcfa59d2910a7f46a536b789\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.exoplayer:extension-okhttp:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\76c4a14a45ce3d22cfe77b307c62e00f\transformed\extension-okhttp-2.18.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\99bcfde82ee67925aa78857cee59febf\transformed\profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2fa4ab394b458d75a6f846a795c898cf\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00bcef2e622b104f18727db070d6169d\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42111d6a7e8e3843f3a83cea99a9af0f\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.facebook.fresco:ui-core:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\baba08885ee5adeb5c521e1907c1be27\transformed\ui-core-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a30b7026cb4b6a42d96b5350a586e1f9\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\767f83551e2e5bb29c2ebda563cad74e\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd04975a740a9fae43a5dd659946ffdf\transformed\sqlite-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.facebook.react:hermes-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d74530985233716120af53cf728703af\transformed\hermes-android-0.79.2-debug\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.databinding:viewbinding:8.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fdc902cc195ef0e05974ea40670694b\transformed\viewbinding-8.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\40dccc500a1fbb51c39df29d566338e9\transformed\exifinterface-1.3.7\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0b50548968ff1170f1dad50aeb1f984c\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3cd9a9c3a591095815201ef436d7e312\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c2e40e29d7aa6530eca75dae92115591\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2059d2ba7c423113ccb55ccbed61837e\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6722f804269a01b830a058251481e90a\transformed\gifdecoder-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\18584f70131a7e8a2675c0a09621b404\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.exoplayer:exoplayer-datasource:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\832c8697706c97d037abb41fbcc978c9\transformed\exoplayer-datasource-2.18.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-database:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6b464e8453e92d11587730488ac2e6f\transformed\exoplayer-database-2.18.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-extractor:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf7f9057072a4e54731b2037e5a36941\transformed\exoplayer-extractor-2.18.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-decoder:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\90ec43178bace097fd5118ad07eff9b7\transformed\exoplayer-decoder-2.18.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.exoplayer:exoplayer-common:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\517add4539723efc219e05c2f11e7214\transformed\exoplayer-common-2.18.1\AndroidManifest.xml:17:1-26:12
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f15f2e8d8ce89352f13363c0e26a758f\transformed\firebase-components-18.0.0\AndroidManifest.xml:15:1-20:12
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4d8cc4ca0e35a89a493aab958b2c0a2c\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:15:1-31:12
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\2dacf4110cea599a348e4be8992dc6ba\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:15:1-37:12
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e24e380aebdbe8a0ea2a84ed997efd74\transformed\firebase-encoders-json-18.0.0\AndroidManifest.xml:15:1-23:12
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\1513070d6931c9d145a3a48af88f4f0b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ed1b76198b0839600988218b12fbb54d\transformed\transport-api-3.1.0\AndroidManifest.xml:15:1-20:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\60c896b32d4b1dcf45dec97c7e45616d\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b3534eeb4edab1b173bc8cbb06465876\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bec391801e1a359fc391013776935fec\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.facebook.fresco:vito-renderer:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8cc9f30128ebd284f23d542534d950f6\transformed\vito-renderer-3.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fbjni:fbjni:0.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e30aad97b36547574040f15ffce4d4f\transformed\fbjni-0.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6569cc9cff3589220010bb18ee2ef6ac\transformed\soloader-0.12.1\AndroidManifest.xml:2:1-17:12
MERGED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\514d135c1e94e8854cd72d6563419da6\transformed\installreferrer-2.2\AndroidManifest.xml:2:1-13:12
MERGED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\6329ce871df5828ee152a72c53b9164a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:2:1-52:12
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.13\transforms\383c3dee9ae5210dacddee017837237e\transformed\image-1.0.0-beta1\AndroidManifest.xml:2:1-9:12
	package
		INJECTED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\debug\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\debug\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:1:70-116
	android:versionCode
		INJECTED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\debug\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:1:11-69
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:2:3-78
MERGED from [:expo-location] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-location\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-81
MERGED from [:expo-location] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-location\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-81
	android:name
		ADDED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:2:20-76
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:3:3-76
MERGED from [:expo-location] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-location\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-79
MERGED from [:expo-location] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-location\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-79
	android:name
		ADDED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:3:20-74
uses-permission#android.permission.CAMERA
ADDED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:4:3-62
MERGED from [:expo-image-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-65
MERGED from [:expo-image-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-65
MERGED from [host.exp.exponent:expo.modules.camera:16.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1c53cf14c7eb0e5643d47e3a95b72d0\transformed\expo.modules.camera-16.1.8\AndroidManifest.xml:7:5-65
MERGED from [host.exp.exponent:expo.modules.camera:16.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1c53cf14c7eb0e5643d47e3a95b72d0\transformed\expo.modules.camera-16.1.8\AndroidManifest.xml:7:5-65
	android:name
		ADDED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:4:20-60
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:5:3-64
MERGED from [:expo-file-system] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-67
MERGED from [:expo-file-system] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7a6de7bf0b1470187fab593af8c6930\transformed\firebase-installations-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7a6de7bf0b1470187fab593af8c6930\transformed\firebase-installations-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d6792621c3f39dfaca06ce80f6b33f1\transformed\play-services-maps-18.2.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d6792621c3f39dfaca06ce80f6b33f1\transformed\play-services-maps-18.2.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e860328566f0fae28883e3e1c789e1e7\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e860328566f0fae28883e3e1c789e1e7\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\2dacf4110cea599a348e4be8992dc6ba\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:25:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\2dacf4110cea599a348e4be8992dc6ba\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:25:5-67
	android:name
		ADDED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:5:20-62
uses-permission#android.permission.MODIFY_AUDIO_SETTINGS
ADDED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:6:3-77
	android:name
		ADDED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:6:20-75
uses-permission#android.permission.NOTIFICATIONS
ADDED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:7:3-69
	android:name
		ADDED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:7:20-67
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:8:3-77
MERGED from [:expo-image-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:5-80
MERGED from [:expo-image-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:5-80
MERGED from [:expo-file-system] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:5-80
MERGED from [:expo-file-system] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:5-80
	android:name
		ADDED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:8:20-75
uses-permission#android.permission.RECORD_AUDIO
ADDED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:9:3-68
MERGED from [host.exp.exponent:expo.modules.camera:16.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1c53cf14c7eb0e5643d47e3a95b72d0\transformed\expo.modules.camera-16.1.8\AndroidManifest.xml:8:5-71
MERGED from [host.exp.exponent:expo.modules.camera:16.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1c53cf14c7eb0e5643d47e3a95b72d0\transformed\expo.modules.camera-16.1.8\AndroidManifest.xml:8:5-71
	android:name
		ADDED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:9:20-66
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:10:3-75
MERGED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:10:3-75
MERGED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:10:3-75
MERGED from [com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\59ddc64970c204fac172d4da5009756f\transformed\react-android-0.79.2-debug\AndroidManifest.xml:16:5-78
MERGED from [com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\59ddc64970c204fac172d4da5009756f\transformed\react-android-0.79.2-debug\AndroidManifest.xml:16:5-78
	android:name
		ADDED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:10:20-73
uses-permission#android.permission.VIBRATE
ADDED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:11:3-63
MERGED from [host.exp.exponent:expo.modules.haptics:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a47fc320c68687c1a718e9fbc28593c\transformed\expo.modules.haptics-14.1.4\AndroidManifest.xml:7:5-66
MERGED from [host.exp.exponent:expo.modules.haptics:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a47fc320c68687c1a718e9fbc28593c\transformed\expo.modules.haptics-14.1.4\AndroidManifest.xml:7:5-66
	android:name
		ADDED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:11:20-61
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:12:3-78
MERGED from [:expo-image-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-81
MERGED from [:expo-image-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-81
MERGED from [:expo-file-system] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-81
MERGED from [:expo-file-system] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-81
	android:name
		ADDED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:12:20-76
queries
ADDED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:13:3-19:13
MERGED from [:react-native-webview] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-17:15
MERGED from [:react-native-webview] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-17:15
MERGED from [:expo-dev-launcher] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-9:15
MERGED from [:expo-dev-launcher] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-9:15
MERGED from [:expo-document-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-document-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-18:15
MERGED from [:expo-document-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-document-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-18:15
MERGED from [:expo-image-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:5-27:15
MERGED from [:expo-image-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:5-27:15
MERGED from [:expo-file-system] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:5-18:15
MERGED from [:expo-file-system] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:5-18:15
MERGED from [host.exp.exponent:expo.modules.sms:13.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\0531343fe10d3c1dea07e7b068516664\transformed\expo.modules.sms-13.1.4\AndroidManifest.xml:7:5-32:15
MERGED from [host.exp.exponent:expo.modules.sms:13.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\0531343fe10d3c1dea07e7b068516664\transformed\expo.modules.sms-13.1.4\AndroidManifest.xml:7:5-32:15
MERGED from [host.exp.exponent:expo.modules.speech:13.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\6f3f3fe67e7bb5816c905504e02db79c\transformed\expo.modules.speech-13.1.7\AndroidManifest.xml:7:5-13:15
MERGED from [host.exp.exponent:expo.modules.speech:13.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\6f3f3fe67e7bb5816c905504e02db79c\transformed\expo.modules.speech-13.1.7\AndroidManifest.xml:7:5-13:15
MERGED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8cc367fa8ce0cb8acb1d9a2937a5991c\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:9:5-20:15
MERGED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8cc367fa8ce0cb8acb1d9a2937a5991c\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:9:5-20:15
MERGED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\797066c703e3fc2659fba207b49a8e07\transformed\camera-extensions-1.4.1\AndroidManifest.xml:22:5-26:15
MERGED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\797066c703e3fc2659fba207b49a8e07\transformed\camera-extensions-1.4.1\AndroidManifest.xml:22:5-26:15
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d6792621c3f39dfaca06ce80f6b33f1\transformed\play-services-maps-18.2.0\AndroidManifest.xml:30:5-34:15
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d6792621c3f39dfaca06ce80f6b33f1\transformed\play-services-maps-18.2.0\AndroidManifest.xml:30:5-34:15
intent#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+data:scheme:https
ADDED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:14:5-18:14
action#android.intent.action.VIEW
ADDED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:15:7-58
	android:name
		ADDED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:15:15-56
category#android.intent.category.BROWSABLE
ADDED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:16:7-67
	android:name
		ADDED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:16:17-65
data
ADDED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:17:7-37
	android:scheme
		ADDED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:17:13-35
application
ADDED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:20:3-37:17
MERGED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:20:3-37:17
MERGED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:20:3-37:17
INJECTED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\debug\AndroidManifest.xml:6:5-162
MERGED from [:react-native-webview] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:5-29:19
MERGED from [:react-native-webview] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:5-29:19
MERGED from [:react-native-performance] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-performance\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:react-native-performance] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-performance\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-13:19
MERGED from [:expo-dev-launcher] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-30:19
MERGED from [:expo-dev-launcher] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-30:19
MERGED from [:expo-dev-menu] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-22:19
MERGED from [:expo-dev-menu] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-22:19
MERGED from [:expo-image-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:5-57:19
MERGED from [:expo-image-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:5-57:19
MERGED from [:expo-modules-core] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-16:19
MERGED from [:expo-modules-core] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-16:19
MERGED from [com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\59ddc64970c204fac172d4da5009756f\transformed\react-android-0.79.2-debug\AndroidManifest.xml:18:5-22:19
MERGED from [com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\59ddc64970c204fac172d4da5009756f\transformed\react-android-0.79.2-debug\AndroidManifest.xml:18:5-22:19
MERGED from [:expo-file-system] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:5-31:19
MERGED from [:expo-file-system] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:5-31:19
MERGED from [:expo-location] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-location\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:5-15:19
MERGED from [:expo-location] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-location\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:5-15:19
MERGED from [host.exp.exponent:expo.modules.camera:16.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1c53cf14c7eb0e5643d47e3a95b72d0\transformed\expo.modules.camera-16.1.8\AndroidManifest.xml:10:5-14:19
MERGED from [host.exp.exponent:expo.modules.camera:16.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1c53cf14c7eb0e5643d47e3a95b72d0\transformed\expo.modules.camera-16.1.8\AndroidManifest.xml:10:5-14:19
MERGED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:10:5-41:19
MERGED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:10:5-41:19
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f8296c347bb3d1ec471e3402800c1db5\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f8296c347bb3d1ec471e3402800c1db5\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8cc367fa8ce0cb8acb1d9a2937a5991c\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:22:5-36:19
MERGED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8cc367fa8ce0cb8acb1d9a2937a5991c\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:22:5-36:19
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:30:5-143:19
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:30:5-143:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d1f7b5139d06634cc3f34d76f8142494\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d1f7b5139d06634cc3f34d76f8142494\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\797066c703e3fc2659fba207b49a8e07\transformed\camera-extensions-1.4.1\AndroidManifest.xml:28:5-32:19
MERGED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\797066c703e3fc2659fba207b49a8e07\transformed\camera-extensions-1.4.1\AndroidManifest.xml:28:5-32:19
MERGED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\024473314cb477a873a41df1f2dc78f6\transformed\camera-camera2-1.4.1\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\024473314cb477a873a41df1f2dc78f6\transformed\camera-camera2-1.4.1\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\036671903f3c086cdacdad857c082bb6\transformed\camera-core-1.4.1\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\036671903f3c086cdacdad857c082bb6\transformed\camera-core-1.4.1\AndroidManifest.xml:23:5-34:19
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\380599f20dc879b965c86746b2c44cf2\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\380599f20dc879b965c86746b2c44cf2\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:28:5-64:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7a6de7bf0b1470187fab593af8c6930\transformed\firebase-installations-17.2.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7a6de7bf0b1470187fab593af8c6930\transformed\firebase-installations-17.2.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4014643dd64b18ee06fdc929b4f60dfa\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4014643dd64b18ee06fdc929b4f60dfa\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cb6a07ae6c113830fda664a1a3f74969\transformed\firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cb6a07ae6c113830fda664a1a3f74969\transformed\firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5af770104b0f4c5b0cc62a5f02ee8adb\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5af770104b0f4c5b0cc62a5f02ee8adb\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0957acf0320ea66222e16f357b16f5c6\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0957acf0320ea66222e16f357b16f5c6\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac0dda40f5036986d8403279f04f501a\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:7:5-11:19
MERGED from [com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac0dda40f5036986d8403279f04f501a\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:7:5-11:19
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d6792621c3f39dfaca06ce80f6b33f1\transformed\play-services-maps-18.2.0\AndroidManifest.xml:36:5-42:19
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d6792621c3f39dfaca06ce80f6b33f1\transformed\play-services-maps-18.2.0\AndroidManifest.xml:36:5-42:19
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\65ad962932dda2be7285ad5ff0cac28a\transformed\play-services-location-21.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\65ad962932dda2be7285ad5ff0cac28a\transformed\play-services-location-21.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7e13feca8295c54dbb8ebd825318ae5\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:8:5-21:19
MERGED from [com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7e13feca8295c54dbb8ebd825318ae5\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:8:5-21:19
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c482101d03f7fe7ae56d665c96568830\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c482101d03f7fe7ae56d665c96568830\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8a12a52861f5caec9654dea7893e7e4e\transformed\vision-common-17.3.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8a12a52861f5caec9654dea7893e7e4e\transformed\vision-common-17.3.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c9f3d34fca54ef9c7958f49d384f7aba\transformed\common-18.9.0\AndroidManifest.xml:8:5-24:19
MERGED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c9f3d34fca54ef9c7958f49d384f7aba\transformed\common-18.9.0\AndroidManifest.xml:8:5-24:19
MERGED from [com.google.android.gms:play-services-base:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ab0477328e0ae05829c4cc214e49234\transformed\play-services-base-18.2.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ab0477328e0ae05829c4cc214e49234\transformed\play-services-base-18.2.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3be839182c659521a63fa617e8709656\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3be839182c659521a63fa617e8709656\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c0b084c6a67ddf100ea5ba3e68834f86\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c0b084c6a67ddf100ea5ba3e68834f86\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c5c2f030b60fbad5693be9855d0dbe71\transformed\play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c5c2f030b60fbad5693be9855d0dbe71\transformed\play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\58e62418120637b707eea6f2a8aa6087\transformed\play-services-tasks-18.1.0\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\58e62418120637b707eea6f2a8aa6087\transformed\play-services-tasks-18.1.0\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cea641498eb0b29f919daa30d9671bd0\transformed\play-services-basement-18.3.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cea641498eb0b29f919daa30d9671bd0\transformed\play-services-basement-18.3.0\AndroidManifest.xml:20:5-24:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93defa7eb94a1f1ee72c0ca0f587e7d5\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93defa7eb94a1f1ee72c0ca0f587e7d5\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\306a6bcd4bd045cfd61a3c5eb43578e4\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\306a6bcd4bd045cfd61a3c5eb43578e4\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\5c6a9353de243fb8621eac0d0cef3eb2\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\5c6a9353de243fb8621eac0d0cef3eb2\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\99bcfde82ee67925aa78857cee59febf\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\99bcfde82ee67925aa78857cee59febf\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2fa4ab394b458d75a6f846a795c898cf\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2fa4ab394b458d75a6f846a795c898cf\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2059d2ba7c423113ccb55ccbed61837e\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2059d2ba7c423113ccb55ccbed61837e\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4d8cc4ca0e35a89a493aab958b2c0a2c\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4d8cc4ca0e35a89a493aab958b2c0a2c\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:21:5-29:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\2dacf4110cea599a348e4be8992dc6ba\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\2dacf4110cea599a348e4be8992dc6ba\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\1513070d6931c9d145a3a48af88f4f0b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\1513070d6931c9d145a3a48af88f4f0b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:25:5-39:19
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6569cc9cff3589220010bb18ee2ef6ac\transformed\soloader-0.12.1\AndroidManifest.xml:11:5-15:19
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6569cc9cff3589220010bb18ee2ef6ac\transformed\soloader-0.12.1\AndroidManifest.xml:11:5-15:19
MERGED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\514d135c1e94e8854cd72d6563419da6\transformed\installreferrer-2.2\AndroidManifest.xml:11:5-20
MERGED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\514d135c1e94e8854cd72d6563419da6\transformed\installreferrer-2.2\AndroidManifest.xml:11:5-20
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.13\transforms\383c3dee9ae5210dacddee017837237e\transformed\image-1.0.0-beta1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.13\transforms\383c3dee9ae5210dacddee017837237e\transformed\image-1.0.0-beta1\AndroidManifest.xml:7:5-20
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\debug\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\306a6bcd4bd045cfd61a3c5eb43578e4\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:20:221-247
		ADDED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:20:221-247
	android:label
		ADDED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:20:48-80
		ADDED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:20:48-80
	tools:ignore
		ADDED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\debug\AndroidManifest.xml:6:75-114
	android:roundIcon
		ADDED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:20:116-161
		ADDED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:20:116-161
	tools:targetApi
		ADDED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\debug\AndroidManifest.xml:6:54-74
	android:icon
		ADDED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:20:81-115
		ADDED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:20:81-115
	android:allowBackup
		ADDED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:20:162-188
		ADDED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:20:162-188
	android:theme
		ADDED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:20:189-220
		ADDED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:20:189-220
	tools:replace
		ADDED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\debug\AndroidManifest.xml:6:115-159
	android:usesCleartextTraffic
		ADDED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\debug\AndroidManifest.xml:6:18-53
	android:name
		ADDED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:20:16-47
		ADDED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:20:16-47
meta-data#expo.modules.updates.ENABLED
ADDED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:21:5-83
	android:value
		ADDED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:21:60-81
	android:name
		ADDED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:21:16-59
meta-data#expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH
ADDED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:22:5-105
	android:value
		ADDED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:22:81-103
	android:name
		ADDED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:22:16-80
meta-data#expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS
ADDED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:23:5-99
	android:value
		ADDED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:23:80-97
	android:name
		ADDED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:23:16-79
activity#com.eliseedev.mientiorlivraison.MainActivity
ADDED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:24:5-36:16
	android:screenOrientation
		ADDED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:24:277-313
	android:launchMode
		ADDED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:24:135-166
	android:windowSoftInputMode
		ADDED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:24:167-206
	android:exported
		ADDED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:24:253-276
	android:configChanges
		ADDED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:24:44-134
	android:theme
		ADDED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:24:207-252
	android:name
		ADDED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:24:15-43
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:25:7-28:23
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:26:9-60
	android:name
		ADDED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:26:17-58
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:27:9-68
	android:name
		ADDED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:27:19-66
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:scheme:exp+mientior-livraison-afrique+data:scheme:mientior
ADDED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:29:7-35:23
category#android.intent.category.DEFAULT
ADDED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:31:9-67
	android:name
		ADDED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:31:19-65
uses-sdk
INJECTED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\debug\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\debug\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\debug\AndroidManifest.xml
MERGED from [:react-native-gesture-handler] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-gesture-handler\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-gesture-handler] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-gesture-handler\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-safe-area-context] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-safe-area-context] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-screens] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-screens\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-screens] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-screens\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-webview] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-webview] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-edge-to-edge] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-edge-to-edge\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-edge-to-edge] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-edge-to-edge\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-async-storage_async-storage] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-async-storage_async-storage] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-maps] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-maps\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-maps] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-maps\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-performance] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-performance\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-performance] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-performance\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-reanimated] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-reanimated\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-reanimated] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-reanimated\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-svg] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-svg\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-svg] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-svg\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-vector-icons] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-vector-icons\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:react-native-vector-icons] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-vector-icons\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-launcher] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-launcher] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-menu] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-menu] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-menu-interface] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-dev-menu-interface\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-menu-interface] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-dev-menu-interface\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-document-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-document-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-document-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-document-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-image-loader] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-image-loader\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-image-loader] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-image-loader\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-image-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:expo-image-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:expo-modules-core] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:expo-modules-core] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:expo-updates] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-updates\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-updates] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-updates\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.av:15.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\9ba53ecbdb4837594108a38f8aaf5c2b\transformed\expo.modules.av-15.1.6\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.av:15.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\9ba53ecbdb4837594108a38f8aaf5c2b\transformed\expo.modules.av-15.1.6\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.font:13.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\76894519d1352f370f7a165e6bb90dca\transformed\expo.modules.font-13.3.1\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.font:13.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\76894519d1352f370f7a165e6bb90dca\transformed\expo.modules.font-13.3.1\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.splashscreen:0.30.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\d3b099114f52f65dac6ace966b31b031\transformed\expo.modules.splashscreen-0.30.9\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.splashscreen:0.30.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\d3b099114f52f65dac6ace966b31b031\transformed\expo.modules.splashscreen-0.30.9\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\59ddc64970c204fac172d4da5009756f\transformed\react-android-0.79.2-debug\AndroidManifest.xml:10:5-44
MERGED from [com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\59ddc64970c204fac172d4da5009756f\transformed\react-android-0.79.2-debug\AndroidManifest.xml:10:5-44
MERGED from [:expo-constants] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-constants\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-constants] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-constants\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-client] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-dev-client\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-dev-client] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-dev-client\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-eas-client] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-eas-client\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-eas-client] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-eas-client\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-file-system] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:expo-file-system] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:expo-manifests] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-manifests\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-manifests] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-manifests\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-json-utils] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-json-utils\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-json-utils] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-json-utils\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-location] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-location\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-location] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-location\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-structured-headers] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-structured-headers\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-structured-headers] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-structured-headers\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-updates-interface] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-updates-interface\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:expo-updates-interface] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-updates-interface\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.application:6.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\526eed63bd79a2a9ba9b0a7ead38a0be\transformed\expo.modules.application-6.1.4\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.application:6.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\526eed63bd79a2a9ba9b0a7ead38a0be\transformed\expo.modules.application-6.1.4\AndroidManifest.xml:5:5-44
MERGED from [expo.modules.asset:expo.modules.asset:11.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fbad6600b10fcc449ea50d94c23061f\transformed\expo.modules.asset-11.1.5\AndroidManifest.xml:5:5-44
MERGED from [expo.modules.asset:expo.modules.asset:11.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\7fbad6600b10fcc449ea50d94c23061f\transformed\expo.modules.asset-11.1.5\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.camera:16.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1c53cf14c7eb0e5643d47e3a95b72d0\transformed\expo.modules.camera-16.1.8\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.camera:16.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1c53cf14c7eb0e5643d47e3a95b72d0\transformed\expo.modules.camera-16.1.8\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.crypto:14.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\92ad5d01b864e43433e60378b11290f9\transformed\expo.modules.crypto-14.1.5\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.crypto:14.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\92ad5d01b864e43433e60378b11290f9\transformed\expo.modules.crypto-14.1.5\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.device:7.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7f6663c046472e84dadbf219d6a39ae0\transformed\expo.modules.device-7.1.4\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.device:7.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\7f6663c046472e84dadbf219d6a39ae0\transformed\expo.modules.device-7.1.4\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.haptics:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a47fc320c68687c1a718e9fbc28593c\transformed\expo.modules.haptics-14.1.4\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.haptics:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\3a47fc320c68687c1a718e9fbc28593c\transformed\expo.modules.haptics-14.1.4\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.keepawake:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\5678df72fac38fd8ea02d011c187cb4b\transformed\expo.modules.keepawake-14.1.4\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.keepawake:14.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\5678df72fac38fd8ea02d011c187cb4b\transformed\expo.modules.keepawake-14.1.4\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.lineargradient:14.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\317774afd492fd826f34b820def5d4bd\transformed\expo.modules.lineargradient-14.1.5\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.lineargradient:14.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\317774afd492fd826f34b820def5d4bd\transformed\expo.modules.lineargradient-14.1.5\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.network:7.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\1660d72d87cc431f2e761d31424f03f3\transformed\expo.modules.network-7.1.5\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.network:7.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\1660d72d87cc431f2e761d31424f03f3\transformed\expo.modules.network-7.1.5\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.sms:13.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\0531343fe10d3c1dea07e7b068516664\transformed\expo.modules.sms-13.1.4\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.sms:13.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\0531343fe10d3c1dea07e7b068516664\transformed\expo.modules.sms-13.1.4\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.speech:13.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\6f3f3fe67e7bb5816c905504e02db79c\transformed\expo.modules.speech-13.1.7\AndroidManifest.xml:5:5-44
MERGED from [host.exp.exponent:expo.modules.speech:13.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\6f3f3fe67e7bb5816c905504e02db79c\transformed\expo.modules.speech-13.1.7\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f8296c347bb3d1ec471e3402800c1db5\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f8296c347bb3d1ec471e3402800c1db5\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8cc367fa8ce0cb8acb1d9a2937a5991c\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8cc367fa8ce0cb8acb1d9a2937a5991c\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d1f7b5139d06634cc3f34d76f8142494\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d1f7b5139d06634cc3f34d76f8142494\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d0a6ecdf0aff4ac27300243ee4f3450\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0d0a6ecdf0aff4ac27300243ee4f3450\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-mlkit-vision:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\106d4abcfa289e330f5f732bb66f1416\transformed\camera-mlkit-vision-1.4.1\AndroidManifest.xml:18:5-20:70
MERGED from [androidx.camera:camera-mlkit-vision:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\106d4abcfa289e330f5f732bb66f1416\transformed\camera-mlkit-vision-1.4.1\AndroidManifest.xml:18:5-20:70
MERGED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\797066c703e3fc2659fba207b49a8e07\transformed\camera-extensions-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\797066c703e3fc2659fba207b49a8e07\transformed\camera-extensions-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-view:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e2de79c846eda1c5d16a048c6ee1e37\transformed\camera-view-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-view:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e2de79c846eda1c5d16a048c6ee1e37\transformed\camera-view-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-video:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0727d4e1ef3966efe60e3bd863cea08a\transformed\camera-video-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-video:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0727d4e1ef3966efe60e3bd863cea08a\transformed\camera-video-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-lifecycle:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b9a59f80a87d711a5ffaf7eb8c128bf\transformed\camera-lifecycle-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-lifecycle:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7b9a59f80a87d711a5ffaf7eb8c128bf\transformed\camera-lifecycle-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\024473314cb477a873a41df1f2dc78f6\transformed\camera-camera2-1.4.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\024473314cb477a873a41df1f2dc78f6\transformed\camera-camera2-1.4.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\036671903f3c086cdacdad857c082bb6\transformed\camera-core-1.4.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\036671903f3c086cdacdad857c082bb6\transformed\camera-core-1.4.1\AndroidManifest.xml:21:5-44
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\380599f20dc879b965c86746b2c44cf2\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\380599f20dc879b965c86746b2c44cf2\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7a6de7bf0b1470187fab593af8c6930\transformed\firebase-installations-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7a6de7bf0b1470187fab593af8c6930\transformed\firebase-installations-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4014643dd64b18ee06fdc929b4f60dfa\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4014643dd64b18ee06fdc929b4f60dfa\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cb6a07ae6c113830fda664a1a3f74969\transformed\firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cb6a07ae6c113830fda664a1a3f74969\transformed\firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5af770104b0f4c5b0cc62a5f02ee8adb\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-extensions:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5af770104b0f4c5b0cc62a5f02ee8adb\transformed\lifecycle-extensions-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core-splashscreen:1.2.0-alpha02] C:\Users\<USER>\.gradle\caches\8.13\transforms\474386d8e7be06cc66831d67b9588d7f\transformed\core-splashscreen-1.2.0-alpha02\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-splashscreen:1.2.0-alpha02] C:\Users\<USER>\.gradle\caches\8.13\transforms\474386d8e7be06cc66831d67b9588d7f\transformed\core-splashscreen-1.2.0-alpha02\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\48e371bdf8a64584b3708853a06cc965\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\48e371bdf8a64584b3708853a06cc965\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0957acf0320ea66222e16f357b16f5c6\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0957acf0320ea66222e16f357b16f5c6\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8fb4e20b948a58ead7b50b7ad3e15312\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8fb4e20b948a58ead7b50b7ad3e15312\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac0dda40f5036986d8403279f04f501a\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac0dda40f5036986d8403279f04f501a\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d6792621c3f39dfaca06ce80f6b33f1\transformed\play-services-maps-18.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d6792621c3f39dfaca06ce80f6b33f1\transformed\play-services-maps-18.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\65ad962932dda2be7285ad5ff0cac28a\transformed\play-services-location-21.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-location:21.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\65ad962932dda2be7285ad5ff0cac28a\transformed\play-services-location-21.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7e13feca8295c54dbb8ebd825318ae5\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7e13feca8295c54dbb8ebd825318ae5\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:barcode-scanning:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\55a17c7390d823dc4ef06f880f3eb817\transformed\barcode-scanning-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:barcode-scanning:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\55a17c7390d823dc4ef06f880f3eb817\transformed\barcode-scanning-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c482101d03f7fe7ae56d665c96568830\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c482101d03f7fe7ae56d665c96568830\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:barcode-scanning-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\de3198b8b443d15a4e565b37e7e1d563\transformed\barcode-scanning-common-17.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:barcode-scanning-common:17.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\de3198b8b443d15a4e565b37e7e1d563\transformed\barcode-scanning-common-17.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8a12a52861f5caec9654dea7893e7e4e\transformed\vision-common-17.3.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8a12a52861f5caec9654dea7893e7e4e\transformed\vision-common-17.3.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c9f3d34fca54ef9c7958f49d384f7aba\transformed\common-18.9.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c9f3d34fca54ef9c7958f49d384f7aba\transformed\common-18.9.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-base:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ab0477328e0ae05829c4cc214e49234\transformed\play-services-base-18.2.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ab0477328e0ae05829c4cc214e49234\transformed\play-services-base-18.2.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.fragment:fragment-ktx:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\20c743a7abee82ff00eb21bb5657d8ac\transformed\fragment-ktx-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\20c743a7abee82ff00eb21bb5657d8ac\transformed\fragment-ktx-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1cb54ba7a96073311227ab0a47db11ff\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1cb54ba7a96073311227ab0a47db11ff\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cea4a31bf1b24410556c1e17095429a9\transformed\glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cea4a31bf1b24410556c1e17095429a9\transformed\glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9ce8d5174fe9c124eecfd6e35bcbacc0\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9ce8d5174fe9c124eecfd6e35bcbacc0\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3be839182c659521a63fa617e8709656\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-iid-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3be839182c659521a63fa617e8709656\transformed\firebase-iid-interop-17.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c0b084c6a67ddf100ea5ba3e68834f86\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c0b084c6a67ddf100ea5ba3e68834f86\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e860328566f0fae28883e3e1c789e1e7\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e860328566f0fae28883e3e1c789e1e7\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c5c2f030b60fbad5693be9855d0dbe71\transformed\play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c5c2f030b60fbad5693be9855d0dbe71\transformed\play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:vision-interfaces:16.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ff2fbbe45984b9424aac1d4528ba5869\transformed\vision-interfaces-16.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.mlkit:vision-interfaces:16.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ff2fbbe45984b9424aac1d4528ba5869\transformed\vision-interfaces-16.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3b856e30758c9f699ebd84b99bdd09a6\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\3b856e30758c9f699ebd84b99bdd09a6\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\58e62418120637b707eea6f2a8aa6087\transformed\play-services-tasks-18.1.0\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\58e62418120637b707eea6f2a8aa6087\transformed\play-services-tasks-18.1.0\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cea641498eb0b29f919daa30d9671bd0\transformed\play-services-basement-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cea641498eb0b29f919daa30d9671bd0\transformed\play-services-basement-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.fragment:fragment:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b87fc65a1354d1557c2c596a386f156e\transformed\fragment-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\b87fc65a1354d1557c2c596a386f156e\transformed\fragment-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\25fc0610bb68c3c8dff3fe308edcbf91\transformed\activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\25fc0610bb68c3c8dff3fe308edcbf91\transformed\activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\388e8c3dd256266ced8f35de3400115e\transformed\activity-ktx-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\388e8c3dd256266ced8f35de3400115e\transformed\activity-ktx-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7060f5f04711df35f0554a7998cb34c0\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7060f5f04711df35f0554a7998cb34c0\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7d4269c44ba19209573180606829e5fb\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\7d4269c44ba19209573180606829e5fb\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\61eab4c931962cd5b4f2baad1a98e42c\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\61eab4c931962cd5b4f2baad1a98e42c\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\55ccd8a6d0c2317bd39cb3e23a8b5388\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\55ccd8a6d0c2317bd39cb3e23a8b5388\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.webkit:webkit:1.14.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b20eb650dfeaa7f22637fabe00fb0d4\transformed\webkit-1.14.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.webkit:webkit:1.14.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8b20eb650dfeaa7f22637fabe00fb0d4\transformed\webkit-1.14.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\be31e15b9f8850576dc84f6d337b4de2\transformed\autofill-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\be31e15b9f8850576dc84f6d337b4de2\transformed\autofill-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.facebook.fresco:animated-gif:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\074bb583a4794a74147dcaf6e6cbee2e\transformed\animated-gif-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-gif:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\074bb583a4794a74147dcaf6e6cbee2e\transformed\animated-gif-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:webpsupport:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce05230d41988a409f9516b9f3306d87\transformed\webpsupport-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:webpsupport:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce05230d41988a409f9516b9f3306d87\transformed\webpsupport-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fresco:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0a1252810784b6addd419a8c9792e526\transformed\fresco-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fresco:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0a1252810784b6addd419a8c9792e526\transformed\fresco-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c4bd2c8d482a029f69ffadfe68c7abed\transformed\imagepipeline-okhttp3-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c4bd2c8d482a029f69ffadfe68c7abed\transformed\imagepipeline-okhttp3-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\862c7c3df9b738b05f6d46274c06617a\transformed\animated-base-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\862c7c3df9b738b05f6d46274c06617a\transformed\animated-base-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-drawable:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\041b7ae1712387c8dd5960d9f372e732\transformed\animated-drawable-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-drawable:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\041b7ae1712387c8dd5960d9f372e732\transformed\animated-drawable-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-options:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6096d38cdaff73aa972de6b6707813c6\transformed\vito-options-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-options:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6096d38cdaff73aa972de6b6707813c6\transformed\vito-options-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:drawee:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c5639c82180c7bcf17e33ad9734cf384\transformed\drawee-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:drawee:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c5639c82180c7bcf17e33ad9734cf384\transformed\drawee-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagefilters:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\72eddf541678f353fd68a8469221f55e\transformed\nativeimagefilters-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagefilters:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\72eddf541678f353fd68a8469221f55e\transformed\nativeimagefilters-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9535f4ff6340b1a5af923b3ebb784298\transformed\memory-type-native-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9535f4ff6340b1a5af923b3ebb784298\transformed\memory-type-native-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-java:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b9b891b5905d77be1ddea8b155a076ca\transformed\memory-type-java-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-java:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b9b891b5905d77be1ddea8b155a076ca\transformed\memory-type-java-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c1ef3f4178e5a3aa490bc581880d7658\transformed\imagepipeline-native-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-native:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c1ef3f4178e5a3aa490bc581880d7658\transformed\imagepipeline-native-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-ashmem:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f44ab799b9464a69c0034f0db541ac6e\transformed\memory-type-ashmem-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-ashmem:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f44ab799b9464a69c0034f0db541ac6e\transformed\memory-type-ashmem-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\46b46437de2beb342752c4d38d69d22a\transformed\imagepipeline-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\46b46437de2beb342752c4d38d69d22a\transformed\imagepipeline-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\82e341dc7190d17e54c2ae081bc91dd4\transformed\nativeimagetranscoder-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\82e341dc7190d17e54c2ae081bc91dd4\transformed\nativeimagetranscoder-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0701e84f9b3f094cb163e59bacb96ef8\transformed\imagepipeline-base-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-base:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0701e84f9b3f094cb163e59bacb96ef8\transformed\imagepipeline-base-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:urimod:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b52fa6429dc15f948ed3fadd1be29b49\transformed\urimod-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:urimod:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b52fa6429dc15f948ed3fadd1be29b49\transformed\urimod-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-source:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a481df75262d5aafe498df81fa58feb\transformed\vito-source-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-source:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a481df75262d5aafe498df81fa58feb\transformed\vito-source-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:middleware:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a54160f52c3f0e456c28aba102d8f08\transformed\middleware-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:middleware:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a54160f52c3f0e456c28aba102d8f08\transformed\middleware-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-common:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\75ebfe97972548fde52f512d63d36859\transformed\ui-common-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-common:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\75ebfe97972548fde52f512d63d36859\transformed\ui-common-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:soloader:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1815db30808f5fa58b147cd854fcb383\transformed\soloader-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:soloader:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1815db30808f5fa58b147cd854fcb383\transformed\soloader-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fbcore:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0024be695326049e61c640cfd5222ec\transformed\fbcore-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fbcore:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0024be695326049e61c640cfd5222ec\transformed\fbcore-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ff199f796fece94789ea9bd928f0e62a\transformed\browser-1.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ff199f796fece94789ea9bd928f0e62a\transformed\browser-1.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd4f01296266bad52a168d7ea8a8a811\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dd4f01296266bad52a168d7ea8a8a811\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd4e473968f9c57d3e4bc7ccd1383605\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd4e473968f9c57d3e4bc7ccd1383605\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\90ea4b92e43f121d19c9c04e6be6a5de\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\90ea4b92e43f121d19c9c04e6be6a5de\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0f316fa4aeda6489db0ee69d64bd3d7b\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0f316fa4aeda6489db0ee69d64bd3d7b\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c397dece186ebe2145e1da66121c87c\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c397dece186ebe2145e1da66121c87c\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\463c44255696161c6368323a9142d2ec\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\463c44255696161c6368323a9142d2ec\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e97b330b761ee3e761e320dde3414d40\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e97b330b761ee3e761e320dde3414d40\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93defa7eb94a1f1ee72c0ca0f587e7d5\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93defa7eb94a1f1ee72c0ca0f587e7d5\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\003636cc0a8fb63f6e38b6fa18505c26\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\003636cc0a8fb63f6e38b6fa18505c26\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.exoplayer:exoplayer:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\73689df39770fdcd7616a1cd6340e5ee\transformed\exoplayer-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\73689df39770fdcd7616a1cd6340e5ee\transformed\exoplayer-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-dash:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\970b4431286e20e92ccd905d4f4ff19b\transformed\exoplayer-dash-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-dash:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\970b4431286e20e92ccd905d4f4ff19b\transformed\exoplayer-dash-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-hls:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\143c46925aa593820271ea2c3ac9f07d\transformed\exoplayer-hls-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-hls:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\143c46925aa593820271ea2c3ac9f07d\transformed\exoplayer-hls-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-rtsp:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\147323c9757f936bf50f92805f9d17ff\transformed\exoplayer-rtsp-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-rtsp:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\147323c9757f936bf50f92805f9d17ff\transformed\exoplayer-rtsp-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-smoothstreaming:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\438ac7e1fb74bb6aff04c26dd77f57d2\transformed\exoplayer-smoothstreaming-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-smoothstreaming:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\438ac7e1fb74bb6aff04c26dd77f57d2\transformed\exoplayer-smoothstreaming-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-core:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1b1d6e3a1eafdeb637916b2c04d0a37e\transformed\exoplayer-core-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-core:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1b1d6e3a1eafdeb637916b2c04d0a37e\transformed\exoplayer-core-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a13766dc6d0438e6cff1b3d0ad48ab1\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a13766dc6d0438e6cff1b3d0ad48ab1\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4279b3c49ba46da5202fb0b313827593\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4279b3c49ba46da5202fb0b313827593\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8236f30b9ad1cf2a84fab1fcd4549768\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8236f30b9ad1cf2a84fab1fcd4549768\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.exoplayer:exoplayer-ui:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4407803e7a2d8d3f17c090093cb28e53\transformed\exoplayer-ui-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-ui:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\4407803e7a2d8d3f17c090093cb28e53\transformed\exoplayer-ui-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b473aa377bb69d3a5a331b37a35a6c4\transformed\recyclerview-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2b473aa377bb69d3a5a331b37a35a6c4\transformed\recyclerview-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\07a66c49e5f9dc73d772c18c9aa0278c\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\07a66c49e5f9dc73d772c18c9aa0278c\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f25ac7b0e07eb0ad51ef05ac5c4cfb00\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f25ac7b0e07eb0ad51ef05ac5c4cfb00\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media:media:1.4.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\807c1c9c9def292ba3b209c6e060146f\transformed\media-1.4.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.4.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\807c1c9c9def292ba3b209c6e060146f\transformed\media-1.4.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\306a6bcd4bd045cfd61a3c5eb43578e4\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\306a6bcd4bd045cfd61a3c5eb43578e4\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\905d82f854a796b225cd9d8a92d03bfe\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\905d82f854a796b225cd9d8a92d03bfe\transformed\lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7aa886086c77d026b1a2e723b8c7a258\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\7aa886086c77d026b1a2e723b8c7a258\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b7be05393bec07f8e724f73a4c2c414\transformed\lifecycle-service-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\9b7be05393bec07f8e724f73a4c2c414\transformed\lifecycle-service-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a7c0c4c7b6f361441770bb006a5736d\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a7c0c4c7b6f361441770bb006a5736d\transformed\lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\5c6a9353de243fb8621eac0d0cef3eb2\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\5c6a9353de243fb8621eac0d0cef3eb2\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c6fccd578e6c1f899999a9b5e08a6662\transformed\lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\c6fccd578e6c1f899999a9b5e08a6662\transformed\lifecycle-livedata-core-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\5f09fbd5cc8fa956dbfe02ed0b475cce\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\5f09fbd5cc8fa956dbfe02ed0b475cce\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce3ca40a8dd11cd0ee824c7cf6469991\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ce3ca40a8dd11cd0ee824c7cf6469991\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b257d7638de1f9c517230d70a67d45ed\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\b257d7638de1f9c517230d70a67d45ed\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\41b496c921314be4bab74a3a36df7b14\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\41b496c921314be4bab74a3a36df7b14\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc20679afcfa59d2910a7f46a536b789\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cc20679afcfa59d2910a7f46a536b789\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.exoplayer:extension-okhttp:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\76c4a14a45ce3d22cfe77b307c62e00f\transformed\extension-okhttp-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:extension-okhttp:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\76c4a14a45ce3d22cfe77b307c62e00f\transformed\extension-okhttp-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\99bcfde82ee67925aa78857cee59febf\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\99bcfde82ee67925aa78857cee59febf\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2fa4ab394b458d75a6f846a795c898cf\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2fa4ab394b458d75a6f846a795c898cf\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00bcef2e622b104f18727db070d6169d\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\00bcef2e622b104f18727db070d6169d\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42111d6a7e8e3843f3a83cea99a9af0f\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42111d6a7e8e3843f3a83cea99a9af0f\transformed\tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.facebook.fresco:ui-core:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\baba08885ee5adeb5c521e1907c1be27\transformed\ui-core-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-core:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\baba08885ee5adeb5c521e1907c1be27\transformed\ui-core-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a30b7026cb4b6a42d96b5350a586e1f9\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a30b7026cb4b6a42d96b5350a586e1f9\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\767f83551e2e5bb29c2ebda563cad74e\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\767f83551e2e5bb29c2ebda563cad74e\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd04975a740a9fae43a5dd659946ffdf\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bd04975a740a9fae43a5dd659946ffdf\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.facebook.react:hermes-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d74530985233716120af53cf728703af\transformed\hermes-android-0.79.2-debug\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:hermes-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d74530985233716120af53cf728703af\transformed\hermes-android-0.79.2-debug\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fdc902cc195ef0e05974ea40670694b\transformed\viewbinding-8.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\3fdc902cc195ef0e05974ea40670694b\transformed\viewbinding-8.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\40dccc500a1fbb51c39df29d566338e9\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\40dccc500a1fbb51c39df29d566338e9\transformed\exifinterface-1.3.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0b50548968ff1170f1dad50aeb1f984c\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0b50548968ff1170f1dad50aeb1f984c\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3cd9a9c3a591095815201ef436d7e312\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3cd9a9c3a591095815201ef436d7e312\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c2e40e29d7aa6530eca75dae92115591\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c2e40e29d7aa6530eca75dae92115591\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2059d2ba7c423113ccb55ccbed61837e\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2059d2ba7c423113ccb55ccbed61837e\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6722f804269a01b830a058251481e90a\transformed\gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6722f804269a01b830a058251481e90a\transformed\gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\18584f70131a7e8a2675c0a09621b404\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\18584f70131a7e8a2675c0a09621b404\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.exoplayer:exoplayer-datasource:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\832c8697706c97d037abb41fbcc978c9\transformed\exoplayer-datasource-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-datasource:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\832c8697706c97d037abb41fbcc978c9\transformed\exoplayer-datasource-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-database:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6b464e8453e92d11587730488ac2e6f\transformed\exoplayer-database-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-database:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a6b464e8453e92d11587730488ac2e6f\transformed\exoplayer-database-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-extractor:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf7f9057072a4e54731b2037e5a36941\transformed\exoplayer-extractor-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-extractor:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\cf7f9057072a4e54731b2037e5a36941\transformed\exoplayer-extractor-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-decoder:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\90ec43178bace097fd5118ad07eff9b7\transformed\exoplayer-decoder-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-decoder:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\90ec43178bace097fd5118ad07eff9b7\transformed\exoplayer-decoder-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-common:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\517add4539723efc219e05c2f11e7214\transformed\exoplayer-common-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.exoplayer:exoplayer-common:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\517add4539723efc219e05c2f11e7214\transformed\exoplayer-common-2.18.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f15f2e8d8ce89352f13363c0e26a758f\transformed\firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f15f2e8d8ce89352f13363c0e26a758f\transformed\firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4d8cc4ca0e35a89a493aab958b2c0a2c\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4d8cc4ca0e35a89a493aab958b2c0a2c\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\2dacf4110cea599a348e4be8992dc6ba\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\2dacf4110cea599a348e4be8992dc6ba\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e24e380aebdbe8a0ea2a84ed997efd74\transformed\firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e24e380aebdbe8a0ea2a84ed997efd74\transformed\firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\1513070d6931c9d145a3a48af88f4f0b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\1513070d6931c9d145a3a48af88f4f0b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ed1b76198b0839600988218b12fbb54d\transformed\transport-api-3.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.datatransport:transport-api:3.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\ed1b76198b0839600988218b12fbb54d\transformed\transport-api-3.1.0\AndroidManifest.xml:18:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\60c896b32d4b1dcf45dec97c7e45616d\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\60c896b32d4b1dcf45dec97c7e45616d\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b3534eeb4edab1b173bc8cbb06465876\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b3534eeb4edab1b173bc8cbb06465876\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bec391801e1a359fc391013776935fec\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\bec391801e1a359fc391013776935fec\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.facebook.fresco:vito-renderer:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8cc9f30128ebd284f23d542534d950f6\transformed\vito-renderer-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-renderer:3.6.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8cc9f30128ebd284f23d542534d950f6\transformed\vito-renderer-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fbjni:fbjni:0.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e30aad97b36547574040f15ffce4d4f\transformed\fbjni-0.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fbjni:fbjni:0.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e30aad97b36547574040f15ffce4d4f\transformed\fbjni-0.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6569cc9cff3589220010bb18ee2ef6ac\transformed\soloader-0.12.1\AndroidManifest.xml:7:5-9:41
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6569cc9cff3589220010bb18ee2ef6ac\transformed\soloader-0.12.1\AndroidManifest.xml:7:5-9:41
MERGED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\514d135c1e94e8854cd72d6563419da6\transformed\installreferrer-2.2\AndroidManifest.xml:5:5-7:41
MERGED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\514d135c1e94e8854cd72d6563419da6\transformed\installreferrer-2.2\AndroidManifest.xml:5:5-7:41
MERGED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\6329ce871df5828ee152a72c53b9164a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:7:5-9:41
MERGED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\6329ce871df5828ee152a72c53b9164a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.13\transforms\383c3dee9ae5210dacddee017837237e\transformed\image-1.0.0-beta1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.13\transforms\383c3dee9ae5210dacddee017837237e\transformed\image-1.0.0-beta1\AndroidManifest.xml:5:5-44
	tools:overrideLibrary
		ADDED from [androidx.camera:camera-mlkit-vision:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\106d4abcfa289e330f5f732bb66f1416\transformed\camera-mlkit-vision-1.4.1\AndroidManifest.xml:20:9-67
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\debug\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\debug\AndroidManifest.xml
intent#action:name:org.chromium.intent.action.PAY
ADDED from [:react-native-webview] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-10:18
action#org.chromium.intent.action.PAY
ADDED from [:react-native-webview] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-69
	android:name
		ADDED from [:react-native-webview] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:21-66
intent#action:name:org.chromium.intent.action.IS_READY_TO_PAY
ADDED from [:react-native-webview] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:9-13:18
action#org.chromium.intent.action.IS_READY_TO_PAY
ADDED from [:react-native-webview] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-81
	android:name
		ADDED from [:react-native-webview] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:21-78
intent#action:name:org.chromium.intent.action.UPDATE_PAYMENT_DETAILS
ADDED from [:react-native-webview] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:9-16:18
action#org.chromium.intent.action.UPDATE_PAYMENT_DETAILS
ADDED from [:react-native-webview] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-88
	android:name
		ADDED from [:react-native-webview] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:21-85
provider#com.reactnativecommunity.webview.RNCWebViewFileProvider
ADDED from [:react-native-webview] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:9-28:20
	android:grantUriPermissions
		ADDED from [:react-native-webview] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-47
	android:authorities
		ADDED from [:react-native-webview] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-64
	android:exported
		ADDED from [:react-native-webview] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-37
	android:name
		ADDED from [:react-native-webview] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-83
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [:react-native-webview] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-27:63
	android:resource
		ADDED from [:react-native-webview] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:17-60
	android:name
		ADDED from [:react-native-webview] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:17-67
provider#com.oblador.performance.StartTimeProvider
ADDED from [:react-native-performance] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-performance\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:39
	android:authorities
		ADDED from [:react-native-performance] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-performance\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-71
	android:exported
		ADDED from [:react-native-performance] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-performance\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
	android:initOrder
		ADDED from [:react-native-performance] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-performance\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-36
	android:name
		ADDED from [:react-native-performance] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-performance\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-69
package#host.exp.exponent
ADDED from [:expo-dev-launcher] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-53
	android:name
		ADDED from [:expo-dev-launcher] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:18-50
activity#expo.modules.devlauncher.launcher.DevLauncherActivity
ADDED from [:expo-dev-launcher] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-25:20
	android:launchMode
		ADDED from [:expo-dev-launcher] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-44
	android:exported
		ADDED from [:expo-dev-launcher] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-36
	android:theme
		ADDED from [:expo-dev-launcher] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-70
	android:name
		ADDED from [:expo-dev-launcher] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-81
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:scheme:expo-dev-launcher
ADDED from [:expo-dev-launcher] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-24:29
activity#expo.modules.devlauncher.launcher.errors.DevLauncherErrorActivity
ADDED from [:expo-dev-launcher] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-29:70
	android:screenOrientation
		ADDED from [:expo-dev-launcher] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-49
	android:theme
		ADDED from [:expo-dev-launcher] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-67
	android:name
		ADDED from [:expo-dev-launcher] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-93
activity#expo.modules.devmenu.DevMenuActivity
ADDED from [:expo-dev-menu] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-21:20
	android:launchMode
		ADDED from [:expo-dev-menu] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-44
	android:exported
		ADDED from [:expo-dev-menu] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-36
	android:theme
		ADDED from [:expo-dev-menu] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-75
	android:name
		ADDED from [:expo-dev-menu] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-64
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:scheme:expo-dev-menu
ADDED from [:expo-dev-menu] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-20:29
intent#action:name:android.intent.action.OPEN_DOCUMENT+category:name:android.intent.category.DEFAULT+category:name:android.intent.category.OPENABLE+data:mimeType:*/*
ADDED from [:expo-document-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-document-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:9-17:18
action#android.intent.action.OPEN_DOCUMENT
ADDED from [:expo-document-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-document-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-74
	android:name
		ADDED from [:expo-document-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-document-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:21-71
category#android.intent.category.OPENABLE
ADDED from [:expo-document-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-document-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-73
	android:name
		ADDED from [:expo-document-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-document-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:23-70
uses-permission#android.permission.READ_MEDIA_IMAGES
ADDED from [:expo-image-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:5-76
	android:name
		ADDED from [:expo-image-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:22-73
uses-permission#android.permission.READ_MEDIA_VIDEO
ADDED from [:expo-image-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:5-75
	android:name
		ADDED from [:expo-image-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:22-72
intent#action:name:android.media.action.IMAGE_CAPTURE
ADDED from [:expo-image-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:9-21:18
action#android.media.action.IMAGE_CAPTURE
ADDED from [:expo-image-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-73
	android:name
		ADDED from [:expo-image-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:21-70
intent#action:name:android.media.action.ACTION_VIDEO_CAPTURE
ADDED from [:expo-image-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:9-26:18
action#android.media.action.ACTION_VIDEO_CAPTURE
ADDED from [:expo-image-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-80
	android:name
		ADDED from [:expo-image-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:21-77
service#com.google.android.gms.metadata.ModuleDependencies
ADDED from [:expo-image-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:9-42:19
	android:enabled
		ADDED from [:expo-image-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:32:13-36
	android:exported
		ADDED from [:expo-image-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:33:13-37
	tools:ignore
		ADDED from [:expo-image-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:34:13-40
	android:name
		ADDED from [:expo-image-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:13-78
intent-filter#action:name:com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [:expo-image-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:13-37:29
action#com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [:expo-image-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:17-94
	android:name
		ADDED from [:expo-image-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:25-91
meta-data#photopicker_activity:0:required
ADDED from [:expo-image-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:39:13-41:36
	android:value
		ADDED from [:expo-image-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:41:17-33
	android:name
		ADDED from [:expo-image-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:40:17-63
activity#com.canhub.cropper.CropImageActivity
ADDED from [:expo-image-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:44:9-46:59
MERGED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8cc367fa8ce0cb8acb1d9a2937a5991c\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:33:9-35:39
MERGED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8cc367fa8ce0cb8acb1d9a2937a5991c\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:33:9-35:39
	android:exported
		ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8cc367fa8ce0cb8acb1d9a2937a5991c\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:35:13-36
	android:theme
		ADDED from [:expo-image-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:46:13-56
	android:name
		ADDED from [:expo-image-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:45:13-64
provider#expo.modules.imagepicker.fileprovider.ImagePickerFileProvider
ADDED from [:expo-image-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:48:9-56:20
	android:grantUriPermissions
		ADDED from [:expo-image-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:52:13-47
	android:authorities
		ADDED from [:expo-image-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:50:13-75
	android:exported
		ADDED from [:expo-image-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:51:13-37
	android:name
		ADDED from [:expo-image-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:49:13-89
meta-data#org.unimodules.core.AppLoader#react-native-headless
ADDED from [:expo-modules-core] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-11:89
	android:value
		ADDED from [:expo-modules-core] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-86
	android:name
		ADDED from [:expo-modules-core] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-79
meta-data#com.facebook.soloader.enabled
ADDED from [:expo-modules-core] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-15:45
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6569cc9cff3589220010bb18ee2ef6ac\transformed\soloader-0.12.1\AndroidManifest.xml:12:9-14:37
MERGED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6569cc9cff3589220010bb18ee2ef6ac\transformed\soloader-0.12.1\AndroidManifest.xml:12:9-14:37
	tools:replace
		ADDED from [:expo-modules-core] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-42
	android:value
		ADDED from [:expo-modules-core] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-33
		REJECTED from [com.facebook.soloader:soloader:0.12.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\6569cc9cff3589220010bb18ee2ef6ac\transformed\soloader-0.12.1\AndroidManifest.xml:14:13-34
	android:name
		ADDED from [:expo-modules-core] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-57
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [:expo-updates] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-updates\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-79
MERGED from [host.exp.exponent:expo.modules.network:7.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\1660d72d87cc431f2e761d31424f03f3\transformed\expo.modules.network-7.1.5\AndroidManifest.xml:8:5-79
MERGED from [host.exp.exponent:expo.modules.network:7.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\1660d72d87cc431f2e761d31424f03f3\transformed\expo.modules.network-7.1.5\AndroidManifest.xml:8:5-79
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:26:5-79
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:26:5-79
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\380599f20dc879b965c86746b2c44cf2\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\380599f20dc879b965c86746b2c44cf2\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7a6de7bf0b1470187fab593af8c6930\transformed\firebase-installations-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7a6de7bf0b1470187fab593af8c6930\transformed\firebase-installations-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d6792621c3f39dfaca06ce80f6b33f1\transformed\play-services-maps-18.2.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d6792621c3f39dfaca06ce80f6b33f1\transformed\play-services-maps-18.2.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e860328566f0fae28883e3e1c789e1e7\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e860328566f0fae28883e3e1c789e1e7\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.exoplayer:exoplayer-core:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1b1d6e3a1eafdeb637916b2c04d0a37e\transformed\exoplayer-core-2.18.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.exoplayer:exoplayer-core:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1b1d6e3a1eafdeb637916b2c04d0a37e\transformed\exoplayer-core-2.18.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.exoplayer:exoplayer-common:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\517add4539723efc219e05c2f11e7214\transformed\exoplayer-common-2.18.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.exoplayer:exoplayer-common:2.18.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\517add4539723efc219e05c2f11e7214\transformed\exoplayer-common-2.18.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\2dacf4110cea599a348e4be8992dc6ba\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\2dacf4110cea599a348e4be8992dc6ba\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\1513070d6931c9d145a3a48af88f4f0b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\1513070d6931c9d145a3a48af88f4f0b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:22:5-79
	android:name
		ADDED from [:expo-updates] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-updates\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-76
activity#com.facebook.react.devsupport.DevSettingsActivity
ADDED from [com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\59ddc64970c204fac172d4da5009756f\transformed\react-android-0.79.2-debug\AndroidManifest.xml:19:9-21:40
	android:exported
		ADDED from [com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\59ddc64970c204fac172d4da5009756f\transformed\react-android-0.79.2-debug\AndroidManifest.xml:21:13-37
	android:name
		ADDED from [com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\59ddc64970c204fac172d4da5009756f\transformed\react-android-0.79.2-debug\AndroidManifest.xml:20:13-77
intent#action:name:android.intent.action.OPEN_DOCUMENT_TREE
ADDED from [:expo-file-system] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:9-17:18
action#android.intent.action.OPEN_DOCUMENT_TREE
ADDED from [:expo-file-system] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-79
	android:name
		ADDED from [:expo-file-system] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:21-76
provider#expo.modules.filesystem.FileSystemFileProvider
ADDED from [:expo-file-system] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:9-30:20
	android:grantUriPermissions
		ADDED from [:expo-file-system] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-47
	android:authorities
		ADDED from [:expo-file-system] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-74
	android:exported
		ADDED from [:expo-file-system] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-37
	tools:replace
		ADDED from [:expo-file-system] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:13-48
	android:name
		ADDED from [:expo-file-system] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-74
service#expo.modules.location.services.LocationTaskService
ADDED from [:expo-location] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-location\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:9-14:56
	android:exported
		ADDED from [:expo-location] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-location\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-37
	android:foregroundServiceType
		ADDED from [:expo-location] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-location\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-53
	android:name
		ADDED from [:expo-location] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-location\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-78
meta-data#com.google.mlkit.vision.DEPENDENCIES
ADDED from [host.exp.exponent:expo.modules.camera:16.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1c53cf14c7eb0e5643d47e3a95b72d0\transformed\expo.modules.camera-16.1.8\AndroidManifest.xml:11:9-13:42
	android:value
		ADDED from [host.exp.exponent:expo.modules.camera:16.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1c53cf14c7eb0e5643d47e3a95b72d0\transformed\expo.modules.camera-16.1.8\AndroidManifest.xml:13:13-39
	android:name
		ADDED from [host.exp.exponent:expo.modules.camera:16.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1c53cf14c7eb0e5643d47e3a95b72d0\transformed\expo.modules.camera-16.1.8\AndroidManifest.xml:12:13-64
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from [host.exp.exponent:expo.modules.network:7.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\1660d72d87cc431f2e761d31424f03f3\transformed\expo.modules.network-7.1.5\AndroidManifest.xml:7:5-76
	android:name
		ADDED from [host.exp.exponent:expo.modules.network:7.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\1660d72d87cc431f2e761d31424f03f3\transformed\expo.modules.network-7.1.5\AndroidManifest.xml:7:22-73
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:7:5-81
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:27:5-81
MERGED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:27:5-81
	android:name
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:7:22-78
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:8:5-77
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\380599f20dc879b965c86746b2c44cf2\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:23:5-77
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\380599f20dc879b965c86746b2c44cf2\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:23:5-77
	android:name
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:8:22-74
service#expo.modules.notifications.service.ExpoFirebaseMessagingService
ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:11:9-17:19
	android:exported
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:13:13-37
	android:name
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:12:13-91
intent-filter#action:name:com.google.firebase.MESSAGING_EVENT
ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:14:13-16:29
	android:priority
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:14:28-49
action#com.google.firebase.MESSAGING_EVENT
ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:15:17-78
	android:name
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:15:25-75
receiver#expo.modules.notifications.service.NotificationsService
ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:19:9-31:20
	android:enabled
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:21:13-35
	android:exported
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:22:13-37
	android:name
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:20:13-83
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.MY_PACKAGE_REPLACED+action:name:android.intent.action.QUICKBOOT_POWERON+action:name:android.intent.action.REBOOT+action:name:com.htc.intent.action.QUICKBOOT_POWERON+action:name:expo.modules.notifications.NOTIFICATION_EVENT
ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:23:13-30:29
	android:priority
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:23:28-49
action#expo.modules.notifications.NOTIFICATION_EVENT
ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:24:17-88
	android:name
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:24:25-85
action#android.intent.action.BOOT_COMPLETED
ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:25:17-79
	android:name
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:25:25-76
action#android.intent.action.REBOOT
ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:26:17-71
	android:name
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:26:25-68
action#android.intent.action.QUICKBOOT_POWERON
ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:27:17-82
	android:name
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:27:25-79
action#com.htc.intent.action.QUICKBOOT_POWERON
ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:28:17-82
	android:name
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:28:25-79
action#android.intent.action.MY_PACKAGE_REPLACED
ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:29:17-84
	android:name
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:29:25-81
activity#expo.modules.notifications.service.NotificationForwarderActivity
ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:33:9-40:75
	android:excludeFromRecents
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:35:13-46
	android:launchMode
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:37:13-42
	android:noHistory
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:38:13-37
	android:exported
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:36:13-37
	android:theme
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:40:13-72
	android:taskAffinity
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:39:13-36
	android:name
		ADDED from [host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:34:13-92
intent#action:name:android.intent.action.SEND+data:mimeType:*/*
ADDED from [host.exp.exponent:expo.modules.sms:13.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\0531343fe10d3c1dea07e7b068516664\transformed\expo.modules.sms-13.1.4\AndroidManifest.xml:8:9-14:18
action#android.intent.action.SEND
ADDED from [host.exp.exponent:expo.modules.sms:13.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\0531343fe10d3c1dea07e7b068516664\transformed\expo.modules.sms-13.1.4\AndroidManifest.xml:11:13-65
	android:name
		ADDED from [host.exp.exponent:expo.modules.sms:13.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\0531343fe10d3c1dea07e7b068516664\transformed\expo.modules.sms-13.1.4\AndroidManifest.xml:11:21-62
intent#action:name:android.intent.action.SENDTO+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:scheme:sms
ADDED from [host.exp.exponent:expo.modules.sms:13.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\0531343fe10d3c1dea07e7b068516664\transformed\expo.modules.sms-13.1.4\AndroidManifest.xml:16:9-23:18
action#android.intent.action.SENDTO
ADDED from [host.exp.exponent:expo.modules.sms:13.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\0531343fe10d3c1dea07e7b068516664\transformed\expo.modules.sms-13.1.4\AndroidManifest.xml:17:13-67
	android:name
		ADDED from [host.exp.exponent:expo.modules.sms:13.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\0531343fe10d3c1dea07e7b068516664\transformed\expo.modules.sms-13.1.4\AndroidManifest.xml:17:21-64
intent#action:name:android.intent.action.SENDTO+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:scheme:smsto
ADDED from [host.exp.exponent:expo.modules.sms:13.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\0531343fe10d3c1dea07e7b068516664\transformed\expo.modules.sms-13.1.4\AndroidManifest.xml:24:9-31:18
intent#action:name:android.intent.action.TTS_SERVICE
ADDED from [host.exp.exponent:expo.modules.speech:13.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\6f3f3fe67e7bb5816c905504e02db79c\transformed\expo.modules.speech-13.1.7\AndroidManifest.xml:8:9-12:18
action#android.intent.action.TTS_SERVICE
ADDED from [host.exp.exponent:expo.modules.speech:13.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\6f3f3fe67e7bb5816c905504e02db79c\transformed\expo.modules.speech-13.1.7\AndroidManifest.xml:11:13-72
	android:name
		ADDED from [host.exp.exponent:expo.modules.speech:13.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\6f3f3fe67e7bb5816c905504e02db79c\transformed\expo.modules.speech-13.1.7\AndroidManifest.xml:11:21-69
intent#action:name:android.intent.action.GET_CONTENT+category:name:android.intent.category.OPENABLE+data:mimeType:*/*
ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8cc367fa8ce0cb8acb1d9a2937a5991c\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:10:9-16:18
action#android.intent.action.GET_CONTENT
ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8cc367fa8ce0cb8acb1d9a2937a5991c\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:11:13-72
	android:name
		ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8cc367fa8ce0cb8acb1d9a2937a5991c\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:11:21-69
provider#com.canhub.cropper.CropFileProvider
ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8cc367fa8ce0cb8acb1d9a2937a5991c\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:23:9-31:20
	android:grantUriPermissions
		ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8cc367fa8ce0cb8acb1d9a2937a5991c\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:27:13-47
	android:authorities
		ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8cc367fa8ce0cb8acb1d9a2937a5991c\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8cc367fa8ce0cb8acb1d9a2937a5991c\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:26:13-37
	android:name
		ADDED from [com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8cc367fa8ce0cb8acb1d9a2937a5991c\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:24:13-63
uses-permission#android.permission.WAKE_LOCK
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:25:5-68
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\380599f20dc879b965c86746b2c44cf2\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:24:5-68
MERGED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\380599f20dc879b965c86746b2c44cf2\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e860328566f0fae28883e3e1c789e1e7\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e860328566f0fae28883e3e1c789e1e7\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:9:5-68
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:25:22-65
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:28:5-77
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:28:22-74
provider#androidx.startup.InitializationProvider
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:31:9-39:20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93defa7eb94a1f1ee72c0ca0f587e7d5\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93defa7eb94a1f1ee72c0ca0f587e7d5\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\5c6a9353de243fb8621eac0d0cef3eb2\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\5c6a9353de243fb8621eac0d0cef3eb2\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\99bcfde82ee67925aa78857cee59febf\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\99bcfde82ee67925aa78857cee59febf\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2fa4ab394b458d75a6f846a795c898cf\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\2fa4ab394b458d75a6f846a795c898cf\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:35:13-31
	android:authorities
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:33:13-68
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:32:13-67
meta-data#androidx.work.WorkManagerInitializer
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:36:13-38:52
	android:value
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:38:17-49
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:37:17-68
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:41:9-46:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:44:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:45:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:46:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:43:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:42:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:47:9-53:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:50:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:51:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:52:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:53:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:49:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:48:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:54:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:57:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:58:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:56:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:55:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:116:13-120:29
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:140:25-85
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d1f7b5139d06634cc3f34d76f8142494\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d1f7b5139d06634cc3f34d76f8142494\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d1f7b5139d06634cc3f34d76f8142494\transformed\room-runtime-2.6.1\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d1f7b5139d06634cc3f34d76f8142494\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d1f7b5139d06634cc3f34d76f8142494\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
intent#action:name:androidx.camera.extensions.action.VENDOR_ACTION
ADDED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\797066c703e3fc2659fba207b49a8e07\transformed\camera-extensions-1.4.1\AndroidManifest.xml:23:9-25:18
action#androidx.camera.extensions.action.VENDOR_ACTION
ADDED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\797066c703e3fc2659fba207b49a8e07\transformed\camera-extensions-1.4.1\AndroidManifest.xml:24:13-86
	android:name
		ADDED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\797066c703e3fc2659fba207b49a8e07\transformed\camera-extensions-1.4.1\AndroidManifest.xml:24:21-83
uses-library#androidx.camera.extensions.impl
ADDED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\797066c703e3fc2659fba207b49a8e07\transformed\camera-extensions-1.4.1\AndroidManifest.xml:29:9-31:40
	android:required
		ADDED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\797066c703e3fc2659fba207b49a8e07\transformed\camera-extensions-1.4.1\AndroidManifest.xml:31:13-37
	android:name
		ADDED from [androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\797066c703e3fc2659fba207b49a8e07\transformed\camera-extensions-1.4.1\AndroidManifest.xml:30:13-59
service#androidx.camera.core.impl.MetadataHolderService
ADDED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\024473314cb477a873a41df1f2dc78f6\transformed\camera-camera2-1.4.1\AndroidManifest.xml:24:9-33:19
MERGED from [androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\036671903f3c086cdacdad857c082bb6\transformed\camera-core-1.4.1\AndroidManifest.xml:29:9-33:78
MERGED from [androidx.camera:camera-core:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\036671903f3c086cdacdad857c082bb6\transformed\camera-core-1.4.1\AndroidManifest.xml:29:9-33:78
	tools:node
		ADDED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\024473314cb477a873a41df1f2dc78f6\transformed\camera-camera2-1.4.1\AndroidManifest.xml:29:13-31
	android:enabled
		ADDED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\024473314cb477a873a41df1f2dc78f6\transformed\camera-camera2-1.4.1\AndroidManifest.xml:26:13-36
	android:exported
		ADDED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\024473314cb477a873a41df1f2dc78f6\transformed\camera-camera2-1.4.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\024473314cb477a873a41df1f2dc78f6\transformed\camera-camera2-1.4.1\AndroidManifest.xml:28:13-75
	android:name
		ADDED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\024473314cb477a873a41df1f2dc78f6\transformed\camera-camera2-1.4.1\AndroidManifest.xml:25:13-75
meta-data#androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER
ADDED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\024473314cb477a873a41df1f2dc78f6\transformed\camera-camera2-1.4.1\AndroidManifest.xml:30:13-32:89
	android:value
		ADDED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\024473314cb477a873a41df1f2dc78f6\transformed\camera-camera2-1.4.1\AndroidManifest.xml:32:17-86
	android:name
		ADDED from [androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\024473314cb477a873a41df1f2dc78f6\transformed\camera-camera2-1.4.1\AndroidManifest.xml:31:17-103
uses-permission#com.google.android.c2dm.permission.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\380599f20dc879b965c86746b2c44cf2\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:26:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e860328566f0fae28883e3e1c789e1e7\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:11:5-82
MERGED from [com.google.android.gms:play-services-cloud-messaging:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\e860328566f0fae28883e3e1c789e1e7\transformed\play-services-cloud-messaging-17.2.0\AndroidManifest.xml:11:5-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\380599f20dc879b965c86746b2c44cf2\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:26:22-79
receiver#com.google.firebase.iid.FirebaseInstanceIdReceiver
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\380599f20dc879b965c86746b2c44cf2\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:29:9-40:20
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\380599f20dc879b965c86746b2c44cf2\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:31:13-36
	android:permission
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\380599f20dc879b965c86746b2c44cf2\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:32:13-73
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\380599f20dc879b965c86746b2c44cf2\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:30:13-78
intent-filter#action:name:com.google.android.c2dm.intent.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\380599f20dc879b965c86746b2c44cf2\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:33:13-35:29
action#com.google.android.c2dm.intent.RECEIVE
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\380599f20dc879b965c86746b2c44cf2\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:34:17-81
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\380599f20dc879b965c86746b2c44cf2\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:34:25-78
meta-data#com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\380599f20dc879b965c86746b2c44cf2\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:37:13-39:40
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\380599f20dc879b965c86746b2c44cf2\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:39:17-37
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\380599f20dc879b965c86746b2c44cf2\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:38:17-92
service#com.google.firebase.messaging.FirebaseMessagingService
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\380599f20dc879b965c86746b2c44cf2\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:46:9-53:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\380599f20dc879b965c86746b2c44cf2\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:49:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\380599f20dc879b965c86746b2c44cf2\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:48:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\380599f20dc879b965c86746b2c44cf2\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:47:13-82
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\380599f20dc879b965c86746b2c44cf2\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:54:9-63:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7a6de7bf0b1470187fab593af8c6930\transformed\firebase-installations-17.2.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7a6de7bf0b1470187fab593af8c6930\transformed\firebase-installations-17.2.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4014643dd64b18ee06fdc929b4f60dfa\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4014643dd64b18ee06fdc929b4f60dfa\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cb6a07ae6c113830fda664a1a3f74969\transformed\firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cb6a07ae6c113830fda664a1a3f74969\transformed\firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4d8cc4ca0e35a89a493aab958b2c0a2c\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:22:9-28:19
MERGED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4d8cc4ca0e35a89a493aab958b2c0a2c\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:22:9-28:19
	android:exported
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\380599f20dc879b965c86746b2c44cf2\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:56:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cb6a07ae6c113830fda664a1a3f74969\transformed\firebase-common-21.0.0\AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cb6a07ae6c113830fda664a1a3f74969\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\380599f20dc879b965c86746b2c44cf2\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:55:13-84
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\380599f20dc879b965c86746b2c44cf2\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:57:13-59:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\380599f20dc879b965c86746b2c44cf2\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:59:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\380599f20dc879b965c86746b2c44cf2\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:58:17-122
meta-data#com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar
ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\380599f20dc879b965c86746b2c44cf2\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:60:13-62:85
	android:value
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\380599f20dc879b965c86746b2c44cf2\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:62:17-82
	android:name
		ADDED from [com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\380599f20dc879b965c86746b2c44cf2\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:61:17-119
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7a6de7bf0b1470187fab593af8c6930\transformed\firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7a6de7bf0b1470187fab593af8c6930\transformed\firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7a6de7bf0b1470187fab593af8c6930\transformed\firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7a6de7bf0b1470187fab593af8c6930\transformed\firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7a6de7bf0b1470187fab593af8c6930\transformed\firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7a6de7bf0b1470187fab593af8c6930\transformed\firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4014643dd64b18ee06fdc929b4f60dfa\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4014643dd64b18ee06fdc929b4f60dfa\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4014643dd64b18ee06fdc929b4f60dfa\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cb6a07ae6c113830fda664a1a3f74969\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cb6a07ae6c113830fda664a1a3f74969\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cb6a07ae6c113830fda664a1a3f74969\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cb6a07ae6c113830fda664a1a3f74969\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cb6a07ae6c113830fda664a1a3f74969\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cb6a07ae6c113830fda664a1a3f74969\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cb6a07ae6c113830fda664a1a3f74969\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cb6a07ae6c113830fda664a1a3f74969\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cb6a07ae6c113830fda664a1a3f74969\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
meta-data#com.google.android.gms.version
ADDED from [com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac0dda40f5036986d8403279f04f501a\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:8:9-10:69
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cea641498eb0b29f919daa30d9671bd0\transformed\play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cea641498eb0b29f919daa30d9671bd0\transformed\play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac0dda40f5036986d8403279f04f501a\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:10:13-66
	android:name
		ADDED from [com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac0dda40f5036986d8403279f04f501a\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:9:13-58
uses-feature#0x00020000
ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d6792621c3f39dfaca06ce80f6b33f1\transformed\play-services-maps-18.2.0\AndroidManifest.xml:26:5-28:35
	android:glEsVersion
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d6792621c3f39dfaca06ce80f6b33f1\transformed\play-services-maps-18.2.0\AndroidManifest.xml:27:9-41
	android:required
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d6792621c3f39dfaca06ce80f6b33f1\transformed\play-services-maps-18.2.0\AndroidManifest.xml:28:9-32
package#com.google.android.apps.maps
ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d6792621c3f39dfaca06ce80f6b33f1\transformed\play-services-maps-18.2.0\AndroidManifest.xml:33:9-64
	android:name
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d6792621c3f39dfaca06ce80f6b33f1\transformed\play-services-maps-18.2.0\AndroidManifest.xml:33:18-61
uses-library#org.apache.http.legacy
ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d6792621c3f39dfaca06ce80f6b33f1\transformed\play-services-maps-18.2.0\AndroidManifest.xml:39:9-41:40
	android:required
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d6792621c3f39dfaca06ce80f6b33f1\transformed\play-services-maps-18.2.0\AndroidManifest.xml:41:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d6792621c3f39dfaca06ce80f6b33f1\transformed\play-services-maps-18.2.0\AndroidManifest.xml:40:13-50
activity#com.google.mlkit.vision.codescanner.internal.GmsBarcodeScanningDelegateActivity
ADDED from [com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7e13feca8295c54dbb8ebd825318ae5\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:15:9-20:20
	android:screenOrientation
		ADDED from [com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7e13feca8295c54dbb8ebd825318ae5\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:18:13-49
	android:exported
		ADDED from [com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7e13feca8295c54dbb8ebd825318ae5\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:17:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7e13feca8295c54dbb8ebd825318ae5\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:19:13-42
	android:name
		ADDED from [com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7e13feca8295c54dbb8ebd825318ae5\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:16:13-107
service#com.google.mlkit.common.internal.MlKitComponentDiscoveryService
ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c482101d03f7fe7ae56d665c96568830\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8a12a52861f5caec9654dea7893e7e4e\transformed\vision-common-17.3.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8a12a52861f5caec9654dea7893e7e4e\transformed\vision-common-17.3.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c9f3d34fca54ef9c7958f49d384f7aba\transformed\common-18.9.0\AndroidManifest.xml:15:9-23:19
MERGED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c9f3d34fca54ef9c7958f49d384f7aba\transformed\common-18.9.0\AndroidManifest.xml:15:9-23:19
	android:exported
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c482101d03f7fe7ae56d665c96568830\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:11:13-37
	tools:targetApi
		ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c9f3d34fca54ef9c7958f49d384f7aba\transformed\common-18.9.0\AndroidManifest.xml:19:13-32
	android:directBootAware
		ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c9f3d34fca54ef9c7958f49d384f7aba\transformed\common-18.9.0\AndroidManifest.xml:17:13-43
	android:name
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c482101d03f7fe7ae56d665c96568830\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:10:13-91
meta-data#com.google.firebase.components:com.google.mlkit.vision.barcode.internal.BarcodeRegistrar
ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c482101d03f7fe7ae56d665c96568830\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c482101d03f7fe7ae56d665c96568830\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c482101d03f7fe7ae56d665c96568830\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:13:17-120
meta-data#com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar
ADDED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8a12a52861f5caec9654dea7893e7e4e\transformed\vision-common-17.3.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8a12a52861f5caec9654dea7893e7e4e\transformed\vision-common-17.3.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8a12a52861f5caec9654dea7893e7e4e\transformed\vision-common-17.3.0\AndroidManifest.xml:13:17-124
provider#com.google.mlkit.common.internal.MlKitInitProvider
ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c9f3d34fca54ef9c7958f49d384f7aba\transformed\common-18.9.0\AndroidManifest.xml:9:9-13:38
	android:authorities
		ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c9f3d34fca54ef9c7958f49d384f7aba\transformed\common-18.9.0\AndroidManifest.xml:11:13-69
	android:exported
		ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c9f3d34fca54ef9c7958f49d384f7aba\transformed\common-18.9.0\AndroidManifest.xml:12:13-37
	android:initOrder
		ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c9f3d34fca54ef9c7958f49d384f7aba\transformed\common-18.9.0\AndroidManifest.xml:13:13-35
	android:name
		ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c9f3d34fca54ef9c7958f49d384f7aba\transformed\common-18.9.0\AndroidManifest.xml:10:13-78
meta-data#com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar
ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c9f3d34fca54ef9c7958f49d384f7aba\transformed\common-18.9.0\AndroidManifest.xml:20:13-22:85
	android:value
		ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c9f3d34fca54ef9c7958f49d384f7aba\transformed\common-18.9.0\AndroidManifest.xml:22:17-82
	android:name
		ADDED from [com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c9f3d34fca54ef9c7958f49d384f7aba\transformed\common-18.9.0\AndroidManifest.xml:21:17-120
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ab0477328e0ae05829c4cc214e49234\transformed\play-services-base-18.2.0\AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ab0477328e0ae05829c4cc214e49234\transformed\play-services-base-18.2.0\AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ab0477328e0ae05829c4cc214e49234\transformed\play-services-base-18.2.0\AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ab0477328e0ae05829c4cc214e49234\transformed\play-services-base-18.2.0\AndroidManifest.xml:20:19-85
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93defa7eb94a1f1ee72c0ca0f587e7d5\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93defa7eb94a1f1ee72c0ca0f587e7d5\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93defa7eb94a1f1ee72c0ca0f587e7d5\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\306a6bcd4bd045cfd61a3c5eb43578e4\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\306a6bcd4bd045cfd61a3c5eb43578e4\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\306a6bcd4bd045cfd61a3c5eb43578e4\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.eliseedev.mientiorlivraison.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\306a6bcd4bd045cfd61a3c5eb43578e4\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\306a6bcd4bd045cfd61a3c5eb43578e4\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\306a6bcd4bd045cfd61a3c5eb43578e4\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\306a6bcd4bd045cfd61a3c5eb43578e4\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\306a6bcd4bd045cfd61a3c5eb43578e4\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.eliseedev.mientiorlivraison.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\306a6bcd4bd045cfd61a3c5eb43578e4\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\306a6bcd4bd045cfd61a3c5eb43578e4\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\5c6a9353de243fb8621eac0d0cef3eb2\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\5c6a9353de243fb8621eac0d0cef3eb2\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\5c6a9353de243fb8621eac0d0cef3eb2\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\99bcfde82ee67925aa78857cee59febf\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\99bcfde82ee67925aa78857cee59febf\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\99bcfde82ee67925aa78857cee59febf\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\99bcfde82ee67925aa78857cee59febf\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\99bcfde82ee67925aa78857cee59febf\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\99bcfde82ee67925aa78857cee59febf\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\99bcfde82ee67925aa78857cee59febf\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\99bcfde82ee67925aa78857cee59febf\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\99bcfde82ee67925aa78857cee59febf\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\99bcfde82ee67925aa78857cee59febf\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\99bcfde82ee67925aa78857cee59febf\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\99bcfde82ee67925aa78857cee59febf\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\99bcfde82ee67925aa78857cee59febf\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\99bcfde82ee67925aa78857cee59febf\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\99bcfde82ee67925aa78857cee59febf\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\99bcfde82ee67925aa78857cee59febf\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\99bcfde82ee67925aa78857cee59febf\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\99bcfde82ee67925aa78857cee59febf\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\99bcfde82ee67925aa78857cee59febf\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\99bcfde82ee67925aa78857cee59febf\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\99bcfde82ee67925aa78857cee59febf\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
meta-data#com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar
ADDED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4d8cc4ca0e35a89a493aab958b2c0a2c\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
	android:value
		ADDED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4d8cc4ca0e35a89a493aab958b2c0a2c\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
	android:name
		ADDED from [com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4d8cc4ca0e35a89a493aab958b2c0a2c\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\2dacf4110cea599a348e4be8992dc6ba\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\1513070d6931c9d145a3a48af88f4f0b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:36:9-38:40
MERGED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\1513070d6931c9d145a3a48af88f4f0b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:36:9-38:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\2dacf4110cea599a348e4be8992dc6ba\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\2dacf4110cea599a348e4be8992dc6ba\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\2dacf4110cea599a348e4be8992dc6ba\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\2dacf4110cea599a348e4be8992dc6ba\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\2dacf4110cea599a348e4be8992dc6ba\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\1513070d6931c9d145a3a48af88f4f0b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\1513070d6931c9d145a3a48af88f4f0b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\1513070d6931c9d145a3a48af88f4f0b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\1513070d6931c9d145a3a48af88f4f0b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\1513070d6931c9d145a3a48af88f4f0b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\1513070d6931c9d145a3a48af88f4f0b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\1513070d6931c9d145a3a48af88f4f0b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
uses-permission#com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE
ADDED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\514d135c1e94e8854cd72d6563419da6\transformed\installreferrer-2.2\AndroidManifest.xml:9:5-110
	android:name
		ADDED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\514d135c1e94e8854cd72d6563419da6\transformed\installreferrer-2.2\AndroidManifest.xml:9:22-107
uses-permission#com.sec.android.provider.badge.permission.READ
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\6329ce871df5828ee152a72c53b9164a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:5-86
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\6329ce871df5828ee152a72c53b9164a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:22-83
uses-permission#com.sec.android.provider.badge.permission.WRITE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\6329ce871df5828ee152a72c53b9164a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:5-87
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\6329ce871df5828ee152a72c53b9164a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:22-84
uses-permission#com.htc.launcher.permission.READ_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\6329ce871df5828ee152a72c53b9164a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:5-81
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\6329ce871df5828ee152a72c53b9164a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:22-78
uses-permission#com.htc.launcher.permission.UPDATE_SHORTCUT
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\6329ce871df5828ee152a72c53b9164a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:5-83
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\6329ce871df5828ee152a72c53b9164a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:22-80
uses-permission#com.sonyericsson.home.permission.BROADCAST_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\6329ce871df5828ee152a72c53b9164a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:5-88
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\6329ce871df5828ee152a72c53b9164a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:22-85
uses-permission#com.sonymobile.home.permission.PROVIDER_INSERT_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\6329ce871df5828ee152a72c53b9164a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:5-92
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\6329ce871df5828ee152a72c53b9164a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:22-89
uses-permission#com.anddoes.launcher.permission.UPDATE_COUNT
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\6329ce871df5828ee152a72c53b9164a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:5-84
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\6329ce871df5828ee152a72c53b9164a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:22-81
uses-permission#com.majeur.launcher.permission.UPDATE_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\6329ce871df5828ee152a72c53b9164a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:5-83
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\6329ce871df5828ee152a72c53b9164a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:22-80
uses-permission#com.huawei.android.launcher.permission.CHANGE_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\6329ce871df5828ee152a72c53b9164a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:5-91
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\6329ce871df5828ee152a72c53b9164a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:22-88
uses-permission#com.huawei.android.launcher.permission.READ_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\6329ce871df5828ee152a72c53b9164a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:5-92
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\6329ce871df5828ee152a72c53b9164a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:22-89
uses-permission#com.huawei.android.launcher.permission.WRITE_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\6329ce871df5828ee152a72c53b9164a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:5-93
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\6329ce871df5828ee152a72c53b9164a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:22-90
uses-permission#android.permission.READ_APP_BADGE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\6329ce871df5828ee152a72c53b9164a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:5-73
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\6329ce871df5828ee152a72c53b9164a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:22-70
uses-permission#com.oppo.launcher.permission.READ_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\6329ce871df5828ee152a72c53b9164a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:5-82
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\6329ce871df5828ee152a72c53b9164a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:22-79
uses-permission#com.oppo.launcher.permission.WRITE_SETTINGS
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\6329ce871df5828ee152a72c53b9164a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:5-83
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\6329ce871df5828ee152a72c53b9164a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:22-80
uses-permission#me.everything.badger.permission.BADGE_COUNT_READ
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\6329ce871df5828ee152a72c53b9164a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:5-88
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\6329ce871df5828ee152a72c53b9164a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:22-85
uses-permission#me.everything.badger.permission.BADGE_COUNT_WRITE
ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\6329ce871df5828ee152a72c53b9164a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:5-89
	android:name
		ADDED from [me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\6329ce871df5828ee152a72c53b9164a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:22-86
