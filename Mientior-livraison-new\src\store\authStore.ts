import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { User, Location, Address } from '../types';
import { authService, supabase, adresseService } from '../services/supabase';

interface AuthState {
  user: User | null;
  session: any;
  loading: boolean;
  isAuthenticated: boolean;
  error: string | null;
  rememberMe: boolean;
  lastActivity: number;
  sessionExpiry: number | null;
  initialized: boolean; // Éviter les multiples initialisations

  // Location and address management
  currentLocation: Location | null;
  locationPermissionGranted: boolean;
  userAddresses: Address[];
  defaultAddress: Address | null;

  // Actions principales
  signIn: (email: string, password: string, rememberMe?: boolean) => Promise<any>;
  signInWithPhone: (phone: string, password: string) => Promise<any>;
  signUp: (email: string, password: string, userData: Partial<User>) => Promise<any>;
  signUpWithPhone: (phone: string, password: string, userData: Partial<User>) => Promise<any>;
  signOut: () => Promise<void>;
  updateProfile: (updates: Partial<User>) => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
  updatePassword: (newPassword: string) => Promise<void>;
  updatePasswordWithToken: (newPassword: string) => Promise<void>;
  verifyOTP: (emailOrPhone: string, token: string, type: 'signup' | 'recovery' | 'sms') => Promise<any>;
  resendOTP: (emailOrPhone: string, type: 'signup' | 'recovery' | 'sms') => Promise<void>;

  // Actions utilitaires
  setUser: (user: User | null) => void;
  setSession: (session: any) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setRememberMe: (remember: boolean) => void;
  updateActivity: () => void;
  checkSessionValidity: () => boolean;
  refreshSession: () => Promise<void>;
  initialize: () => Promise<void>;
  clearError: () => void;

  // Location and address management
  setCurrentLocation: (location: Location | null) => void;
  setLocationPermission: (granted: boolean) => void;
  loadUserAddresses: () => Promise<void>;
  forceReloadAddresses: () => Promise<void>;
  addAddress: (address: Omit<Address, 'id' | 'user_id' | 'created_at' | 'updated_at'>) => Promise<Address>;
  updateAddress: (id: string, updates: Partial<Address>) => Promise<void>;
  deleteAddress: (id: string) => Promise<void>;
  setDefaultAddress: (id: string) => Promise<void>;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      session: null,
      loading: true,
      isAuthenticated: false,
      error: null,
      rememberMe: false,
      lastActivity: Date.now(),
      sessionExpiry: null,
      initialized: false,

      // Location and address management initial state
      currentLocation: null,
      locationPermissionGranted: false,
      userAddresses: [],
      defaultAddress: null,

      signIn: async (email: string, password: string, rememberMe = false) => {
        try {
          set({ loading: true, error: null, lastActivity: Date.now() });

          const result = await authService.signIn(email, password);
          const user = await authService.getCurrentUser();

          // Sauvegarder les préférences "Se souvenir de moi"
          if (rememberMe) {
            await AsyncStorage.setItem('rememberedEmail', email);
            await AsyncStorage.setItem('rememberMe', 'true');
          } else {
            await AsyncStorage.removeItem('rememberedEmail');
            await AsyncStorage.removeItem('rememberMe');
          }

          // Calculer l'expiration de session
          const sessionExpiry = result.session?.expires_at
            ? new Date(result.session.expires_at).getTime()
            : Date.now() + (24 * 60 * 60 * 1000); // 24h par défaut

          set({
            user,
            session: result.session,
            isAuthenticated: !!user,
            loading: false,
            error: null,
            rememberMe,
            sessionExpiry,
            lastActivity: Date.now(),
          });

          return result;
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Erreur de connexion';
          set({
            loading: false,
            error: errorMessage,
            isAuthenticated: false,
            user: null,
            session: null,
            sessionExpiry: null,
          });
          throw error;
        }
      },

      signInWithPhone: async (phone: string, password: string) => {
        try {
          set({ loading: true, error: null, lastActivity: Date.now() });

          const result = await authService.signInWithPhone(phone, password);
          const user = await authService.getCurrentUser();

          const sessionExpiry = result.session?.expires_at
            ? new Date(result.session.expires_at).getTime()
            : Date.now() + (24 * 60 * 60 * 1000);

          set({
            user,
            session: result.session,
            isAuthenticated: !!user,
            loading: false,
            error: null,
            sessionExpiry,
            lastActivity: Date.now(),
          });

          return result;
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Erreur de connexion par téléphone';
          set({
            loading: false,
            error: errorMessage,
            isAuthenticated: false,
            user: null,
            session: null,
            sessionExpiry: null,
          });
          throw error;
        }
      },

      signUp: async (email: string, password: string, userData: Partial<User>) => {
        try {
          set({ loading: true, error: null, lastActivity: Date.now() });

          const result = await authService.signUp(email, password, userData);

          set({
            loading: false,
            error: null,
            lastActivity: Date.now(),
          });

          return result;
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Erreur d\'inscription';
          set({
            loading: false,
            error: errorMessage
          });
          throw error;
        }
      },

      signUpWithPhone: async (phone: string, password: string, userData: Partial<User>) => {
        try {
          set({ loading: true, error: null, lastActivity: Date.now() });

          const result = await authService.signUpWithPhone(phone, password, userData);

          set({
            loading: false,
            error: null,
            lastActivity: Date.now(),
          });

          return result;
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Erreur d\'inscription par téléphone';
          set({
            loading: false,
            error: errorMessage
          });
          throw error;
        }
      },

      signOut: async () => {
        try {
          set({ loading: true, error: null });

          await authService.signOut();

          // Nettoyer les données "Se souvenir de moi" si nécessaire
          const { rememberMe } = get();
          if (!rememberMe) {
            await AsyncStorage.removeItem('rememberedEmail');
            await AsyncStorage.removeItem('rememberMe');
          }

          set({
            user: null,
            session: null,
            isAuthenticated: false,
            loading: false,
            error: null,
          });
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Erreur de déconnexion';
          set({
            loading: false,
            error: errorMessage
          });
          throw error;
        }
      },

      updateProfile: async (updates: Partial<User>) => {
        try {
          set({ loading: true, error: null });
          const { user } = get();
          if (!user) throw new Error('Utilisateur non connecté');

          const updatedUser = await authService.updateProfile(user.id, updates);

          set({
            user: updatedUser,
            loading: false,
            error: null
          });
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Erreur de mise à jour';
          set({
            loading: false,
            error: errorMessage
          });
          throw error;
        }
      },

      resetPassword: async (email: string) => {
        try {
          set({ loading: true, error: null });

          await authService.resetPassword(email);

          set({
            loading: false,
            error: null,
          });
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Erreur de réinitialisation';
          set({
            loading: false,
            error: errorMessage
          });
          throw error;
        }
      },

      updatePassword: async (newPassword: string) => {
        try {
          set({ loading: true, error: null });

          await authService.updatePassword(newPassword);

          set({
            loading: false,
            error: null,
          });
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Erreur de mise à jour du mot de passe';
          set({
            loading: false,
            error: errorMessage
          });
          throw error;
        }
      },

      updatePasswordWithToken: async (newPassword: string) => {
        try {
          set({ loading: true, error: null });

          await authService.updatePasswordWithToken(newPassword);

          set({
            loading: false,
            error: null,
          });
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Erreur de mise à jour du mot de passe';
          set({
            loading: false,
            error: errorMessage
          });
          throw error;
        }
      },

      verifyOTP: async (emailOrPhone: string, token: string, type: 'signup' | 'recovery' | 'sms') => {
        try {
          set({ loading: true, error: null, lastActivity: Date.now() });

          const result = await authService.verifyOTP(emailOrPhone, token, type);

          if (result.user) {
            const user = await authService.getCurrentUser();
            const sessionExpiry = result.session?.expires_at
              ? new Date(result.session.expires_at).getTime()
              : Date.now() + (24 * 60 * 60 * 1000);

            set({
              user,
              session: result.session,
              isAuthenticated: !!user,
              loading: false,
              error: null,
              sessionExpiry,
              lastActivity: Date.now(),
            });
          } else {
            set({
              loading: false,
              error: null,
              lastActivity: Date.now(),
            });
          }

          return result;
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Code de vérification incorrect';
          set({
            loading: false,
            error: errorMessage
          });
          throw error;
        }
      },

      resendOTP: async (emailOrPhone: string, type: 'signup' | 'recovery' | 'sms') => {
        try {
          set({ loading: true, error: null, lastActivity: Date.now() });

          await authService.resendOTP(emailOrPhone, type);

          set({
            loading: false,
            error: null,
            lastActivity: Date.now(),
          });
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Erreur de renvoi du code';
          set({
            loading: false,
            error: errorMessage
          });
          throw error;
        }
      },

      setUser: (user: User | null) => {
        set({
          user,
          isAuthenticated: !!user
        });
      },

      setSession: (session: any) => {
        set({ session });
      },

      setLoading: (loading: boolean) => {
        set({ loading });
      },

      setError: (error: string | null) => {
        set({ error });
      },

      setRememberMe: (remember: boolean) => {
        set({ rememberMe: remember });
      },

      clearError: () => {
        set({ error: null });
      },

      // Location and address management methods
      setCurrentLocation: (location: Location | null) => {
        set({ currentLocation: location });

        // Update user profile with current location if authenticated
        const { user } = get();
        if (user && location) {
          get().updateProfile({ current_location: location });
        }
      },

      setLocationPermission: (granted: boolean) => {
        set({ locationPermissionGranted: granted });

        // Update user profile with permission status
        const { user } = get();
        if (user) {
          get().updateProfile({ location_permission_granted: granted });
        }
      },

      loadUserAddresses: async () => {
        const { user, userAddresses } = get();
        if (!user) return;

        // Éviter les appels répétitifs si les adresses sont déjà chargées
        if (userAddresses.length > 0) {
          console.log('ℹ️ Adresses déjà chargées, ignoré');
          return;
        }

        try {
          set({ loading: true });
          const addresses = await adresseService.getByUser(user.id);
          const defaultAddr = addresses.find(addr => addr.is_default) || null;

          set({
            userAddresses: addresses,
            defaultAddress: defaultAddr,
            loading: false
          });
        } catch (error) {
          console.error('Erreur chargement adresses:', error);
          set({
            error: 'Impossible de charger les adresses',
            loading: false
          });
        }
      },

      forceReloadAddresses: async () => {
        const { user } = get();
        if (!user) return;

        try {
          set({ loading: true, userAddresses: [] }); // Reset addresses first
          const addresses = await adresseService.getByUser(user.id);
          const defaultAddr = addresses.find(addr => addr.is_default) || null;

          set({
            userAddresses: addresses,
            defaultAddress: defaultAddr,
            loading: false
          });
        } catch (error) {
          console.error('Erreur rechargement adresses:', error);
          set({
            error: 'Impossible de recharger les adresses',
            loading: false
          });
        }
      },

      addAddress: async (addressData) => {
        const { user } = get();
        if (!user) throw new Error('Utilisateur non connecté');

        try {
          set({ loading: true });
          const newAddress = await adresseService.create({
            ...addressData,
            user_id: user.id,
          });

          // Reload addresses to get updated list
          await get().forceReloadAddresses();

          return newAddress;
        } catch (error) {
          set({
            error: 'Impossible d\'ajouter l\'adresse',
            loading: false
          });
          throw error;
        }
      },

      updateAddress: async (id: string, updates: Partial<Address>) => {
        try {
          set({ loading: true });
          await adresseService.update(id, updates);

          // Reload addresses to get updated list
          await get().forceReloadAddresses();
        } catch (error) {
          set({
            error: 'Impossible de mettre à jour l\'adresse',
            loading: false
          });
          throw error;
        }
      },

      deleteAddress: async (id: string) => {
        try {
          set({ loading: true });
          await adresseService.delete(id);

          // Reload addresses to get updated list
          await get().forceReloadAddresses();
        } catch (error) {
          set({
            error: 'Impossible de supprimer l\'adresse',
            loading: false
          });
          throw error;
        }
      },

      setDefaultAddress: async (id: string) => {
        try {
          set({ loading: true });
          await adresseService.setDefault(id);

          // Update user profile with new default address
          const { user } = get();
          if (user) {
            await get().updateProfile({ default_address_id: id });
          }

          // Reload addresses to get updated list
          await get().forceReloadAddresses();
        } catch (error) {
          set({
            error: 'Impossible de définir l\'adresse par défaut',
            loading: false
          });
          throw error;
        }
      },

      updateActivity: () => {
        set({ lastActivity: Date.now() });
      },

      checkSessionValidity: () => {
        const { sessionExpiry, lastActivity } = get();
        const now = Date.now();

        // Vérifier l'expiration de session
        if (sessionExpiry && now > sessionExpiry) {
          return false;
        }

        // Vérifier l'inactivité (30 minutes)
        const maxInactivity = 30 * 60 * 1000; // 30 minutes
        if (now - lastActivity > maxInactivity) {
          return false;
        }

        return true;
      },

      refreshSession: async () => {
        try {
          set({ loading: true });

          const { data, error } = await supabase.auth.refreshSession();

          if (error) throw error;

          if (data.session) {
            const sessionExpiry = data.session.expires_at
              ? new Date(data.session.expires_at).getTime()
              : Date.now() + (24 * 60 * 60 * 1000);

            set({
              session: data.session,
              sessionExpiry,
              lastActivity: Date.now(),
              loading: false,
            });
          }
        } catch (error) {
          console.error('Erreur refresh session:', error);
          // Déconnecter l'utilisateur si le refresh échoue
          set({
            user: null,
            session: null,
            isAuthenticated: false,
            sessionExpiry: null,
            loading: false,
          });
        }
      },

      initialize: async () => {
        const { initialized } = get();
        if (initialized) {
          console.log('⚠️ Initialisation déjà effectuée, ignorée');
          return;
        }

        console.log('🔄 Début initialisation auth store...');

        try {
          set({ loading: true });

          // Vérifier la session existante avec timeout RÉDUIT
          console.log('📡 Vérification session Supabase...');
          const sessionPromise = supabase.auth.getSession();
          const timeoutPromise = new Promise((_, reject) =>
            setTimeout(() => reject(new Error('Timeout session après 3 secondes')), 3000)
          );

          const { data: { session }, error } = await Promise.race([
            sessionPromise,
            timeoutPromise
          ]) as any;

          if (error) {
            console.error('❌ Erreur récupération session:', error);

            // Gestion spécifique des erreurs réseau
            if (error.message.includes('Network request failed') ||
                error.message.includes('Timeout') ||
                error.name === 'AuthRetryableFetchError') {
              console.warn('⚠️ Problème réseau lors de l\'initialisation, mode hors ligne');
            }

            set({
              user: null,
              session: null,
              isAuthenticated: false,
              loading: false,
              initialized: true, // Marquer comme initialisé même en cas d'erreur
            });
            return;
          }

          if (session) {
            console.log('✅ Session trouvée, récupération utilisateur...');
            try {
              const userPromise = authService.getCurrentUser();
              const userTimeoutPromise = new Promise((_, reject) =>
                setTimeout(() => reject(new Error('Timeout user')), 5000)
              );

              const user = await Promise.race([
                userPromise,
                userTimeoutPromise
              ]) as any;

              console.log('👤 Utilisateur récupéré:', !!user);
              set({
                user,
                session,
                isAuthenticated: !!user,
                loading: false,
              });
            } catch (userError) {
              console.error('❌ Erreur récupération utilisateur:', userError);
              // Garder la session mais pas l'utilisateur
              set({
                user: null,
                session,
                isAuthenticated: false,
                loading: false,
              });
            }
          } else {
            console.log('ℹ️ Pas de session active');
            set({
              user: null,
              session: null,
              isAuthenticated: false,
              loading: false,
            });
          }

          // Écouter les changements d'authentification (une seule fois)
          console.log('👂 Configuration écoute auth state...');
          supabase.auth.onAuthStateChange(async (event, session) => {
            console.log('🔄 Auth state change:', event, !!session);

            // Ignorer l'événement INITIAL_SESSION pour éviter la boucle
            if (event === 'INITIAL_SESSION') {
              console.log('ℹ️ Événement INITIAL_SESSION ignoré');
              return;
            }

            if (event === 'SIGNED_IN' && session) {
              try {
                const user = await authService.getCurrentUser();
                console.log('👤 Utilisateur connecté:', !!user);
                set({
                  user,
                  session,
                  isAuthenticated: !!user,
                });
              } catch (error) {
                console.error('Erreur lors de la connexion:', error);
                set({
                  user: null,
                  session,
                  isAuthenticated: false,
                });
              }
            } else if (event === 'SIGNED_OUT') {
              console.log('👋 Utilisateur déconnecté');
              set({
                user: null,
                session: null,
                isAuthenticated: false,
                currentLocation: null,
                userAddresses: [],
                defaultAddress: null,
              });
            }
          });

          // Marquer l'initialisation comme terminée
          set({ initialized: true });
          console.log('✅ Initialisation auth terminée');

        } catch (error) {
          console.error('❌ Erreur lors de l\'initialisation:', error);

          // Gestion spécifique des erreurs réseau
          if (error.message.includes('Network request failed') ||
              error.message.includes('Timeout') ||
              error.name === 'AuthRetryableFetchError' ||
              error.name === 'TypeError') {
            console.warn('⚠️ Initialisation auth en mode dégradé (problème réseau)');
          }

          set({
            user: null,
            session: null,
            isAuthenticated: false,
            loading: false,
            initialized: true, // Marquer comme initialisé même en cas d'erreur
          });
        }
      },
    }),
    {
      name: 'auth-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
); 