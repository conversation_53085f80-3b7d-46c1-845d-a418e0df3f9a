// Generated by view binder compiler. Do not edit!
package expo.modules.devlauncher.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.ListView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import expo.modules.devlauncher.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ErrorConsoleFragmentBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ImageButton consoleHomeButton;

  @NonNull
  public final ImageButton consoleReloadButton;

  @NonNull
  public final ListView listView;

  private ErrorConsoleFragmentBinding(@NonNull LinearLayout rootView,
      @NonNull ImageButton consoleHomeButton, @NonNull ImageButton consoleReloadButton,
      @NonNull ListView listView) {
    this.rootView = rootView;
    this.consoleHomeButton = consoleHomeButton;
    this.consoleReloadButton = consoleReloadButton;
    this.listView = listView;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ErrorConsoleFragmentBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ErrorConsoleFragmentBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.error_console_fragment, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ErrorConsoleFragmentBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.console_home_button;
      ImageButton consoleHomeButton = ViewBindings.findChildViewById(rootView, id);
      if (consoleHomeButton == null) {
        break missingId;
      }

      id = R.id.console_reload_button;
      ImageButton consoleReloadButton = ViewBindings.findChildViewById(rootView, id);
      if (consoleReloadButton == null) {
        break missingId;
      }

      id = R.id.list_view;
      ListView listView = ViewBindings.findChildViewById(rootView, id);
      if (listView == null) {
        break missingId;
      }

      return new ErrorConsoleFragmentBinding((LinearLayout) rootView, consoleHomeButton,
          consoleReloadButton, listView);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
