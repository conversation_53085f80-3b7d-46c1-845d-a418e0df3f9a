import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  Dimensions,
  StatusBar,
} from 'react-native';
import MapView, {
  <PERSON><PERSON>,
  <PERSON>yline,
  PROVIDER_GOOGLE,
  Region,
  Callout,
  MapPressEvent
} from 'react-native-maps';
import { Ionicons } from '@expo/vector-icons';
import { SafeAreaView } from 'react-native-safe-area-context';

import { locationService } from '../../services/locationService';
import { restaurantService, Restaurant } from '../../services/restaurantService';
import { directionsService, RouteCoordinate } from '../../services/directionsService';
import { MAPS_CONFIG } from '../../config/environment';
import { useAuthStore } from '../../store/authStore';
import RestaurantMarker from '../../components/RestaurantMarker';
import { useMapRestaurants, RestaurantMarkerData } from '../../hooks/useMapRestaurants';
import { theme } from '../../constants/theme';

const { width, height } = Dimensions.get('window');

interface MapScreenProps {
  navigation: any;
  route?: {
    params?: {
      selectedRestaurant?: Restaurant;
      showRoute?: boolean;
    };
  };
}



// Style personnalisé pour la carte - Thème Africain Mientior
const mapStyle = [
  {
    "elementType": "geometry",
    "stylers": [
      {
        "color": "#f5f5f5"
      }
    ]
  },
  {
    "elementType": "labels.icon",
    "stylers": [
      {
        "visibility": "off"
      }
    ]
  },
  {
    "elementType": "labels.text.fill",
    "stylers": [
      {
        "color": "#616161"
      }
    ]
  },
  {
    "elementType": "labels.text.stroke",
    "stylers": [
      {
        "color": "#f5f5f5"
      }
    ]
  },
  {
    "featureType": "administrative.land_parcel",
    "elementType": "labels.text.fill",
    "stylers": [
      {
        "color": "#bdbdbd"
      }
    ]
  },
  {
    "featureType": "poi",
    "elementType": "geometry",
    "stylers": [
      {
        "color": "#eeeeee"
      }
    ]
  },
  {
    "featureType": "poi",
    "elementType": "labels.text.fill",
    "stylers": [
      {
        "color": "#757575"
      }
    ]
  },
  {
    "featureType": "poi.park",
    "elementType": "geometry",
    "stylers": [
      {
        "color": "#e5f3e0"
      }
    ]
  },
  {
    "featureType": "poi.park",
    "elementType": "labels.text.fill",
    "stylers": [
      {
        "color": "#4CAF50"
      }
    ]
  },
  {
    "featureType": "road",
    "elementType": "geometry",
    "stylers": [
      {
        "color": "#ffffff"
      }
    ]
  },
  {
    "featureType": "road.arterial",
    "elementType": "labels.text.fill",
    "stylers": [
      {
        "color": "#757575"
      }
    ]
  },
  {
    "featureType": "road.highway",
    "elementType": "geometry",
    "stylers": [
      {
        "color": "#dadada"
      }
    ]
  },
  {
    "featureType": "road.highway",
    "elementType": "labels.text.fill",
    "stylers": [
      {
        "color": "#616161"
      }
    ]
  },
  {
    "featureType": "road.local",
    "elementType": "labels.text.fill",
    "stylers": [
      {
        "color": "#9e9e9e"
      }
    ]
  },
  {
    "featureType": "transit.line",
    "elementType": "geometry",
    "stylers": [
      {
        "color": "#e5e5e5"
      }
    ]
  },
  {
    "featureType": "transit.station",
    "elementType": "geometry",
    "stylers": [
      {
        "color": "#eeeeee"
      }
    ]
  },
  {
    "featureType": "water",
    "elementType": "geometry",
    "stylers": [
      {
        "color": "#4ECDC4"
      }
    ]
  },
  {
    "featureType": "water",
    "elementType": "labels.text.fill",
    "stylers": [
      {
        "color": "#0DCAA8"
      }
    ]
  }
];

const MapScreen: React.FC<MapScreenProps> = ({ navigation, route }) => {
  const { user } = useAuthStore();
  const mapRef = useRef<MapView>(null);

  // États
  const [currentLocation, setCurrentLocation] = useState<{ latitude: number; longitude: number } | null>(null);
  const [selectedRestaurant, setSelectedRestaurant] = useState<RestaurantMarkerData | null>(
    route?.params?.selectedRestaurant || null
  );
  const [routeCoordinates, setRouteCoordinates] = useState<RouteCoordinate[]>([]);
  const [loading, setLoading] = useState(true);
  const [loadingRoute, setLoadingRoute] = useState(false);
  const [filterType, setFilterType] = useState<string | null>(null);
  const [region, setRegion] = useState<Region>(MAPS_CONFIG.defaultRegion);
  const [locationError, setLocationError] = useState<string | null>(null);
  const [isMapReady, setIsMapReady] = useState(false);

  // Hook optimisé pour les restaurants
  const {
    restaurants,
    loading: loadingRestaurants,
    error: restaurantsError,
    refreshRestaurants,
    clearError: clearRestaurantsError,
  } = useMapRestaurants({ currentLocation, filterType });

  // Log des changements de restaurants
  useEffect(() => {
    console.log('🗺️ MapScreen: Restaurants mis à jour:', {
      count: restaurants.length,
      loading: loadingRestaurants,
      error: restaurantsError,
      currentLocation,
      filterType
    });
  }, [restaurants, loadingRestaurants, restaurantsError, currentLocation, filterType]);

  // Utilisation du thème global
  const colors = theme.colors;
  const styles = createStyles(colors);

  // Types de restaurants avec icônes
  const restaurantTypes = {
    restaurant: { icon: 'restaurant', color: colors.primary },
    colis: { icon: 'cube', color: colors.secondary },
    marchandises: { icon: 'storefront', color: colors.accent },
    pharmacie: { icon: 'medical', color: colors.success },
    epicerie: { icon: 'basket', color: colors.warning },
    autres: { icon: 'business', color: colors.text.secondary },
  };

  // Initialisation
  useEffect(() => {
    console.log('🗺️ MapScreen: Composant monté, initialisation...');
    initializeMap();
  }, []);



  // Afficher la route si un restaurant est sélectionné
  useEffect(() => {
    if (selectedRestaurant && currentLocation && route?.params?.showRoute) {
      calculateRoute(selectedRestaurant);
    }
  }, [selectedRestaurant, currentLocation]);

  // Ajuster la vue quand les restaurants changent
  useEffect(() => {
    if (restaurants.length > 0 && currentLocation) {
      setTimeout(() => fitToRestaurants(restaurants), 500);
    }
  }, [restaurants, currentLocation]);

  const initializeMap = async () => {
    console.log('🗺️ MapScreen: Initialisation de la carte...');
    try {
      setLoading(true);
      setLocationError(null);

      console.log('📍 MapScreen: Demande de géolocalisation...');
      // Obtenir la position actuelle
      const location = await locationService.getCurrentLocation();
      console.log('📍 MapScreen: Position obtenue:', location);

      if (location) {
        const newRegion = {
          latitude: location.latitude,
          longitude: location.longitude,
          latitudeDelta: 0.01,
          longitudeDelta: 0.01,
        };

        console.log('📍 MapScreen: Nouvelle région définie:', newRegion);
        setCurrentLocation({ latitude: location.latitude, longitude: location.longitude });
        setRegion(newRegion);

        // Centrer la carte sur la position avec un délai pour éviter les conflits
        setTimeout(() => {
          console.log('📍 MapScreen: Animation vers la nouvelle région...');
          mapRef.current?.animateToRegion(newRegion, 1000);
        }, 300);
      } else {
        console.log('❌ MapScreen: Aucune position obtenue');
      }
    } catch (error) {
      console.error('❌ MapScreen: Erreur initialisation carte:', error);
      setLocationError('Impossible d\'obtenir votre position');
      // Utiliser la position par défaut en cas d'erreur
      console.log('📍 MapScreen: Utilisation de la région par défaut:', MAPS_CONFIG.defaultRegion);
      setRegion(MAPS_CONFIG.defaultRegion);
    } finally {
      console.log('🗺️ MapScreen: Fin de l\'initialisation, loading=false');
      setLoading(false);
    }
  };



  const fitToRestaurants = (restaurantList: RestaurantMarkerData[]) => {
    if (!currentLocation || restaurantList.length === 0) return;

    const coordinates = [
      currentLocation,
      ...restaurantList.map(r => r.coordinates!),
    ];

    mapRef.current?.fitToCoordinates(coordinates, {
      edgePadding: { top: 100, right: 50, bottom: 100, left: 50 },
      animated: true,
    });
  };

  const calculateRoute = async (restaurant: RestaurantMarkerData) => {
    if (!currentLocation || !restaurant.coordinates) return;

    try {
      setLoadingRoute(true);
      
      const route = await directionsService.getDirections(
        currentLocation,
        restaurant.coordinates,
        { mode: 'driving' }
      );

      if (route) {
        const coordinates = directionsService.decodePolyline(route.polyline.points);
        setRouteCoordinates(coordinates);
        
        // Mettre à jour les informations du restaurant
        setSelectedRestaurant({
          ...restaurant,
          routeCoordinates: coordinates,
          distance: route.distance.value,
          duration: route.duration.value,
        });

        // Ajuster la vue pour inclure la route
        mapRef.current?.fitToCoordinates([currentLocation, restaurant.coordinates], {
          edgePadding: { top: 100, right: 50, bottom: 200, left: 50 },
          animated: true,
        });
      }
    } catch (error) {
      console.error('❌ Erreur calcul itinéraire:', error);
      Alert.alert('Erreur', 'Impossible de calculer l\'itinéraire');
    } finally {
      setLoadingRoute(false);
    }
  };

  const centerOnUserLocation = () => {
    if (currentLocation) {
      const newRegion = {
        ...currentLocation,
        latitudeDelta: 0.01,
        longitudeDelta: 0.01,
      };
      mapRef.current?.animateToRegion(newRegion, 1000);
    }
  };

  const onMarkerPress = (restaurant: RestaurantMarkerData) => {
    setSelectedRestaurant(restaurant);
    calculateRoute(restaurant);
  };

  const onCalloutPress = (restaurant: RestaurantMarkerData) => {
    navigation.navigate('Establishment', { restaurant });
  };

  const renderFilterButtons = () => (
    <View style={styles.filterContainer}>
      <TouchableOpacity
        style={[styles.filterButton, !filterType && styles.filterButtonActive]}
        onPress={() => setFilterType(null)}
      >
        <Text style={[styles.filterText, !filterType && styles.filterTextActive]}>
          Tous
        </Text>
      </TouchableOpacity>
      
      {Object.entries(restaurantTypes).map(([type, config]) => (
        <TouchableOpacity
          key={type}
          style={[styles.filterButton, filterType === type && styles.filterButtonActive]}
          onPress={() => setFilterType(filterType === type ? null : type)}
        >
          <Ionicons
            name={config.icon as any}
            size={16}
            color={filterType === type ? colors.background.primary : config.color}
          />
        </TouchableOpacity>
      ))}
    </View>
  );

  if (loading) {
    console.log('🗺️ MapScreen: Affichage de l\'écran de chargement');
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={styles.loadingText}>Chargement de la carte...</Text>
        <Text style={[styles.loadingText, { fontSize: 12, marginTop: 8 }]}>
          Région: {region.latitude.toFixed(4)}, {region.longitude.toFixed(4)}
        </Text>
      </View>
    );
  }

  console.log('🗺️ MapScreen: Rendu de la carte principale', {
    region,
    currentLocation,
    restaurantsCount: restaurants.length,
    loading,
    loadingRestaurants
  });

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={colors.background.primary} />
      
      {/* En-tête */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color={colors.text.primary} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Restaurants à proximité</Text>
        <View style={styles.headerRight} />
      </View>

      {/* Filtres */}
      {renderFilterButtons()}

      {/* Carte */}
      <View style={styles.mapContainer}>
        {/* Diagnostic de la carte */}
        <View style={styles.diagnosticOverlay}>
          <Text style={styles.diagnosticText}>
            📍 Région: {region.latitude.toFixed(4)}, {region.longitude.toFixed(4)}
          </Text>
          <Text style={styles.diagnosticText}>
            🗺️ API Key: {process.env.GOOGLE_MAPS_API_KEY ? '✅ Configurée' : '❌ Manquante'}
          </Text>
          <Text style={styles.diagnosticText}>
            📱 Provider: {PROVIDER_GOOGLE}
          </Text>
        </View>

        <MapView
          ref={mapRef}
          provider={PROVIDER_GOOGLE}
          style={styles.map}
          initialRegion={{
            latitude: 5.3364, // Abidjan, Côte d'Ivoire
            longitude: -4.0267,
            latitudeDelta: 0.0922,
            longitudeDelta: 0.0421,
          }}
          showsUserLocation={true}
          showsMyLocationButton={false}
          loadingEnabled={true}
          onMapReady={() => {
            console.log('🗺️ MapScreen: MapView prête !');
            setIsMapReady(true);
          }}
          onLayout={() => {
            console.log('🗺️ MapScreen: MapView layout terminé');
          }}
        >
          {/* Marqueurs des restaurants */}
          {restaurants.map((restaurant) => (
            <Marker
              key={restaurant.id}
              coordinate={restaurant.coordinates!}
              onPress={() => onMarkerPress(restaurant)}
            >
              <RestaurantMarker
                businessType={restaurant.business_type}
                isSelected={selectedRestaurant?.id === restaurant.id}
                rating={restaurant.rating}
                size="medium"
              />

              <Callout onPress={() => onCalloutPress(restaurant)}>
                <View style={styles.calloutContainer}>
                  <Text style={styles.calloutTitle}>{restaurant.business_name}</Text>
                  <Text style={styles.calloutSubtitle}>⭐ {restaurant.rating.toFixed(1)}</Text>
                  {restaurant.distance && (
                    <Text style={styles.calloutDistance}>
                      📍 {directionsService.formatDistance(restaurant.distance)}
                    </Text>
                  )}
                  {restaurant.duration && (
                    <Text style={styles.calloutDuration}>
                      🚗 {directionsService.formatDuration(restaurant.duration)}
                    </Text>
                  )}
                  <Text style={styles.calloutAction}>Appuyez pour voir les détails</Text>
                </View>
              </Callout>
            </Marker>
          ))}

          {/* Route */}
          {routeCoordinates.length > 0 && (
            <Polyline
              coordinates={routeCoordinates}
              strokeColor={colors.primary}
              strokeWidth={4}
              lineDashPattern={[5, 5]}
            />
          )}
        </MapView>

        {/* Bouton de recentrage */}
        <TouchableOpacity style={styles.centerButton} onPress={centerOnUserLocation}>
          <Ionicons name="locate" size={24} color={colors.primary} />
        </TouchableOpacity>

        {/* Indicateur de chargement de route */}
        {loadingRoute && (
          <View style={styles.routeLoadingContainer}>
            <ActivityIndicator size="small" color="#0DCAA8" />
            <Text style={styles.routeLoadingText}>Calcul de l'itinéraire...</Text>
          </View>
        )}

        {/* Indicateur de chargement des restaurants */}
        {loadingRestaurants && (
          <View style={styles.restaurantLoadingContainer}>
            <ActivityIndicator size="small" color="#0DCAA8" />
            <Text style={styles.restaurantLoadingText}>Chargement des restaurants...</Text>
          </View>
        )}

        {/* État vide - Aucun restaurant trouvé */}
        {!loadingRestaurants && !restaurantsError && restaurants.length === 0 && currentLocation && (
          <View style={styles.emptyStateContainer}>
            <Ionicons name="restaurant-outline" size={48} color="#7F8C8D" />
            <Text style={styles.emptyStateTitle}>Aucun restaurant trouvé</Text>
            <Text style={styles.emptyStateSubtitle}>
              {filterType
                ? `Aucun ${filterType} disponible dans cette zone`
                : 'Aucun restaurant disponible dans cette zone'
              }
            </Text>
            <TouchableOpacity
              style={styles.emptyStateButton}
              onPress={() => {
                setFilterType(null);
                refreshRestaurants();
              }}
            >
              <Text style={styles.emptyStateButtonText}>
                {filterType ? 'Voir tous les restaurants' : 'Actualiser'}
              </Text>
            </TouchableOpacity>
          </View>
        )}

        {/* Message d'erreur de géolocalisation */}
        {locationError && (
          <View style={styles.errorContainer}>
            <Ionicons name="alert-circle" size={20} color="#E74C3C" />
            <Text style={styles.errorText}>{locationError}</Text>
            <TouchableOpacity
              style={styles.retryButton}
              onPress={() => {
                setLocationError(null);
                initializeMap();
              }}
            >
              <Text style={styles.retryButtonText}>Réessayer</Text>
            </TouchableOpacity>
          </View>
        )}

        {/* Message d'erreur des restaurants */}
        {restaurantsError && (
          <View style={[styles.errorContainer, { top: 140 }]}>
            <Ionicons name="alert-circle" size={20} color="#E74C3C" />
            <Text style={styles.errorText}>{restaurantsError}</Text>
            <TouchableOpacity
              style={styles.retryButton}
              onPress={() => {
                clearRestaurantsError();
                refreshRestaurants();
              }}
            >
              <Text style={styles.retryButtonText}>Réessayer</Text>
            </TouchableOpacity>
          </View>
        )}
      </View>

      {/* Informations du restaurant sélectionné */}
      {selectedRestaurant && (
        <View style={styles.selectedRestaurantContainer}>
          <View style={styles.selectedRestaurantInfo}>
            <Text style={styles.selectedRestaurantName}>{selectedRestaurant.business_name}</Text>
            <Text style={styles.selectedRestaurantType}>
              {selectedRestaurant.business_type.charAt(0).toUpperCase() + selectedRestaurant.business_type.slice(1)}
            </Text>
            
            <View style={styles.selectedRestaurantStats}>
              <Text style={styles.selectedRestaurantStat}>⭐ {selectedRestaurant.rating.toFixed(1)}</Text>
              {selectedRestaurant.distance && (
                <Text style={styles.selectedRestaurantStat}>
                  📍 {directionsService.formatDistance(selectedRestaurant.distance)}
                </Text>
              )}
              {selectedRestaurant.duration && (
                <Text style={styles.selectedRestaurantStat}>
                  🚗 {directionsService.formatDuration(selectedRestaurant.duration)}
                </Text>
              )}
            </View>
          </View>
          
          <TouchableOpacity
            style={styles.selectedRestaurantButton}
            onPress={() => onCalloutPress(selectedRestaurant)}
          >
            <Text style={styles.selectedRestaurantButtonText}>Voir le menu</Text>
          </TouchableOpacity>
        </View>
      )}

      {/* Légende */}
      <View style={styles.legendContainer}>
        <Text style={styles.legendTitle}>Légende</Text>
        <View style={styles.legendItems}>
          {Object.entries(restaurantTypes).slice(0, 3).map(([type, config]) => (
            <View key={type} style={styles.legendItem}>
              <View style={[styles.legendMarker, { backgroundColor: config.color }]}>
                <Ionicons name={config.icon as any} size={12} color={colors.background.primary} />
              </View>
              <Text style={styles.legendText}>
                {type.charAt(0).toUpperCase() + type.slice(1)}
              </Text>
            </View>
          ))}
        </View>
      </View>
    </SafeAreaView>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.background.primary,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: colors.text.secondary,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: colors.background.primary,
    borderBottomWidth: 1,
    borderBottomColor: colors.border.light,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text.primary,
  },
  headerRight: {
    width: 40,
  },
  filterContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: colors.background.primary,
    borderBottomWidth: 1,
    borderBottomColor: colors.border.light,
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 8,
    borderRadius: 20,
    backgroundColor: colors.background.secondary,
    borderWidth: 1,
    borderColor: colors.border.light,
  },
  filterButtonActive: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  filterText: {
    fontSize: 14,
    color: colors.text.secondary,
    fontWeight: '500',
  },
  filterTextActive: {
    color: colors.text.inverse,
  },
  mapContainer: {
    flex: 1,
    position: 'relative',
  },
  map: {
    flex: 1,
  },

  calloutContainer: {
    width: 200,
    padding: 12,
  },
  calloutTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text.primary,
    marginBottom: 4,
  },
  calloutSubtitle: {
    fontSize: 14,
    color: colors.text.secondary,
    marginBottom: 2,
  },
  calloutDistance: {
    fontSize: 12,
    color: colors.text.secondary,
    marginBottom: 2,
  },
  calloutDuration: {
    fontSize: 12,
    color: colors.text.secondary,
    marginBottom: 8,
  },
  calloutAction: {
    fontSize: 12,
    color: colors.primary,
    fontWeight: '500',
  },
  centerButton: {
    position: 'absolute',
    top: 16,
    right: 16,
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: colors.background.primary,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  routeLoadingContainer: {
    position: 'absolute',
    top: 80,
    left: 16,
    right: 16,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background.primary,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  routeLoadingText: {
    marginLeft: 8,
    fontSize: 14,
    color: colors.text.secondary,
  },
  restaurantLoadingContainer: {
    position: 'absolute',
    top: 140,
    left: 16,
    right: 16,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background.primary,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  restaurantLoadingText: {
    marginLeft: 8,
    fontSize: 14,
    color: colors.text.secondary,
  },
  errorContainer: {
    position: 'absolute',
    top: 80,
    left: 16,
    right: 16,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFF5F5',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#FEB2B2',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  errorText: {
    flex: 1,
    marginLeft: 8,
    fontSize: 14,
    color: colors.error,
  },
  retryButton: {
    backgroundColor: colors.error,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    marginLeft: 8,
  },
  retryButtonText: {
    color: colors.text.inverse,
    fontSize: 12,
    fontWeight: '600',
  },
  emptyStateContainer: {
    position: 'absolute',
    top: '50%',
    left: 16,
    right: 16,
    transform: [{ translateY: -100 }],
    backgroundColor: colors.background.primary,
    paddingHorizontal: 24,
    paddingVertical: 32,
    borderRadius: 12,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  emptyStateTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.text.primary,
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  emptyStateSubtitle: {
    fontSize: 14,
    color: colors.text.secondary,
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 20,
  },
  emptyStateButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  emptyStateButtonText: {
    color: colors.text.inverse,
    fontSize: 14,
    fontWeight: '600',
  },
  selectedRestaurantContainer: {
    backgroundColor: colors.background.primary,
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderTopWidth: 1,
    borderTopColor: colors.border.light,
    flexDirection: 'row',
    alignItems: 'center',
  },
  selectedRestaurantInfo: {
    flex: 1,
  },
  selectedRestaurantName: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.text.primary,
    marginBottom: 4,
  },
  selectedRestaurantType: {
    fontSize: 14,
    color: colors.text.secondary,
    marginBottom: 8,
  },
  selectedRestaurantStats: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  selectedRestaurantStat: {
    fontSize: 12,
    color: colors.text.secondary,
    marginRight: 16,
  },
  selectedRestaurantButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
  },
  selectedRestaurantButtonText: {
    color: colors.text.inverse,
    fontSize: 14,
    fontWeight: '600',
  },
  legendContainer: {
    position: 'absolute',
    bottom: 16,
    left: 16,
    backgroundColor: colors.background.primary,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  legendTitle: {
    fontSize: 12,
    fontWeight: '600',
    color: colors.text.primary,
    marginBottom: 8,
  },
  legendItems: {
    flexDirection: 'row',
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 12,
  },
  legendMarker: {
    width: 16,
    height: 16,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 4,
  },
  legendText: {
    fontSize: 10,
    color: colors.text.secondary,
  },
  diagnosticOverlay: {
    position: 'absolute',
    top: 10,
    left: 10,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    padding: 8,
    borderRadius: 6,
    zIndex: 1000,
  },
  diagnosticText: {
    fontSize: 10,
    color: '#FFFFFF',
    marginBottom: 2,
  },
});

export default MapScreen;
