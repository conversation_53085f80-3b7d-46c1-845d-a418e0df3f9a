import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  Dimensions,
  StatusBar,
} from 'react-native';
import MapView, { 
  <PERSON>er, 
  Polyline, 
  PROVIDER_GOOGLE, 
  Region,
  Callout,
  MapPressEvent 
} from 'react-native-maps';
import { Ionicons } from '@expo/vector-icons';
import { SafeAreaView } from 'react-native-safe-area-context';

import { locationService } from '../../services/locationService';
import { restaurantService, Restaurant } from '../../services/restaurantService';
import { directionsService, RouteCoordinate } from '../../services/directionsService';
import { MAPS_CONFIG } from '../../config/environment';
import { useAuthStore } from '../../store/authStore';
import RestaurantMarker from '../../components/RestaurantMarker';

const { width, height } = Dimensions.get('window');

interface MapScreenProps {
  navigation: any;
  route?: {
    params?: {
      selectedRestaurant?: Restaurant;
      showRoute?: boolean;
    };
  };
}

interface RestaurantMarkerData extends Restaurant {
  routeCoordinates?: RouteCoordinate[];
  distance?: number;
  duration?: number;
}

const MapScreen: React.FC<MapScreenProps> = ({ navigation, route }) => {
  const { user } = useAuthStore();
  const mapRef = useRef<MapView>(null);

  // États
  const [currentLocation, setCurrentLocation] = useState<{ latitude: number; longitude: number } | null>(null);
  const [restaurants, setRestaurants] = useState<RestaurantMarkerData[]>([]);
  const [selectedRestaurant, setSelectedRestaurant] = useState<RestaurantMarkerData | null>(
    route?.params?.selectedRestaurant || null
  );
  const [routeCoordinates, setRouteCoordinates] = useState<RouteCoordinate[]>([]);
  const [loading, setLoading] = useState(true);
  const [loadingRoute, setLoadingRoute] = useState(false);
  const [filterType, setFilterType] = useState<string | null>(null);
  const [region, setRegion] = useState<Region>(MAPS_CONFIG.defaultRegion);

  // Couleurs du design system africain
  const colors = {
    primary: '#0DCAA8',
    secondary: '#FF6B35',
    tertiary: '#4ECDC4',
    background: '#FFFFFF',
    text: '#2C3E50',
    textLight: '#7F8C8D',
    success: '#27AE60',
    warning: '#F39C12',
    error: '#E74C3C',
  };

  // Types de restaurants avec icônes
  const restaurantTypes = {
    restaurant: { icon: 'restaurant', color: colors.primary },
    colis: { icon: 'cube', color: colors.secondary },
    marchandises: { icon: 'storefront', color: colors.tertiary },
    pharmacie: { icon: 'medical', color: colors.success },
    epicerie: { icon: 'basket', color: colors.warning },
    autres: { icon: 'business', color: colors.textLight },
  };

  // Initialisation
  useEffect(() => {
    initializeMap();
  }, []);

  // Charger les restaurants quand la position change
  useEffect(() => {
    if (currentLocation) {
      loadRestaurants();
    }
  }, [currentLocation, filterType]);

  // Afficher la route si un restaurant est sélectionné
  useEffect(() => {
    if (selectedRestaurant && currentLocation && route?.params?.showRoute) {
      calculateRoute(selectedRestaurant);
    }
  }, [selectedRestaurant, currentLocation]);

  const initializeMap = async () => {
    try {
      setLoading(true);
      
      // Obtenir la position actuelle
      const location = await locationService.getCurrentLocation();
      if (location) {
        const newRegion = {
          latitude: location.latitude,
          longitude: location.longitude,
          latitudeDelta: 0.01,
          longitudeDelta: 0.01,
        };
        
        setCurrentLocation({ latitude: location.latitude, longitude: location.longitude });
        setRegion(newRegion);
        
        // Centrer la carte sur la position
        mapRef.current?.animateToRegion(newRegion, 1000);
      }
    } catch (error) {
      console.error('❌ Erreur initialisation carte:', error);
      Alert.alert('Erreur', 'Impossible d\'obtenir votre position');
    } finally {
      setLoading(false);
    }
  };

  const loadRestaurants = async () => {
    if (!currentLocation) return;

    try {
      const filters = filterType ? { business_type: filterType as any } : undefined;
      const restaurantList = await restaurantService.getRestaurants({
        ...filters,
        user_location: currentLocation,
        is_open: true,
        is_verified: true,
      });

      // Ajouter les coordonnées par défaut si manquantes
      const restaurantsWithCoords = restaurantList.map(restaurant => ({
        ...restaurant,
        coordinates: restaurant.coordinates || {
          latitude: MAPS_CONFIG.defaultRegion.latitude + (Math.random() - 0.5) * 0.02,
          longitude: MAPS_CONFIG.defaultRegion.longitude + (Math.random() - 0.5) * 0.02,
        },
      }));

      setRestaurants(restaurantsWithCoords);
      
      // Ajuster la vue pour inclure tous les restaurants
      if (restaurantsWithCoords.length > 0) {
        fitToRestaurants(restaurantsWithCoords);
      }
    } catch (error) {
      console.error('❌ Erreur chargement restaurants:', error);
    }
  };

  const fitToRestaurants = (restaurantList: RestaurantMarkerData[]) => {
    if (!currentLocation || restaurantList.length === 0) return;

    const coordinates = [
      currentLocation,
      ...restaurantList.map(r => r.coordinates!),
    ];

    mapRef.current?.fitToCoordinates(coordinates, {
      edgePadding: { top: 100, right: 50, bottom: 100, left: 50 },
      animated: true,
    });
  };

  const calculateRoute = async (restaurant: RestaurantMarkerData) => {
    if (!currentLocation || !restaurant.coordinates) return;

    try {
      setLoadingRoute(true);
      
      const route = await directionsService.getDirections(
        currentLocation,
        restaurant.coordinates,
        { mode: 'driving' }
      );

      if (route) {
        const coordinates = directionsService.decodePolyline(route.polyline.points);
        setRouteCoordinates(coordinates);
        
        // Mettre à jour les informations du restaurant
        setSelectedRestaurant({
          ...restaurant,
          routeCoordinates: coordinates,
          distance: route.distance.value,
          duration: route.duration.value,
        });

        // Ajuster la vue pour inclure la route
        mapRef.current?.fitToCoordinates([currentLocation, restaurant.coordinates], {
          edgePadding: { top: 100, right: 50, bottom: 200, left: 50 },
          animated: true,
        });
      }
    } catch (error) {
      console.error('❌ Erreur calcul itinéraire:', error);
      Alert.alert('Erreur', 'Impossible de calculer l\'itinéraire');
    } finally {
      setLoadingRoute(false);
    }
  };

  const centerOnUserLocation = () => {
    if (currentLocation) {
      const newRegion = {
        ...currentLocation,
        latitudeDelta: 0.01,
        longitudeDelta: 0.01,
      };
      mapRef.current?.animateToRegion(newRegion, 1000);
    }
  };

  const onMarkerPress = (restaurant: RestaurantMarkerData) => {
    setSelectedRestaurant(restaurant);
    calculateRoute(restaurant);
  };

  const onCalloutPress = (restaurant: RestaurantMarkerData) => {
    navigation.navigate('Establishment', { restaurant });
  };

  const renderFilterButtons = () => (
    <View style={styles.filterContainer}>
      <TouchableOpacity
        style={[styles.filterButton, !filterType && styles.filterButtonActive]}
        onPress={() => setFilterType(null)}
      >
        <Text style={[styles.filterText, !filterType && styles.filterTextActive]}>
          Tous
        </Text>
      </TouchableOpacity>
      
      {Object.entries(restaurantTypes).map(([type, config]) => (
        <TouchableOpacity
          key={type}
          style={[styles.filterButton, filterType === type && styles.filterButtonActive]}
          onPress={() => setFilterType(filterType === type ? null : type)}
        >
          <Ionicons 
            name={config.icon as any} 
            size={16} 
            color={filterType === type ? colors.background : config.color} 
          />
        </TouchableOpacity>
      ))}
    </View>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={styles.loadingText}>Chargement de la carte...</Text>
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={colors.background} />
      
      {/* En-tête */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Restaurants à proximité</Text>
        <View style={styles.headerRight} />
      </View>

      {/* Filtres */}
      {renderFilterButtons()}

      {/* Carte */}
      <View style={styles.mapContainer}>
        <MapView
          ref={mapRef}
          provider={PROVIDER_GOOGLE}
          style={styles.map}
          region={region}
          onRegionChangeComplete={setRegion}
          showsUserLocation={true}
          showsMyLocationButton={false}
          showsCompass={true}
          showsScale={true}
          loadingEnabled={true}
        >
          {/* Marqueurs des restaurants */}
          {restaurants.map((restaurant) => (
            <Marker
              key={restaurant.id}
              coordinate={restaurant.coordinates!}
              onPress={() => onMarkerPress(restaurant)}
            >
              <RestaurantMarker
                businessType={restaurant.business_type}
                isSelected={selectedRestaurant?.id === restaurant.id}
                rating={restaurant.rating}
                size="medium"
              />

              <Callout onPress={() => onCalloutPress(restaurant)}>
                <View style={styles.calloutContainer}>
                  <Text style={styles.calloutTitle}>{restaurant.business_name}</Text>
                  <Text style={styles.calloutSubtitle}>⭐ {restaurant.rating.toFixed(1)}</Text>
                  {restaurant.distance && (
                    <Text style={styles.calloutDistance}>
                      📍 {directionsService.formatDistance(restaurant.distance)}
                    </Text>
                  )}
                  {restaurant.duration && (
                    <Text style={styles.calloutDuration}>
                      🚗 {directionsService.formatDuration(restaurant.duration)}
                    </Text>
                  )}
                  <Text style={styles.calloutAction}>Appuyez pour voir les détails</Text>
                </View>
              </Callout>
            </Marker>
          ))}

          {/* Route */}
          {routeCoordinates.length > 0 && (
            <Polyline
              coordinates={routeCoordinates}
              strokeColor={colors.primary}
              strokeWidth={4}
              lineDashPattern={[5, 5]}
            />
          )}
        </MapView>

        {/* Bouton de recentrage */}
        <TouchableOpacity style={styles.centerButton} onPress={centerOnUserLocation}>
          <Ionicons name="locate" size={24} color={colors.primary} />
        </TouchableOpacity>

        {/* Indicateur de chargement de route */}
        {loadingRoute && (
          <View style={styles.routeLoadingContainer}>
            <ActivityIndicator size="small" color={colors.primary} />
            <Text style={styles.routeLoadingText}>Calcul de l'itinéraire...</Text>
          </View>
        )}
      </View>

      {/* Informations du restaurant sélectionné */}
      {selectedRestaurant && (
        <View style={styles.selectedRestaurantContainer}>
          <View style={styles.selectedRestaurantInfo}>
            <Text style={styles.selectedRestaurantName}>{selectedRestaurant.business_name}</Text>
            <Text style={styles.selectedRestaurantType}>
              {selectedRestaurant.business_type.charAt(0).toUpperCase() + selectedRestaurant.business_type.slice(1)}
            </Text>
            
            <View style={styles.selectedRestaurantStats}>
              <Text style={styles.selectedRestaurantStat}>⭐ {selectedRestaurant.rating.toFixed(1)}</Text>
              {selectedRestaurant.distance && (
                <Text style={styles.selectedRestaurantStat}>
                  📍 {directionsService.formatDistance(selectedRestaurant.distance)}
                </Text>
              )}
              {selectedRestaurant.duration && (
                <Text style={styles.selectedRestaurantStat}>
                  🚗 {directionsService.formatDuration(selectedRestaurant.duration)}
                </Text>
              )}
            </View>
          </View>
          
          <TouchableOpacity
            style={styles.selectedRestaurantButton}
            onPress={() => onCalloutPress(selectedRestaurant)}
          >
            <Text style={styles.selectedRestaurantButtonText}>Voir le menu</Text>
          </TouchableOpacity>
        </View>
      )}

      {/* Légende */}
      <View style={styles.legendContainer}>
        <Text style={styles.legendTitle}>Légende</Text>
        <View style={styles.legendItems}>
          {Object.entries(restaurantTypes).slice(0, 3).map(([type, config]) => (
            <View key={type} style={styles.legendItem}>
              <View style={[styles.legendMarker, { backgroundColor: config.color }]}>
                <Ionicons name={config.icon as any} size={12} color={colors.background} />
              </View>
              <Text style={styles.legendText}>
                {type.charAt(0).toUpperCase() + type.slice(1)}
              </Text>
            </View>
          ))}
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#7F8C8D',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E8E8E8',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#2C3E50',
  },
  headerRight: {
    width: 40,
  },
  filterContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E8E8E8',
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginRight: 8,
    borderRadius: 20,
    backgroundColor: '#F8F9FA',
    borderWidth: 1,
    borderColor: '#E8E8E8',
  },
  filterButtonActive: {
    backgroundColor: '#0DCAA8',
    borderColor: '#0DCAA8',
  },
  filterText: {
    fontSize: 14,
    color: '#7F8C8D',
    fontWeight: '500',
  },
  filterTextActive: {
    color: '#FFFFFF',
  },
  mapContainer: {
    flex: 1,
    position: 'relative',
  },
  map: {
    flex: 1,
  },

  calloutContainer: {
    width: 200,
    padding: 12,
  },
  calloutTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2C3E50',
    marginBottom: 4,
  },
  calloutSubtitle: {
    fontSize: 14,
    color: '#7F8C8D',
    marginBottom: 2,
  },
  calloutDistance: {
    fontSize: 12,
    color: '#7F8C8D',
    marginBottom: 2,
  },
  calloutDuration: {
    fontSize: 12,
    color: '#7F8C8D',
    marginBottom: 8,
  },
  calloutAction: {
    fontSize: 12,
    color: '#0DCAA8',
    fontWeight: '500',
  },
  centerButton: {
    position: 'absolute',
    top: 16,
    right: 16,
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  routeLoadingContainer: {
    position: 'absolute',
    top: 80,
    left: 16,
    right: 16,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  routeLoadingText: {
    marginLeft: 8,
    fontSize: 14,
    color: '#7F8C8D',
  },
  selectedRestaurantContainer: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderTopWidth: 1,
    borderTopColor: '#E8E8E8',
    flexDirection: 'row',
    alignItems: 'center',
  },
  selectedRestaurantInfo: {
    flex: 1,
  },
  selectedRestaurantName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#2C3E50',
    marginBottom: 4,
  },
  selectedRestaurantType: {
    fontSize: 14,
    color: '#7F8C8D',
    marginBottom: 8,
  },
  selectedRestaurantStats: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  selectedRestaurantStat: {
    fontSize: 12,
    color: '#7F8C8D',
    marginRight: 16,
  },
  selectedRestaurantButton: {
    backgroundColor: '#0DCAA8',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
  },
  selectedRestaurantButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
  legendContainer: {
    position: 'absolute',
    bottom: 16,
    left: 16,
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  legendTitle: {
    fontSize: 12,
    fontWeight: '600',
    color: '#2C3E50',
    marginBottom: 8,
  },
  legendItems: {
    flexDirection: 'row',
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 12,
  },
  legendMarker: {
    width: 16,
    height: 16,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 4,
  },
  legendText: {
    fontSize: 10,
    color: '#7F8C8D',
  },
});

export default MapScreen;
