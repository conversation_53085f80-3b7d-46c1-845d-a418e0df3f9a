{"version": 3, "file": "UpdatesEmitter.js", "sourceRoot": "", "sources": ["../src/UpdatesEmitter.ts"], "names": [], "mappings": "AAAA,OAAO,iBAAiB,MAAM,eAAe,CAAC;AAM9C,MAAM,CAAC,IAAI,aAAa,GAAG,kCAAkC,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;AAEhG,iBAAiB,CAAC,WAAW,CAAC,oCAAoC,EAAE,6BAA6B,CAAC,CAAC;AAMnG,MAAM,4BAA4B,GAAG,IAAI,GAAG,EAAkD,CAAC;AAE/F,qCAAqC;AACrC,SAAS,6BAA6B,CAAC,MAAW;IAChD,MAAM,SAAS,GAAG,OAAO,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,MAAM,EAAE,CAAC;IAClF,MAAM,kBAAkB,GAAG,kCAAkC,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;IAEjF,wDAAwD;IACxD,IAAI,kBAAkB,CAAC,cAAc,IAAI,aAAa,CAAC,cAAc,EAAE,CAAC;QACtE,OAAO;IACT,CAAC;IAED,SAAS,CAAC,OAAO,GAAG,kBAAkB,CAAC;IACvC,aAAa,GAAG,kBAAkB,CAAC;IACnC,4BAA4B,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC;AAC1E,CAAC;AAED;;;GAGG;AACH,MAAM,CAAC,MAAM,6BAA6B,GAAG,CAC3C,QAAwD,EACxB,EAAE;IAClC,4BAA4B,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IAC3C,OAAO;QACL,MAAM;YACJ,4BAA4B,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QAChD,CAAC;KACF,CAAC;AACJ,CAAC,CAAC;AAEF;;;GAGG;AACH,MAAM,CAAC,MAAM,wBAAwB,GAAG,CAAC,KAAoC,EAAE,EAAE;IAC/E,6BAA6B,CAAC,KAAK,CAAC,CAAC;AACvC,CAAC,CAAC;AAEF;;;GAGG;AACH,MAAM,CAAC,MAAM,kBAAkB,GAAG,GAAG,EAAE;IACrC,aAAa,GAAG,kCAAkC,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;AACvF,CAAC,CAAC;AAEF,SAAS,kCAAkC,CACzC,qBAKC;IAED,MAAM,aAAa,GAAG,EAAE,GAAG,qBAAqB,EAAE,CAAC;IACnD,IAAI,aAAa,CAAC,oBAAoB,EAAE,CAAC;QACvC,aAAa,CAAC,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,oBAAoB,CAAC,CAAC;QAC9E,OAAO,aAAa,CAAC,oBAAoB,CAAC;IAC5C,CAAC;IACD,IAAI,aAAa,CAAC,wBAAwB,EAAE,CAAC;QAC3C,aAAa,CAAC,kBAAkB,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,wBAAwB,CAAC,CAAC;QACtF,OAAO,aAAa,CAAC,wBAAwB,CAAC;IAChD,CAAC;IACD,IAAI,aAAa,CAAC,4BAA4B,EAAE,CAAC;QAC/C,aAAa,CAAC,sBAAsB,GAAG,IAAI,IAAI,CAAC,aAAa,CAAC,4BAA4B,CAAC,CAAC;QAC5F,OAAO,aAAa,CAAC,4BAA4B,CAAC;IACpD,CAAC;IACD,IAAI,aAAa,CAAC,cAAc,EAAE,CAAC;QACjC,aAAa,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC;QAClE,OAAO,aAAa,CAAC,cAAc,CAAC;IACtC,CAAC;IACD,OAAO,aAAa,CAAC;AACvB,CAAC", "sourcesContent": ["import ExpoUpdatesModule from './ExpoUpdates';\nimport type {\n  UpdatesNativeStateChangeEvent,\n  UpdatesNativeStateMachineContext,\n} from './Updates.types';\n\nexport let latestContext = transformNativeStateMachineContext(ExpoUpdatesModule.initialContext);\n\nExpoUpdatesModule.addListener('Expo.nativeUpdatesStateChangeEvent', _handleNativeStateChangeEvent);\n\ninterface UpdatesStateChangeSubscription {\n  remove(): void;\n}\n\nconst _updatesStateChangeListeners = new Set<(event: UpdatesNativeStateChangeEvent) => void>();\n\n// Reemits native state change events\nfunction _handleNativeStateChangeEvent(params: any) {\n  const newParams = typeof params === 'string' ? JSON.parse(params) : { ...params };\n  const transformedContext = transformNativeStateMachineContext(newParams.context);\n\n  // only process state change events if they are in order\n  if (transformedContext.sequenceNumber <= latestContext.sequenceNumber) {\n    return;\n  }\n\n  newParams.context = transformedContext;\n  latestContext = transformedContext;\n  _updatesStateChangeListeners.forEach((listener) => listener(newParams));\n}\n\n/**\n * Add listener for state change events\n * @hidden\n */\nexport const addUpdatesStateChangeListener = (\n  listener: (event: UpdatesNativeStateChangeEvent) => void\n): UpdatesStateChangeSubscription => {\n  _updatesStateChangeListeners.add(listener);\n  return {\n    remove() {\n      _updatesStateChangeListeners.delete(listener);\n    },\n  };\n};\n\n/**\n * Allows JS test to emit a simulated native state change event (used in unit testing)\n * @hidden\n */\nexport const emitTestStateChangeEvent = (event: UpdatesNativeStateChangeEvent) => {\n  _handleNativeStateChangeEvent(event);\n};\n\n/**\n * Allows JS test to reset latest context (and sequence number)\n * @hidden\n */\nexport const resetLatestContext = () => {\n  latestContext = transformNativeStateMachineContext(ExpoUpdatesModule.initialContext);\n};\n\nfunction transformNativeStateMachineContext(\n  originalNativeContext: UpdatesNativeStateMachineContext & {\n    latestManifestString?: string;\n    downloadedManifestString?: string;\n    lastCheckForUpdateTimeString?: string;\n    rollbackString?: string;\n  }\n): UpdatesNativeStateMachineContext {\n  const nativeContext = { ...originalNativeContext };\n  if (nativeContext.latestManifestString) {\n    nativeContext.latestManifest = JSON.parse(nativeContext.latestManifestString);\n    delete nativeContext.latestManifestString;\n  }\n  if (nativeContext.downloadedManifestString) {\n    nativeContext.downloadedManifest = JSON.parse(nativeContext.downloadedManifestString);\n    delete nativeContext.downloadedManifestString;\n  }\n  if (nativeContext.lastCheckForUpdateTimeString) {\n    nativeContext.lastCheckForUpdateTime = new Date(nativeContext.lastCheckForUpdateTimeString);\n    delete nativeContext.lastCheckForUpdateTimeString;\n  }\n  if (nativeContext.rollbackString) {\n    nativeContext.rollback = JSON.parse(nativeContext.rollbackString);\n    delete nativeContext.rollbackString;\n  }\n  return nativeContext;\n}\n"]}