import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { StatusBar } from 'expo-status-bar';

export default function App() {
  return (
    <View style={styles.container}>
      <StatusBar style="dark" />

      <View style={styles.header}>
        <Text style={styles.title}>🚀 Mientior Livraison</Text>
        <Text style={styles.subtitle}>Plateforme de livraison pour l'Afrique</Text>
      </View>

      <View style={styles.content}>
        <Text style={styles.welcomeText}>
          Bienvenue sur votre application de livraison !
        </Text>

        <View style={styles.features}>
          <Text style={styles.featureTitle}>✨ Fonctionnalités principales :</Text>
          <Text style={styles.feature}>🏠 Commande de repas</Text>
          <Text style={styles.feature}>🚚 Suivi de livraison en temps réel</Text>
          <Text style={styles.feature}>💳 Paiement mobile (Orange Money, MTN)</Text>
          <Text style={styles.feature}>🌍 Optimisé pour l'Afrique</Text>
        </View>

        <TouchableOpacity style={styles.button}>
          <Text style={styles.buttonText}>Commencer</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.footer}>
        <Text style={styles.footerText}>
          Application en cours de développement
        </Text>
        <Text style={styles.version}>Version 1.0.0</Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    paddingTop: 50,
  },
  header: {
    alignItems: 'center',
    paddingVertical: 40,
    backgroundColor: '#0DCAA8',
    marginBottom: 40,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#FFFFFF',
    opacity: 0.9,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
    alignItems: 'center',
  },
  welcomeText: {
    fontSize: 18,
    color: '#333',
    textAlign: 'center',
    marginBottom: 30,
    lineHeight: 26,
  },
  features: {
    width: '100%',
    marginBottom: 40,
  },
  featureTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 16,
    textAlign: 'center',
  },
  feature: {
    fontSize: 16,
    color: '#666',
    marginBottom: 12,
    paddingLeft: 20,
  },
  button: {
    backgroundColor: '#0DCAA8',
    paddingHorizontal: 40,
    paddingVertical: 16,
    borderRadius: 12,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  buttonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '600',
  },
  footer: {
    alignItems: 'center',
    paddingVertical: 20,
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
  },
  footerText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  version: {
    fontSize: 12,
    color: '#999',
  },
});
