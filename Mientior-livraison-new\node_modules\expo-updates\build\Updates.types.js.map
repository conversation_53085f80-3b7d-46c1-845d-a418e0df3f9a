{"version": 3, "file": "Updates.types.js", "sourceRoot": "", "sources": ["../src/Updates.types.ts"], "names": [], "mappings": "AAIA,MAAM,CAAN,IAAY,mCAwBX;AAxBD,WAAY,mCAAmC;IAC7C;;OAEG;IACH,kGAA2D,CAAA;IAC3D;;;OAGG;IACH,8GAAuE,CAAA;IACvE;;;OAGG;IACH,0FAAmD,CAAA;IACnD;;;OAGG;IACH,kHAA2E,CAAA;IAC3E;;OAEG;IACH,+FAAwD,CAAA;AAC1D,CAAC,EAxBW,mCAAmC,KAAnC,mCAAmC,QAwB9C;AA+KD;;GAEG;AACH,MAAM,CAAN,IAAY,mBAYX;AAZD,WAAY,mBAAmB;IAC7B,oCAAa,CAAA;IACb,kEAA2C,CAAA;IAC3C,+EAAwD,CAAA;IACxD,4EAAqD,CAAA;IACrD,iFAA0D,CAAA;IAC1D,2EAAoD,CAAA;IACpD,mEAA4C,CAAA;IAC5C,mEAA4C,CAAA;IAC5C,0DAAmC,CAAA;IACnC,mEAA4C,CAAA;IAC5C,0CAAmB,CAAA;AACrB,CAAC,EAZW,mBAAmB,KAAnB,mBAAmB,QAY9B;AAED;;GAEG;AACH,MAAM,CAAN,IAAY,oBAOX;AAPD,WAAY,oBAAoB;IAC9B,uCAAe,CAAA;IACf,uCAAe,CAAA;IACf,qCAAa,CAAA;IACb,qCAAa,CAAA;IACb,uCAAe,CAAA;IACf,uCAAe,CAAA;AACjB,CAAC,EAPW,oBAAoB,KAApB,oBAAoB,QAO/B;AAED;;;;;GAKG;AACH,MAAM,CAAN,IAAY,8BAiBX;AAjBD,WAAY,8BAA8B;IACxC;;OAEG;IACH,qDAAmB,CAAA;IACnB;;OAEG;IACH,yEAAuC,CAAA;IACvC;;OAEG;IACH,yDAAuB,CAAA;IACvB;;OAEG;IACH,iDAAe,CAAA;AACjB,CAAC,EAjBW,8BAA8B,KAA9B,8BAA8B,QAiBzC", "sourcesContent": ["import { ExpoUpdatesManifest, EmbeddedManifest } from 'expo-manifests';\n\nexport type Manifest = ExpoUpdatesManifest | EmbeddedManifest;\n\nexport enum UpdateCheckResultNotAvailableReason {\n  /**\n   * No update manifest or rollback directive received from the update server.\n   */\n  NO_UPDATE_AVAILABLE_ON_SERVER = 'noUpdateAvailableOnServer',\n  /**\n   * An update manifest was received from the update server, but the update is not launchable,\n   * or does not pass the configured selection policy.\n   */\n  UPDATE_REJECTED_BY_SELECTION_POLICY = 'updateRejectedBySelectionPolicy',\n  /**\n   * An update manifest was received from the update server, but the update has been previously\n   * launched on this device and never successfully launched.\n   */\n  UPDATE_PREVIOUSLY_FAILED = 'updatePreviouslyFailed',\n  /**\n   * A rollback directive was received from the update server, but the directive does not pass\n   * the configured selection policy.\n   */\n  ROLLBACK_REJECTED_BY_SELECTION_POLICY = 'rollbackRejectedBySelectionPolicy',\n  /**\n   * A rollback directive was received from the update server, but this app has no embedded update.\n   */\n  ROLLBACK_NO_EMBEDDED = 'rollbackNoEmbeddedConfiguration',\n}\n\n/**\n * The update check result when a rollback directive is received.\n */\nexport type UpdateCheckResultRollBack = {\n  /**\n   * Whether an update is available. This property is false for a roll back update.\n   */\n  isAvailable: false;\n  /**\n   * The manifest of the update when available.\n   */\n  manifest: undefined;\n  /**\n   * Whether a roll back to embedded update is available.\n   */\n  isRollBackToEmbedded: true;\n  /**\n   * If no new update is found, this contains one of several enum values indicating the reason.\n   */\n  reason: undefined;\n};\n\n/**\n * The update check result when a new update is found on the server.\n */\nexport type UpdateCheckResultAvailable = {\n  /**\n   * Whether an update is available. This property is false for a roll back update.\n   */\n  isAvailable: true;\n  /**\n   * The manifest of the update when available.\n   */\n  manifest: Manifest;\n  /**\n   * Whether a roll back to embedded update is available.\n   */\n  isRollBackToEmbedded: false;\n  /**\n   * If no new update is found, this contains one of several enum values indicating the reason.\n   */\n  reason: undefined;\n};\n\n/**\n * The update check result if no new update was found.\n */\nexport type UpdateCheckResultNotAvailable = {\n  /**\n   * Whether an update is available. This property is false for a roll back update.\n   */\n  isAvailable: false;\n  /**\n   * The manifest of the update when available.\n   */\n  manifest: undefined;\n  /**\n   * Whether a roll back to embedded update is available.\n   */\n  isRollBackToEmbedded: false;\n  /**\n   * If no new update is found, this contains one of several enum values indicating the reason.\n   */\n  reason: UpdateCheckResultNotAvailableReason;\n};\n\n/**\n * The result of checking for a new update.\n */\nexport type UpdateCheckResult =\n  | UpdateCheckResultRollBack\n  | UpdateCheckResultAvailable\n  | UpdateCheckResultNotAvailable;\n\n/**\n * The successful result of fetching a new update.\n */\nexport type UpdateFetchResultSuccess = {\n  /**\n   * Whether the fetched update is new (that is, a different version than what's currently running).\n   * Always `true` when `isRollBackToEmbedded` is `false`.\n   */\n  isNew: true;\n  /**\n   * The manifest of the fetched update.\n   */\n  manifest: Manifest;\n  /**\n   * Whether the fetched update is a roll back to the embedded update.\n   */\n  isRollBackToEmbedded: false;\n};\n\n/**\n * The failed result of fetching a new update.\n */\nexport type UpdateFetchResultFailure = {\n  /**\n   * Whether the fetched update is new (that is, a different version than what's currently running).\n   * Always `false` when `isRollBackToEmbedded` is `true`.\n   */\n  isNew: false;\n  /**\n   * The manifest of the fetched update.\n   */\n  manifest: undefined;\n  /**\n   * Whether the fetched update is a roll back to the embedded update.\n   */\n  isRollBackToEmbedded: false;\n};\n\n/**\n * The roll back to embedded result of fetching a new update.\n */\nexport type UpdateFetchResultRollBackToEmbedded = {\n  /**\n   * Whether the fetched update is new (that is, a different version than what's currently running).\n   * Always `false` when `isRollBackToEmbedded` is `true`.\n   */\n  isNew: false;\n  /**\n   * The manifest of the fetched update.\n   */\n  manifest: undefined;\n  /**\n   * Whether the fetched update is a roll back to the embedded update.\n   */\n  isRollBackToEmbedded: true;\n};\n\n/**\n * The result of fetching a new update.\n */\nexport type UpdateFetchResult =\n  | UpdateFetchResultSuccess\n  | UpdateFetchResultFailure\n  | UpdateFetchResultRollBackToEmbedded;\n\n/**\n * An object representing a single log entry from `expo-updates` logging on the client.\n */\nexport type UpdatesLogEntry = {\n  /**\n   * The time the log was written, in milliseconds since Jan 1 1970 UTC.\n   */\n  timestamp: number;\n  /**\n   * The log entry message.\n   */\n  message: string;\n  /**\n   * One of the defined code values for `expo-updates` log entries.\n   */\n  code: UpdatesLogEntryCode;\n  /**\n   * One of the defined log level or severity values.\n   */\n  level: UpdatesLogEntryLevel;\n  /**\n   * If present, the unique ID of an update associated with this log entry.\n   */\n  updateId?: string;\n  /**\n   * If present, the unique ID or hash of an asset associated with this log entry.\n   */\n  assetId?: string;\n  /**\n   * If present, an Android or iOS native stack trace associated with this log entry.\n   */\n  stacktrace?: string[];\n};\n\n/**\n * The possible code values for `expo-updates` log entries\n */\nexport enum UpdatesLogEntryCode {\n  NONE = 'None',\n  NO_UPDATES_AVAILABLE = 'NoUpdatesAvailable',\n  UPDATE_ASSETS_NOT_AVAILABLE = 'UpdateAssetsNotAvailable',\n  UPDATE_SERVER_UNREACHABLE = 'UpdateServerUnreachable',\n  UPDATE_HAS_INVALID_SIGNATURE = 'UpdateHasInvalidSignature',\n  UPDATE_CODE_SIGNING_ERROR = 'UpdateCodeSigningError',\n  UPDATE_FAILED_TO_LOAD = 'UpdateFailedToLoad',\n  ASSETS_FAILED_TO_LOAD = 'AssetsFailedToLoad',\n  JS_RUNTIME_ERROR = 'JSRuntimeError',\n  INITIALIZATION_ERROR = 'InitializationError',\n  UNKNOWN = 'Unknown',\n}\n\n/**\n * The possible log levels for `expo-updates` log entries\n */\nexport enum UpdatesLogEntryLevel {\n  TRACE = 'trace',\n  DEBUG = 'debug',\n  INFO = 'info',\n  WARN = 'warn',\n  ERROR = 'error',\n  FATAL = 'fatal',\n}\n\n/**\n * The possible settings that determine if `expo-updates` will check for updates on app startup.\n * By default, Expo will check for updates every time the app is loaded.\n * Set this to `ON_ERROR_RECOVERY` to disable automatic checking unless recovering from an error.\n * Set this to `NEVER` to completely disable automatic checking.\n */\nexport enum UpdatesCheckAutomaticallyValue {\n  /**\n   * Checks for updates whenever the app is loaded. This is the default setting.\n   */\n  ON_LOAD = 'ON_LOAD',\n  /**\n   * Only checks for updates when the app starts up after an error recovery.\n   */\n  ON_ERROR_RECOVERY = 'ON_ERROR_RECOVERY',\n  /**\n   * Only checks for updates when the app starts and has a Wi-Fi connection.\n   */\n  WIFI_ONLY = 'WIFI_ONLY',\n  /**\n   * Automatic update checks are off, and update checks must be done through the JS API.\n   */\n  NEVER = 'NEVER',\n}\n\n/**\n * @hidden\n */\nexport type LocalAssets = Record<string, string>;\n\n/**\n * @hidden\n */\nexport type UpdatesNativeStateRollback = {\n  // ISO date string with the rollback commit time\n  commitTime: string;\n};\n\n/**\n * The native state machine context, either read directly from a native module method,\n * or received in a state change event. Used internally by this module and not exported publicly.\n * @hidden\n */\nexport type UpdatesNativeStateMachineContext = {\n  isStartupProcedureRunning: boolean;\n  isUpdateAvailable: boolean;\n  isUpdatePending: boolean;\n  isChecking: boolean;\n  isDownloading: boolean;\n  isRestarting: boolean;\n  restartCount: number;\n  latestManifest?: Manifest;\n  downloadedManifest?: Manifest;\n  rollback?: UpdatesNativeStateRollback;\n  checkError?: Error;\n  downloadError?: Error;\n  lastCheckForUpdateTime?: Date;\n  sequenceNumber: number;\n};\n\n/**\n * @hidden\n */\nexport type UpdatesNativeStateChangeEvent = {\n  // Change event emitted by native\n  context: UpdatesNativeStateMachineContext;\n};\n"]}