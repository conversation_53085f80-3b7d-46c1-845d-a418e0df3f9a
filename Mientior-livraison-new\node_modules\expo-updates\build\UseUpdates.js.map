{"version": 3, "file": "UseUpdates.js", "sourceRoot": "", "sources": ["../src/UseUpdates.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,MAAM,OAAO,CAAC;AAE5C,OAAO,EAAE,6BAA6B,EAAE,aAAa,EAAE,MAAM,kBAAkB,CAAC;AAEhF,OAAO,EAAE,gBAAgB,EAAE,uBAAuB,EAAE,MAAM,mBAAmB,CAAC;AAE9E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA+CG;AACH,MAAM,CAAC,MAAM,UAAU,GAA+B,GAAG,EAAE;IACzD,MAAM,CAAC,YAAY,EAAE,eAAe,CAAC,GAAG,QAAQ,CAAC,uBAAuB,CAAC,aAAa,CAAC,CAAC,CAAC;IAEzF,iEAAiE;IACjE,SAAS,CAAC,GAAG,EAAE;QACb,MAAM,YAAY,GAAG,6BAA6B,CAAC,CAAC,KAAK,EAAE,EAAE;YAC3D,eAAe,CAAC,uBAAuB,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;QACH,OAAO,GAAG,EAAE,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC;IACrC,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,wDAAwD;IACxD,OAAO;QACL,gBAAgB;QAChB,GAAG,YAAY;KAChB,CAAC;AACJ,CAAC,CAAC", "sourcesContent": ["import { useEffect, useState } from 'react';\n\nimport { addUpdatesStateChangeListener, latestContext } from './UpdatesEmitter';\nimport type { UseUpdatesReturnType } from './UseUpdates.types';\nimport { currentlyRunning, updatesStateFromContext } from './UseUpdatesUtils';\n\n/**\n * Hook that obtains information on available updates and on the currently running update.\n *\n * @return the structures with information on currently running and available updates.\n *\n * @example\n * ```tsx UpdatesDemo.tsx\n * import { StatusBar } from 'expo-status-bar';\n * import * as Updates from 'expo-updates';\n * import { useEffect } from 'react';\n * import { Button, Text, View } from 'react-native';\n *\n * export default function UpdatesDemo() {\n *   const {\n *     currentlyRunning,\n *     isUpdateAvailable,\n *     isUpdatePending\n *   } = Updates.useUpdates();\n *\n *   useEffect(() => {\n *     if (isUpdatePending) {\n *       // Update has successfully downloaded; apply it now\n *       Updates.reloadAsync();\n *     }\n *   }, [isUpdatePending]);\n *\n *   // If true, we show the button to download and run the update\n *   const showDownloadButton = isUpdateAvailable;\n *\n *   // Show whether or not we are running embedded code or an update\n *   const runTypeMessage = currentlyRunning.isEmbeddedLaunch\n *     ? 'This app is running from built-in code'\n *     : 'This app is running an update';\n *\n *   return (\n *     <View style={styles.container}>\n *       <Text style={styles.headerText}>Updates Demo</Text>\n *       <Text>{runTypeMessage}</Text>\n *       <Button onPress={() => Updates.checkForUpdateAsync()} title=\"Check manually for updates\" />\n *       {showDownloadButton ? (\n *         <Button onPress={() => Updates.fetchUpdateAsync()} title=\"Download and run update\" />\n *       ) : null}\n *       <StatusBar style=\"auto\" />\n *     </View>\n *   );\n * }\n * ```\n */\nexport const useUpdates: () => UseUpdatesReturnType = () => {\n  const [updatesState, setUpdatesState] = useState(updatesStateFromContext(latestContext));\n\n  // Change the state based on native state machine context changes\n  useEffect(() => {\n    const subscription = addUpdatesStateChangeListener((event) => {\n      setUpdatesState(updatesStateFromContext(event.context));\n    });\n    return () => subscription.remove();\n  }, []);\n\n  // Return the updates info and the user facing functions\n  return {\n    currentlyRunning,\n    ...updatesState,\n  };\n};\n"]}