#!/usr/bin/env node

/**
 * Script Node.js pour extraire l'empreinte SHA-1 du certificat de débogage Android
 * Utilise des méthodes alternatives quand keytool n'est pas disponible
 */

const fs = require('fs');
const crypto = require('crypto');
const { execSync } = require('child_process');
const path = require('path');

console.log('🔍 Extraction de l\'empreinte SHA-1 du certificat de débogage Android');
console.log('=================================================================');
console.log('');

// Configuration
const KEYSTORE_PATHS = [
  'android/app/debug.keystore',
  path.join(process.env.USERPROFILE || process.env.HOME, '.android', 'debug.keystore'),
  path.join(process.env.ANDROID_HOME || '', 'debug.keystore')
];

const KEYTOOL_PATHS = [
  'keytool',
  path.join(process.env.JAVA_HOME || '', 'bin', 'keytool'),
  path.join(process.env.JAVA_HOME || '', 'bin', 'keytool.exe'),
  'C:\\Program Files\\Java\\jdk*\\bin\\keytool.exe',
  'C:\\Program Files\\Android\\Android Studio\\jre\\bin\\keytool.exe'
];

// Fonction pour trouver le keystore
function findKeystore() {
  for (const keystorePath of KEYSTORE_PATHS) {
    if (fs.existsSync(keystorePath)) {
      console.log(`✅ Keystore trouvé: ${keystorePath}`);
      return keystorePath;
    }
  }
  
  console.log('❌ Aucun keystore trouvé dans les emplacements suivants:');
  KEYSTORE_PATHS.forEach(path => console.log(`   - ${path}`));
  return null;
}

// Fonction pour trouver keytool
function findKeytool() {
  for (const keytoolPath of KEYTOOL_PATHS) {
    try {
      if (keytoolPath.includes('*')) {
        // Gérer les chemins avec wildcards (Windows)
        continue;
      }
      
      execSync(`"${keytoolPath}" -help`, { stdio: 'ignore' });
      console.log(`✅ Keytool trouvé: ${keytoolPath}`);
      return keytoolPath;
    } catch (error) {
      // Continuer avec le prochain chemin
    }
  }
  
  console.log('❌ Keytool non trouvé');
  return null;
}

// Fonction pour extraire l'empreinte avec keytool
function extractWithKeytool(keystorePath, keytoolPath) {
  try {
    console.log('🔄 Extraction avec keytool...');
    
    const command = `"${keytoolPath}" -list -v -keystore "${keystorePath}" -alias androiddebugkey -storepass android -keypass android`;
    const output = execSync(command, { encoding: 'utf8' });
    
    console.log('✅ Extraction réussie!');
    console.log('');
    console.log('📄 Sortie de keytool:');
    console.log('====================');
    console.log(output);
    console.log('');
    
    // Extraire SHA-1
    const sha1Match = output.match(/SHA1:\s*([A-F0-9:]{59})/i);
    const sha256Match = output.match(/SHA256:\s*([A-F0-9:]{95})/i);
    
    if (sha1Match) {
      console.log('🔑 EMPREINTE SHA-1:');
      console.log(`   ${sha1Match[1]}`);
    }
    
    if (sha256Match) {
      console.log('');
      console.log('🔑 EMPREINTE SHA-256:');
      console.log(`   ${sha256Match[1]}`);
    }
    
    return {
      sha1: sha1Match ? sha1Match[1] : null,
      sha256: sha256Match ? sha256Match[1] : null
    };
    
  } catch (error) {
    console.log(`❌ Erreur avec keytool: ${error.message}`);
    return null;
  }
}

// Fonction pour analyser le keystore manuellement (méthode de fallback)
function analyzeKeystoreManually(keystorePath) {
  try {
    console.log('🔄 Analyse manuelle du keystore...');
    
    const keystoreData = fs.readFileSync(keystorePath);
    console.log(`📊 Taille du keystore: ${keystoreData.length} bytes`);
    
    // Rechercher des patterns de certificat X.509
    const x509Pattern = Buffer.from([0x30, 0x82]); // Début d'un certificat X.509 DER
    let offset = keystoreData.indexOf(x509Pattern);
    
    if (offset !== -1) {
      console.log(`📍 Certificat X.509 trouvé à l'offset: ${offset}`);
      
      // Essayer d'extraire une partie du certificat pour analyse
      const certStart = offset;
      const certData = keystoreData.slice(certStart, certStart + 1000); // Premiers 1000 bytes
      
      // Calculer un hash simple pour identification
      const hash = crypto.createHash('sha1').update(certData).digest('hex');
      const formattedHash = hash.match(/.{2}/g).join(':').toUpperCase();
      
      console.log('⚠️  Hash approximatif (non officiel):');
      console.log(`   ${formattedHash}`);
      console.log('');
      console.log('⚠️  ATTENTION: Ce hash est approximatif et ne doit PAS être utilisé');
      console.log('   pour la configuration Google Cloud Console.');
      
    } else {
      console.log('❌ Aucun certificat X.509 trouvé dans le keystore');
    }
    
  } catch (error) {
    console.log(`❌ Erreur lors de l'analyse manuelle: ${error.message}`);
  }
}

// Fonction pour afficher les instructions de configuration
function showGoogleCloudInstructions(fingerprints) {
  console.log('');
  console.log('📋 INSTRUCTIONS POUR GOOGLE CLOUD CONSOLE:');
  console.log('==========================================');
  console.log('');
  console.log('1. 🌐 Accédez à Google Cloud Console:');
  console.log('   https://console.cloud.google.com/');
  console.log('');
  console.log('2. 📂 Naviguez vers "APIs & Services" > "Credentials"');
  console.log('');
  console.log('3. 🔑 Sélectionnez votre clé API:');
  console.log('   AIzaSyCUSlG6L03l-nE5SH9Rm8sHQLZRKuRhD3s');
  console.log('');
  console.log('4. 🔒 Configurez les restrictions d\'application:');
  console.log('   - Type: Applications Android');
  console.log('   - Nom de package: com.livraisonafrique.mobile');
  
  if (fingerprints && fingerprints.sha1) {
    console.log(`   - Empreinte SHA-1: ${fingerprints.sha1}`);
  } else {
    console.log('   - Empreinte SHA-1: [À générer avec keytool]');
  }
  
  console.log('');
  console.log('5. 🌍 Vérifiez que ces APIs sont activées:');
  console.log('   - Maps SDK for Android');
  console.log('   - Maps SDK for iOS');
  console.log('   - Places API');
  console.log('   - Directions API');
  console.log('   - Geocoding API');
  console.log('');
}

// Fonction pour afficher les méthodes alternatives
function showAlternativeMethods() {
  console.log('💡 MÉTHODES ALTERNATIVES:');
  console.log('========================');
  console.log('');
  console.log('1. 🏗️  Android Studio:');
  console.log('   - Ouvrez le projet Android');
  console.log('   - Build > Generate Signed Bundle/APK');
  console.log('   - Sélectionnez le keystore de débogage');
  console.log('   - Cliquez sur "View Certificate"');
  console.log('');
  console.log('2. 🔧 Gradle:');
  console.log('   - cd android');
  console.log('   - ./gradlew signingReport');
  console.log('');
  console.log('3. 🖥️  Ligne de commande (si Java est installé):');
  console.log('   keytool -list -v -keystore android/app/debug.keystore \\');
  console.log('           -alias androiddebugkey -storepass android -keypass android');
  console.log('');
}

// Fonction principale
function main() {
  const keystorePath = findKeystore();
  
  if (!keystorePath) {
    console.log('');
    showAlternativeMethods();
    process.exit(1);
  }
  
  console.log('');
  console.log('📋 Informations du certificat:');
  console.log(`   Keystore: ${keystorePath}`);
  console.log('   Alias: androiddebugkey');
  console.log('   Store Password: android');
  console.log('   Key Password: android');
  console.log('');
  
  const keytoolPath = findKeytool();
  let fingerprints = null;
  
  if (keytoolPath) {
    fingerprints = extractWithKeytool(keystorePath, keytoolPath);
  } else {
    console.log('');
    console.log('⚠️  Keytool non disponible, tentative d\'analyse manuelle...');
    analyzeKeystoreManually(keystorePath);
  }
  
  showGoogleCloudInstructions(fingerprints);
  
  if (!fingerprints || !fingerprints.sha1) {
    showAlternativeMethods();
  }
  
  console.log('🎯 Script terminé!');
}

// Exécution
main();
