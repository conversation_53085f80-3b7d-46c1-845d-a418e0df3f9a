  AsmClassVisitorFactory %com.android.build.api.instrumentation  ClassContext %com.android.build.api.instrumentation  	ClassData %com.android.build.api.instrumentation  FramesComputationMode %com.android.build.api.instrumentation  InstrumentationContext %com.android.build.api.instrumentation  InstrumentationParameters %com.android.build.api.instrumentation  InstrumentationScope %com.android.build.api.instrumentation  	className /com.android.build.api.instrumentation.ClassData  'COMPUTE_FRAMES_FOR_INSTRUMENTED_METHODS ;com.android.build.api.instrumentation.FramesComputationMode  
apiVersion <com.android.build.api.instrumentation.InstrumentationContext  ALL :com.android.build.api.instrumentation.InstrumentationScope  AndroidComponentsExtension com.android.build.api.variant  Variant com.android.build.api.variant  VariantSelector com.android.build.api.variant  
onVariants 8com.android.build.api.variant.AndroidComponentsExtension  selector 8com.android.build.api.variant.AndroidComponentsExtension  setAsmFramesComputationMode -com.android.build.api.variant.Instrumentation  transformClassesWith -com.android.build.api.variant.Instrumentation  	buildType %com.android.build.api.variant.Variant  instrumentation %com.android.build.api.variant.Variant  name %com.android.build.api.variant.Variant  all -com.android.build.api.variant.VariantSelector  ReactExtension com.facebook.react  	Companion !com.facebook.react.ReactExtension  debuggableVariants !com.facebook.react.ReactExtension  equals !com.facebook.react.ReactExtension  AndroidComponentsExtension expo.modules.devlauncher  Array expo.modules.devlauncher  Boolean expo.modules.devlauncher  DevLauncherClassVisitorFactory expo.modules.devlauncher  DevLauncherPlugin expo.modules.devlauncher  FramesComputationMode expo.modules.devlauncher  InstrumentationScope expo.modules.devlauncher  Int expo.modules.devlauncher  
LoggerFactory expo.modules.devlauncher  OkHttpClassVisitor expo.modules.devlauncher  OkHttpClientCustomBuildMethod expo.modules.devlauncher  Opcodes expo.modules.devlauncher  ReactExtension expo.modules.devlauncher  String expo.modules.devlauncher  System expo.modules.devlauncher  any expo.modules.devlauncher  equals expo.modules.devlauncher  getValue expo.modules.devlauncher  java expo.modules.devlauncher  lazy expo.modules.devlauncher  listOf expo.modules.devlauncher  logger expo.modules.devlauncher  provideDelegate expo.modules.devlauncher  	toBoolean expo.modules.devlauncher  AndroidComponentsExtension *expo.modules.devlauncher.DevLauncherPlugin  Array *expo.modules.devlauncher.DevLauncherPlugin  AsmClassVisitorFactory *expo.modules.devlauncher.DevLauncherPlugin  Boolean *expo.modules.devlauncher.DevLauncherPlugin  ClassContext *expo.modules.devlauncher.DevLauncherPlugin  	ClassData *expo.modules.devlauncher.DevLauncherPlugin  ClassVisitor *expo.modules.devlauncher.DevLauncherPlugin  	Companion *expo.modules.devlauncher.DevLauncherPlugin  DevLauncherClassVisitorFactory *expo.modules.devlauncher.DevLauncherPlugin  DevLauncherPlugin *expo.modules.devlauncher.DevLauncherPlugin  DevLauncherPluginParameters *expo.modules.devlauncher.DevLauncherPlugin  FramesComputationMode *expo.modules.devlauncher.DevLauncherPlugin  Input *expo.modules.devlauncher.DevLauncherPlugin  InstrumentationParameters *expo.modules.devlauncher.DevLauncherPlugin  InstrumentationScope *expo.modules.devlauncher.DevLauncherPlugin  Int *expo.modules.devlauncher.DevLauncherPlugin  
LoggerFactory *expo.modules.devlauncher.DevLauncherPlugin  
MethodVisitor *expo.modules.devlauncher.DevLauncherPlugin  OkHttpClassVisitor *expo.modules.devlauncher.DevLauncherPlugin  OkHttpClientCustomBuildMethod *expo.modules.devlauncher.DevLauncherPlugin  Opcodes *expo.modules.devlauncher.DevLauncherPlugin  Optional *expo.modules.devlauncher.DevLauncherPlugin  Project *expo.modules.devlauncher.DevLauncherPlugin  Property *expo.modules.devlauncher.DevLauncherPlugin  ReactExtension *expo.modules.devlauncher.DevLauncherPlugin  String *expo.modules.devlauncher.DevLauncherPlugin  System *expo.modules.devlauncher.DevLauncherPlugin  Variant *expo.modules.devlauncher.DevLauncherPlugin  any *expo.modules.devlauncher.DevLauncherPlugin  equals *expo.modules.devlauncher.DevLauncherPlugin  getANY *expo.modules.devlauncher.DevLauncherPlugin  getAny *expo.modules.devlauncher.DevLauncherPlugin  	getEQUALS *expo.modules.devlauncher.DevLauncherPlugin  	getEquals *expo.modules.devlauncher.DevLauncherPlugin  	getLOGGER *expo.modules.devlauncher.DevLauncherPlugin  	getLogger *expo.modules.devlauncher.DevLauncherPlugin  getTOBoolean *expo.modules.devlauncher.DevLauncherPlugin  getToBoolean *expo.modules.devlauncher.DevLauncherPlugin  getValue *expo.modules.devlauncher.DevLauncherPlugin  isDebugVariant *expo.modules.devlauncher.DevLauncherPlugin  java *expo.modules.devlauncher.DevLauncherPlugin  lazy *expo.modules.devlauncher.DevLauncherPlugin  listOf *expo.modules.devlauncher.DevLauncherPlugin  logger *expo.modules.devlauncher.DevLauncherPlugin  provideDelegate *expo.modules.devlauncher.DevLauncherPlugin  	toBoolean *expo.modules.devlauncher.DevLauncherPlugin  AndroidComponentsExtension 4expo.modules.devlauncher.DevLauncherPlugin.Companion  Array 4expo.modules.devlauncher.DevLauncherPlugin.Companion  AsmClassVisitorFactory 4expo.modules.devlauncher.DevLauncherPlugin.Companion  Boolean 4expo.modules.devlauncher.DevLauncherPlugin.Companion  ClassContext 4expo.modules.devlauncher.DevLauncherPlugin.Companion  	ClassData 4expo.modules.devlauncher.DevLauncherPlugin.Companion  ClassVisitor 4expo.modules.devlauncher.DevLauncherPlugin.Companion  DevLauncherClassVisitorFactory 4expo.modules.devlauncher.DevLauncherPlugin.Companion  DevLauncherPlugin 4expo.modules.devlauncher.DevLauncherPlugin.Companion  FramesComputationMode 4expo.modules.devlauncher.DevLauncherPlugin.Companion  Input 4expo.modules.devlauncher.DevLauncherPlugin.Companion  InstrumentationParameters 4expo.modules.devlauncher.DevLauncherPlugin.Companion  InstrumentationScope 4expo.modules.devlauncher.DevLauncherPlugin.Companion  Int 4expo.modules.devlauncher.DevLauncherPlugin.Companion  
LoggerFactory 4expo.modules.devlauncher.DevLauncherPlugin.Companion  
MethodVisitor 4expo.modules.devlauncher.DevLauncherPlugin.Companion  OkHttpClassVisitor 4expo.modules.devlauncher.DevLauncherPlugin.Companion  OkHttpClientCustomBuildMethod 4expo.modules.devlauncher.DevLauncherPlugin.Companion  Opcodes 4expo.modules.devlauncher.DevLauncherPlugin.Companion  Optional 4expo.modules.devlauncher.DevLauncherPlugin.Companion  Project 4expo.modules.devlauncher.DevLauncherPlugin.Companion  Property 4expo.modules.devlauncher.DevLauncherPlugin.Companion  ReactExtension 4expo.modules.devlauncher.DevLauncherPlugin.Companion  String 4expo.modules.devlauncher.DevLauncherPlugin.Companion  System 4expo.modules.devlauncher.DevLauncherPlugin.Companion  Variant 4expo.modules.devlauncher.DevLauncherPlugin.Companion  any 4expo.modules.devlauncher.DevLauncherPlugin.Companion  equals 4expo.modules.devlauncher.DevLauncherPlugin.Companion  getANY 4expo.modules.devlauncher.DevLauncherPlugin.Companion  getAny 4expo.modules.devlauncher.DevLauncherPlugin.Companion  	getEQUALS 4expo.modules.devlauncher.DevLauncherPlugin.Companion  	getEquals 4expo.modules.devlauncher.DevLauncherPlugin.Companion  getGETValue 4expo.modules.devlauncher.DevLauncherPlugin.Companion  getGetValue 4expo.modules.devlauncher.DevLauncherPlugin.Companion  getLAZY 4expo.modules.devlauncher.DevLauncherPlugin.Companion  	getLISTOf 4expo.modules.devlauncher.DevLauncherPlugin.Companion  getLazy 4expo.modules.devlauncher.DevLauncherPlugin.Companion  	getListOf 4expo.modules.devlauncher.DevLauncherPlugin.Companion  getPROVIDEDelegate 4expo.modules.devlauncher.DevLauncherPlugin.Companion  getProvideDelegate 4expo.modules.devlauncher.DevLauncherPlugin.Companion  getTOBoolean 4expo.modules.devlauncher.DevLauncherPlugin.Companion  getToBoolean 4expo.modules.devlauncher.DevLauncherPlugin.Companion  getValue 4expo.modules.devlauncher.DevLauncherPlugin.Companion  java 4expo.modules.devlauncher.DevLauncherPlugin.Companion  lazy 4expo.modules.devlauncher.DevLauncherPlugin.Companion  listOf 4expo.modules.devlauncher.DevLauncherPlugin.Companion  logger 4expo.modules.devlauncher.DevLauncherPlugin.Companion  provideDelegate 4expo.modules.devlauncher.DevLauncherPlugin.Companion  	toBoolean 4expo.modules.devlauncher.DevLauncherPlugin.Companion  Boolean Iexpo.modules.devlauncher.DevLauncherPlugin.DevLauncherClassVisitorFactory  ClassContext Iexpo.modules.devlauncher.DevLauncherPlugin.DevLauncherClassVisitorFactory  	ClassData Iexpo.modules.devlauncher.DevLauncherPlugin.DevLauncherClassVisitorFactory  ClassVisitor Iexpo.modules.devlauncher.DevLauncherPlugin.DevLauncherClassVisitorFactory  OkHttpClassVisitor Iexpo.modules.devlauncher.DevLauncherPlugin.DevLauncherClassVisitorFactory  	getLISTOf Iexpo.modules.devlauncher.DevLauncherPlugin.DevLauncherClassVisitorFactory  	getListOf Iexpo.modules.devlauncher.DevLauncherPlugin.DevLauncherClassVisitorFactory  instrumentationContext Iexpo.modules.devlauncher.DevLauncherPlugin.DevLauncherClassVisitorFactory  listOf Iexpo.modules.devlauncher.DevLauncherPlugin.DevLauncherClassVisitorFactory  
parameters Iexpo.modules.devlauncher.DevLauncherPlugin.DevLauncherClassVisitorFactory  Boolean Fexpo.modules.devlauncher.DevLauncherPlugin.DevLauncherPluginParameters  Input Fexpo.modules.devlauncher.DevLauncherPlugin.DevLauncherPluginParameters  Optional Fexpo.modules.devlauncher.DevLauncherPlugin.DevLauncherPluginParameters  Property Fexpo.modules.devlauncher.DevLauncherPlugin.DevLauncherPluginParameters  enabled Fexpo.modules.devlauncher.DevLauncherPlugin.DevLauncherPluginParameters  Array =expo.modules.devlauncher.DevLauncherPlugin.OkHttpClassVisitor  ClassContext =expo.modules.devlauncher.DevLauncherPlugin.OkHttpClassVisitor  ClassVisitor =expo.modules.devlauncher.DevLauncherPlugin.OkHttpClassVisitor  Int =expo.modules.devlauncher.DevLauncherPlugin.OkHttpClassVisitor  
MethodVisitor =expo.modules.devlauncher.DevLauncherPlugin.OkHttpClassVisitor  OkHttpClientCustomBuildMethod =expo.modules.devlauncher.DevLauncherPlugin.OkHttpClassVisitor  String =expo.modules.devlauncher.DevLauncherPlugin.OkHttpClassVisitor  api =expo.modules.devlauncher.DevLauncherPlugin.OkHttpClassVisitor  Int Hexpo.modules.devlauncher.DevLauncherPlugin.OkHttpClientCustomBuildMethod  
MethodVisitor Hexpo.modules.devlauncher.DevLauncherPlugin.OkHttpClientCustomBuildMethod  Opcodes Hexpo.modules.devlauncher.DevLauncherPlugin.OkHttpClientCustomBuildMethod  	visitInsn Hexpo.modules.devlauncher.DevLauncherPlugin.OkHttpClientCustomBuildMethod  visitMethodInsn Hexpo.modules.devlauncher.DevLauncherPlugin.OkHttpClientCustomBuildMethod  
visitTypeInsn Hexpo.modules.devlauncher.DevLauncherPlugin.OkHttpClientCustomBuildMethod  visitVarInsn Hexpo.modules.devlauncher.DevLauncherPlugin.OkHttpClientCustomBuildMethod  AndroidComponentsExtension 	java.lang  Class 	java.lang  DevLauncherClassVisitorFactory 	java.lang  DevLauncherPlugin 	java.lang  FramesComputationMode 	java.lang  InstrumentationScope 	java.lang  
LoggerFactory 	java.lang  OkHttpClassVisitor 	java.lang  OkHttpClientCustomBuildMethod 	java.lang  Opcodes 	java.lang  ReactExtension 	java.lang  System 	java.lang  any 	java.lang  equals 	java.lang  getValue 	java.lang  java 	java.lang  lazy 	java.lang  listOf 	java.lang  logger 	java.lang  provideDelegate 	java.lang  	toBoolean 	java.lang  getenv java.lang.System  AndroidComponentsExtension kotlin  Any kotlin  Array kotlin  Boolean kotlin  DevLauncherClassVisitorFactory kotlin  DevLauncherPlugin kotlin  FramesComputationMode kotlin  	Function0 kotlin  	Function1 kotlin  InstrumentationScope kotlin  Int kotlin  Lazy kotlin  
LoggerFactory kotlin  Nothing kotlin  OkHttpClassVisitor kotlin  OkHttpClientCustomBuildMethod kotlin  Opcodes kotlin  ReactExtension kotlin  String kotlin  System kotlin  any kotlin  equals kotlin  getValue kotlin  java kotlin  lazy kotlin  listOf kotlin  logger kotlin  provideDelegate kotlin  	toBoolean kotlin  getGETValue kotlin.Lazy  getGetValue kotlin.Lazy  getPROVIDEDelegate kotlin.Lazy  getProvideDelegate kotlin.Lazy  getValue kotlin.Lazy  provideDelegate kotlin.Lazy  	getEQUALS 
kotlin.String  	getEquals 
kotlin.String  getTOBoolean 
kotlin.String  getToBoolean 
kotlin.String  AndroidComponentsExtension kotlin.annotation  DevLauncherClassVisitorFactory kotlin.annotation  DevLauncherPlugin kotlin.annotation  FramesComputationMode kotlin.annotation  InstrumentationScope kotlin.annotation  
LoggerFactory kotlin.annotation  OkHttpClassVisitor kotlin.annotation  OkHttpClientCustomBuildMethod kotlin.annotation  Opcodes kotlin.annotation  ReactExtension kotlin.annotation  System kotlin.annotation  any kotlin.annotation  equals kotlin.annotation  getValue kotlin.annotation  java kotlin.annotation  lazy kotlin.annotation  listOf kotlin.annotation  logger kotlin.annotation  provideDelegate kotlin.annotation  	toBoolean kotlin.annotation  AndroidComponentsExtension kotlin.collections  DevLauncherClassVisitorFactory kotlin.collections  DevLauncherPlugin kotlin.collections  FramesComputationMode kotlin.collections  InstrumentationScope kotlin.collections  List kotlin.collections  
LoggerFactory kotlin.collections  
MutableMap kotlin.collections  OkHttpClassVisitor kotlin.collections  OkHttpClientCustomBuildMethod kotlin.collections  Opcodes kotlin.collections  ReactExtension kotlin.collections  System kotlin.collections  any kotlin.collections  equals kotlin.collections  getValue kotlin.collections  java kotlin.collections  lazy kotlin.collections  listOf kotlin.collections  logger kotlin.collections  provideDelegate kotlin.collections  	toBoolean kotlin.collections  getANY kotlin.collections.MutableList  getAny kotlin.collections.MutableList  AndroidComponentsExtension kotlin.comparisons  DevLauncherClassVisitorFactory kotlin.comparisons  DevLauncherPlugin kotlin.comparisons  FramesComputationMode kotlin.comparisons  InstrumentationScope kotlin.comparisons  
LoggerFactory kotlin.comparisons  OkHttpClassVisitor kotlin.comparisons  OkHttpClientCustomBuildMethod kotlin.comparisons  Opcodes kotlin.comparisons  ReactExtension kotlin.comparisons  System kotlin.comparisons  any kotlin.comparisons  equals kotlin.comparisons  getValue kotlin.comparisons  java kotlin.comparisons  lazy kotlin.comparisons  listOf kotlin.comparisons  logger kotlin.comparisons  provideDelegate kotlin.comparisons  	toBoolean kotlin.comparisons  AndroidComponentsExtension 	kotlin.io  DevLauncherClassVisitorFactory 	kotlin.io  DevLauncherPlugin 	kotlin.io  FramesComputationMode 	kotlin.io  InstrumentationScope 	kotlin.io  
LoggerFactory 	kotlin.io  OkHttpClassVisitor 	kotlin.io  OkHttpClientCustomBuildMethod 	kotlin.io  Opcodes 	kotlin.io  ReactExtension 	kotlin.io  System 	kotlin.io  any 	kotlin.io  equals 	kotlin.io  getValue 	kotlin.io  java 	kotlin.io  lazy 	kotlin.io  listOf 	kotlin.io  logger 	kotlin.io  provideDelegate 	kotlin.io  	toBoolean 	kotlin.io  AndroidComponentsExtension 
kotlin.jvm  DevLauncherClassVisitorFactory 
kotlin.jvm  DevLauncherPlugin 
kotlin.jvm  FramesComputationMode 
kotlin.jvm  InstrumentationScope 
kotlin.jvm  
LoggerFactory 
kotlin.jvm  OkHttpClassVisitor 
kotlin.jvm  OkHttpClientCustomBuildMethod 
kotlin.jvm  Opcodes 
kotlin.jvm  ReactExtension 
kotlin.jvm  System 
kotlin.jvm  any 
kotlin.jvm  equals 
kotlin.jvm  getValue 
kotlin.jvm  java 
kotlin.jvm  lazy 
kotlin.jvm  listOf 
kotlin.jvm  logger 
kotlin.jvm  provideDelegate 
kotlin.jvm  	toBoolean 
kotlin.jvm  AndroidComponentsExtension 
kotlin.ranges  DevLauncherClassVisitorFactory 
kotlin.ranges  DevLauncherPlugin 
kotlin.ranges  FramesComputationMode 
kotlin.ranges  InstrumentationScope 
kotlin.ranges  
LoggerFactory 
kotlin.ranges  OkHttpClassVisitor 
kotlin.ranges  OkHttpClientCustomBuildMethod 
kotlin.ranges  Opcodes 
kotlin.ranges  ReactExtension 
kotlin.ranges  System 
kotlin.ranges  any 
kotlin.ranges  equals 
kotlin.ranges  getValue 
kotlin.ranges  java 
kotlin.ranges  lazy 
kotlin.ranges  listOf 
kotlin.ranges  logger 
kotlin.ranges  provideDelegate 
kotlin.ranges  	toBoolean 
kotlin.ranges  KClass kotlin.reflect  getJAVA kotlin.reflect.KClass  getJava kotlin.reflect.KClass  java kotlin.reflect.KClass  AndroidComponentsExtension kotlin.sequences  DevLauncherClassVisitorFactory kotlin.sequences  DevLauncherPlugin kotlin.sequences  FramesComputationMode kotlin.sequences  InstrumentationScope kotlin.sequences  
LoggerFactory kotlin.sequences  OkHttpClassVisitor kotlin.sequences  OkHttpClientCustomBuildMethod kotlin.sequences  Opcodes kotlin.sequences  ReactExtension kotlin.sequences  System kotlin.sequences  any kotlin.sequences  equals kotlin.sequences  getValue kotlin.sequences  java kotlin.sequences  lazy kotlin.sequences  listOf kotlin.sequences  logger kotlin.sequences  provideDelegate kotlin.sequences  	toBoolean kotlin.sequences  AndroidComponentsExtension kotlin.text  DevLauncherClassVisitorFactory kotlin.text  DevLauncherPlugin kotlin.text  FramesComputationMode kotlin.text  InstrumentationScope kotlin.text  
LoggerFactory kotlin.text  OkHttpClassVisitor kotlin.text  OkHttpClientCustomBuildMethod kotlin.text  Opcodes kotlin.text  ReactExtension kotlin.text  System kotlin.text  any kotlin.text  equals kotlin.text  getValue kotlin.text  java kotlin.text  lazy kotlin.text  listOf kotlin.text  logger kotlin.text  provideDelegate kotlin.text  	toBoolean kotlin.text  Plugin org.gradle.api  Project org.gradle.api  equals org.gradle.api.Project  
extensions org.gradle.api.Project  findProject org.gradle.api.Project  
getEXTENSIONS org.gradle.api.Project  
getExtensions org.gradle.api.Project  
getPROPERTIES org.gradle.api.Project  
getProperties org.gradle.api.Project  
properties org.gradle.api.Project  
setExtensions org.gradle.api.Project  
setProperties org.gradle.api.Project  
findByType )org.gradle.api.plugins.ExtensionContainer  	getByType )org.gradle.api.plugins.ExtensionContainer  Property org.gradle.api.provider  get $org.gradle.api.provider.ListProperty  get  org.gradle.api.provider.Property  	getOrElse  org.gradle.api.provider.Property  set  org.gradle.api.provider.Property  Input org.gradle.api.tasks  Optional org.gradle.api.tasks  ClassVisitor org.objectweb.asm  
MethodVisitor org.objectweb.asm  Opcodes org.objectweb.asm  Array org.objectweb.asm.ClassVisitor  ClassContext org.objectweb.asm.ClassVisitor  ClassVisitor org.objectweb.asm.ClassVisitor  Int org.objectweb.asm.ClassVisitor  
MethodVisitor org.objectweb.asm.ClassVisitor  OkHttpClientCustomBuildMethod org.objectweb.asm.ClassVisitor  String org.objectweb.asm.ClassVisitor  visitMethod org.objectweb.asm.ClassVisitor  Int org.objectweb.asm.MethodVisitor  
MethodVisitor org.objectweb.asm.MethodVisitor  Opcodes org.objectweb.asm.MethodVisitor  	visitInsn org.objectweb.asm.MethodVisitor  visitMethodInsn org.objectweb.asm.MethodVisitor  
visitTypeInsn org.objectweb.asm.MethodVisitor  visitVarInsn org.objectweb.asm.MethodVisitor  ALOAD org.objectweb.asm.Opcodes  ARETURN org.objectweb.asm.Opcodes  	CHECKCAST org.objectweb.asm.Opcodes  DUP org.objectweb.asm.Opcodes  
INVOKESPECIAL org.objectweb.asm.Opcodes  
INVOKEVIRTUAL org.objectweb.asm.Opcodes  NEW org.objectweb.asm.Opcodes  Logger 	org.slf4j  
LoggerFactory 	org.slf4j  warn org.slf4j.Logger  	getLogger org.slf4j.LoggerFactory                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 