{"logs": [{"outputFile": "com.eliseedev.mientiorlivraison.app-mergeDebugResources-71:/values-nb/values-nb.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\1b1d6e3a1eafdeb637916b2c04d0a37e\\transformed\\exoplayer-core-2.18.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,193,257,328,408,486,580,677", "endColumns": "75,61,63,70,79,77,93,96,70", "endOffsets": "126,188,252,323,403,481,575,672,743"}, "to": {"startLines": "113,114,115,116,117,118,119,120,121", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "9638,9714,9776,9840,9911,9991,10069,10163,10260", "endColumns": "75,61,63,70,79,77,93,96,70", "endOffsets": "9709,9771,9835,9906,9986,10064,10158,10255,10326"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\306a6bcd4bd045cfd61a3c5eb43578e4\\transformed\\core-1.13.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,447,555,661,781", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "144,246,343,442,550,656,776,877"}, "to": {"startLines": "55,56,57,58,59,60,61,215", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3965,4059,4161,4258,4357,4465,4571,17227", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "4054,4156,4253,4352,4460,4566,4686,17323"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8cc367fa8ce0cb8acb1d9a2937a5991c\\transformed\\Android-Image-Cropper-4.3.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,164,211,268,312,378,442,515,568", "endColumns": "108,46,56,43,65,63,72,52,63", "endOffsets": "159,206,263,307,373,437,510,563,627"}, "to": {"startLines": "84,85,86,144,145,146,147,148,210", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "7315,7424,7471,11858,11902,11968,12032,12105,16849", "endColumns": "108,46,56,43,65,63,72,52,63", "endOffsets": "7419,7466,7523,11897,11963,12027,12100,12153,16908"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8fb4e20b948a58ead7b50b7ad3e15312\\transformed\\appcompat-1.7.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,303,417,503,603,716,793,868,959,1052,1146,1240,1340,1433,1528,1626,1717,1808,1886,1989,2087,2183,2287,2386,2487,2640,2737", "endColumns": "102,94,113,85,99,112,76,74,90,92,93,93,99,92,94,97,90,90,77,102,97,95,103,98,100,152,96,79", "endOffsets": "203,298,412,498,598,711,788,863,954,1047,1141,1235,1335,1428,1523,1621,1712,1803,1881,1984,2082,2178,2282,2381,2482,2635,2732,2812"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,211", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "928,1031,1126,1240,1326,1426,1539,1616,1691,1782,1875,1969,2063,2163,2256,2351,2449,2540,2631,2709,2812,2910,3006,3110,3209,3310,3463,16913", "endColumns": "102,94,113,85,99,112,76,74,90,92,93,93,99,92,94,97,90,90,77,102,97,95,103,98,100,152,96,79", "endOffsets": "1026,1121,1235,1321,1421,1534,1611,1686,1777,1870,1964,2058,2158,2251,2346,2444,2535,2626,2704,2807,2905,3001,3105,3204,3305,3458,3555,16988"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cea641498eb0b29f919daa30d9671bd0\\transformed\\play-services-basement-18.3.0\\res\\values-nb\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "125", "endOffsets": "320"}, "to": {"startLines": "73", "startColumns": "4", "startOffsets": "6000", "endColumns": "129", "endOffsets": "6125"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f8296c347bb3d1ec471e3402800c1db5\\transformed\\material-1.12.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,261,338,411,498,586,666,765,884,966,1025,1089,1181,1249,1309,1396,1460,1522,1586,1654,1719,1773,1882,1940,2002,2056,2131,2251,2333,2410,2500,2584,2664,2798,2876,2956,3079,3167,3245,3299,3350,3416,3484,3558,3629,3705,3776,3854,3924,3994,4094,4183,4261,4349,4439,4511,4583,4667,4718,4796,4862,4943,5026,5088,5152,5215,5284,5384,5488,5581,5681,5739,5794,5872,5956,6034", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,76,72,86,87,79,98,118,81,58,63,91,67,59,86,63,61,63,67,64,53,108,57,61,53,74,119,81,76,89,83,79,133,77,79,122,87,77,53,50,65,67,73,70,75,70,77,69,69,99,88,77,87,89,71,71,83,50,77,65,80,82,61,63,62,68,99,103,92,99,57,54,77,83,77,71", "endOffsets": "256,333,406,493,581,661,760,879,961,1020,1084,1176,1244,1304,1391,1455,1517,1581,1649,1714,1768,1877,1935,1997,2051,2126,2246,2328,2405,2495,2579,2659,2793,2871,2951,3074,3162,3240,3294,3345,3411,3479,3553,3624,3700,3771,3849,3919,3989,4089,4178,4256,4344,4434,4506,4578,4662,4713,4791,4857,4938,5021,5083,5147,5210,5279,5379,5483,5576,5676,5734,5789,5867,5951,6029,6101"}, "to": {"startLines": "19,50,51,52,53,54,62,63,64,87,88,140,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,212,213,214", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "767,3560,3637,3710,3797,3885,4691,4790,4909,7528,7587,11456,12158,12226,12286,12373,12437,12499,12563,12631,12696,12750,12859,12917,12979,13033,13108,13228,13310,13387,13477,13561,13641,13775,13853,13933,14056,14144,14222,14276,14327,14393,14461,14535,14606,14682,14753,14831,14901,14971,15071,15160,15238,15326,15416,15488,15560,15644,15695,15773,15839,15920,16003,16065,16129,16192,16261,16361,16465,16558,16658,16716,16771,16993,17077,17155", "endLines": "22,50,51,52,53,54,62,63,64,87,88,140,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,212,213,214", "endColumns": "12,76,72,86,87,79,98,118,81,58,63,91,67,59,86,63,61,63,67,64,53,108,57,61,53,74,119,81,76,89,83,79,133,77,79,122,87,77,53,50,65,67,73,70,75,70,77,69,69,99,88,77,87,89,71,71,83,50,77,65,80,82,61,63,62,68,99,103,92,99,57,54,77,83,77,71", "endOffsets": "923,3632,3705,3792,3880,3960,4785,4904,4986,7582,7646,11543,12221,12281,12368,12432,12494,12558,12626,12691,12745,12854,12912,12974,13028,13103,13223,13305,13382,13472,13556,13636,13770,13848,13928,14051,14139,14217,14271,14322,14388,14456,14530,14601,14677,14748,14826,14896,14966,15066,15155,15233,15321,15411,15483,15555,15639,15690,15768,15834,15915,15998,16060,16124,16187,16256,16356,16460,16553,16653,16711,16766,16844,17072,17150,17222"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ff199f796fece94789ea9bd928f0e62a\\transformed\\browser-1.6.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,165,266,378", "endColumns": "109,100,111,96", "endOffsets": "160,261,373,470"}, "to": {"startLines": "83,141,142,143", "startColumns": "4,4,4,4", "startOffsets": "7205,11548,11649,11761", "endColumns": "109,100,111,96", "endOffsets": "7310,11644,11756,11853"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2ab0477328e0ae05829c4cc214e49234\\transformed\\play-services-base-18.2.0\\res\\values-nb\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,450,572,677,829,955,1071,1170,1320,1423,1580,1704,1842,2014,2077,2135", "endColumns": "101,154,121,104,151,125,115,98,149,102,156,123,137,171,62,57,73", "endOffsets": "294,449,571,676,828,954,1070,1169,1319,1422,1579,1703,1841,2013,2076,2134,2208"}, "to": {"startLines": "65,66,67,68,69,70,71,72,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4991,5097,5256,5382,5491,5647,5777,5897,6130,6284,6391,6552,6680,6822,6998,7065,7127", "endColumns": "105,158,125,108,155,129,119,102,153,106,160,127,141,175,66,61,77", "endOffsets": "5092,5251,5377,5486,5642,5772,5892,5995,6279,6386,6547,6675,6817,6993,7060,7122,7200"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4407803e7a2d8d3f17c090093cb28e53\\transformed\\exoplayer-ui-2.18.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,285,482,672,758,843,921,1007,1095,1170,1234,1327,1418,1491,1558,1624,1694,1803,1913,2020,2093,2176,2252,2325,2428,2530,2594,2659,2712,2770,2818,2879,2949,3017,3083,3153,3217,3276,3340,3405,3471,3523,3583,3657,3731", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,85,84,77,85,87,74,63,92,90,72,66,65,69,108,109,106,72,82,75,72,102,101,63,64,52,57,47,60,69,67,65,69,63,58,63,64,65,51,59,73,73,52", "endOffsets": "280,477,667,753,838,916,1002,1090,1165,1229,1322,1413,1486,1553,1619,1689,1798,1908,2015,2088,2171,2247,2320,2423,2525,2589,2654,2707,2765,2813,2874,2944,3012,3078,3148,3212,3271,3335,3400,3466,3518,3578,3652,3726,3779"}, "to": {"startLines": "2,11,15,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,380,577,7651,7737,7822,7900,7986,8074,8149,8213,8306,8397,8470,8537,8603,8673,8782,8892,8999,9072,9155,9231,9304,9407,9509,9573,10331,10384,10442,10490,10551,10621,10689,10755,10825,10889,10948,11012,11077,11143,11195,11255,11329,11403", "endLines": "10,14,18,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139", "endColumns": "17,12,12,85,84,77,85,87,74,63,92,90,72,66,65,69,108,109,106,72,82,75,72,102,101,63,64,52,57,47,60,69,67,65,69,63,58,63,64,65,51,59,73,73,52", "endOffsets": "375,572,762,7732,7817,7895,7981,8069,8144,8208,8301,8392,8465,8532,8598,8668,8777,8887,8994,9067,9150,9226,9299,9402,9504,9568,9633,10379,10437,10485,10546,10616,10684,10750,10820,10884,10943,11007,11072,11138,11190,11250,11324,11398,11451"}}]}]}