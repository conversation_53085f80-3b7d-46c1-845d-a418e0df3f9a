,expo.modules.filesystem.AppDirectoriesModule$expo.modules.filesystem.CountingSink+expo.modules.filesystem.CountingRequestBody,expo.modules.filesystem.FilePermissionModule5expo.modules.filesystem.FileSystemOkHttpNullException><EMAIL>>expo.modules.filesystem.FileSystemUnreadableDirectoryException;expo.modules.filesystem.FileSystemCannotCreateFileException7expo.modules.filesystem.FileSystemFileNotFoundExceptionDexpo.modules.filesystem.FileSystemPendingPermissionsRequestException9expo.modules.filesystem.FileSystemCannotMoveFileException<expo.modules.filesystem.FileSystemUnsupportedSchemeException9expo.modules.filesystem.FileSystemCannotFindTaskException5expo.modules.filesystem.FileSystemCopyFailedException6expo.modules.filesystem.CookieHandlerNotFoundException.expo.modules.filesystem.FileSystemFileProvider(expo.modules.filesystem.FileSystemModule<expo.modules.filesystem.FileSystemModule.DownloadTaskHandler=expo.modules.filesystem.FileSystemModule.ProgressResponseBody)expo.modules.filesystem.FileSystemPackage#expo.modules.filesystem.InfoOptions'expo.modules.filesystem.DeletingOptions&expo.modules.filesystem.ReadingOptions$expo.modules.filesystem.EncodingType#expo.modules.filesystem.SessionType,expo.modules.filesystem.FileSystemUploadType,expo.modules.filesystem.MakeDirectoryOptions)expo.modules.filesystem.RelocatingOptions'expo.modules.filesystem.DownloadOptions&expo.modules.filesystem.WritingOptions/expo.modules.filesystem.FileSystemUploadOptions"expo.modules.filesystem.HttpMethod0expo.modules.filesystem.next.FileSystemDirectory+expo.modules.filesystem.next.FileSystemFile1expo.modules.filesystem.next.FileSystemFileHandle?expo.modules.filesystem.next.CopyOrMoveDirectoryToFileException7expo.modules.filesystem.next.InvalidTypeFolderException5expo.modules.filesystem.next.InvalidTypeFileException=expo.modules.filesystem.next.DestinationDoesNotExistException6expo.modules.filesystem.next.UnableToDownloadException4expo.modules.filesystem.next.UnableToDeleteException4expo.modules.filesystem.next.UnableToCreateException7expo.modules.filesystem.next.InvalidPermissionException8expo.modules.filesystem.next.UnableToReadHandleException9expo.modules.filesystem.next.UnableToWriteHandleException>expo.modules.filesystem.next.DestinationAlreadyExistsException1expo.modules.filesystem.next.FileSystemNextModule*expo.modules.filesystem.next.CreateOptions+expo.modules.filesystem.next.FileSystemPath                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           