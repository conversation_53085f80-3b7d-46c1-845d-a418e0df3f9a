/**
 * Service de géolocalisation pour Mientior Livraison
 * Intégration avec React Native Maps et Expo Location
 */

import * as Location from 'expo-location';
import { Alert, Platform } from 'react-native';
import { MAPS_CONFIG, log, logError } from '../config/environment';

// Types pour la géolocalisation
export interface LocationCoordinates {
  latitude: number;
  longitude: number;
}

export interface LocationData extends LocationCoordinates {
  accuracy?: number;
  altitude?: number;
  heading?: number;
  speed?: number;
  timestamp?: number;
}

export interface AddressComponent {
  street?: string;
  city?: string;
  region?: string;
  country?: string;
  postalCode?: string;
  formattedAddress?: string;
}

export interface LocationPermissionStatus {
  granted: boolean;
  canAskAgain: boolean;
  status: Location.PermissionStatus;
}

class LocationService {
  private static instance: LocationService;
  private currentLocation: LocationData | null = null;
  private watchId: Location.LocationSubscription | null = null;
  private locationCallbacks: ((location: LocationData) => void)[] = [];

  static getInstance(): LocationService {
    if (!LocationService.instance) {
      LocationService.instance = new LocationService();
    }
    return LocationService.instance;
  }

  /**
   * Demande les permissions de géolocalisation
   */
  async requestLocationPermission(): Promise<LocationPermissionStatus> {
    try {
      log('📍 Demande de permission de géolocalisation...');

      // Vérifier d'abord le statut actuel
      const { status: existingStatus } = await Location.getForegroundPermissionsAsync();
      
      if (existingStatus === 'granted') {
        log('✅ Permission de géolocalisation déjà accordée');
        return {
          granted: true,
          canAskAgain: true,
          status: existingStatus,
        };
      }

      // Demander la permission
      const { status, canAskAgain } = await Location.requestForegroundPermissionsAsync();
      
      if (status === 'granted') {
        log('✅ Permission de géolocalisation accordée');
        
        // Demander aussi la permission en arrière-plan pour les livreurs
        if (Platform.OS === 'android') {
          await Location.requestBackgroundPermissionsAsync();
        }
        
        return {
          granted: true,
          canAskAgain,
          status,
        };
      } else {
        logError('❌ Permission de géolocalisation refusée');
        return {
          granted: false,
          canAskAgain,
          status,
        };
      }
    } catch (error) {
      logError('❌ Erreur lors de la demande de permission:', error);
      return {
        granted: false,
        canAskAgain: false,
        status: 'denied' as Location.PermissionStatus,
      };
    }
  }

  /**
   * Obtient la position actuelle de l'utilisateur
   */
  async getCurrentLocation(): Promise<LocationData | null> {
    try {
      log('📍 Récupération de la position actuelle...');

      const permission = await this.requestLocationPermission();
      if (!permission.granted) {
        throw new Error('Permission de géolocalisation non accordée');
      }

      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.High,
        maximumAge: MAPS_CONFIG.location.maximumAge,
        timeout: MAPS_CONFIG.location.timeout,
      });

      const locationData: LocationData = {
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
        accuracy: location.coords.accuracy || undefined,
        altitude: location.coords.altitude || undefined,
        heading: location.coords.heading || undefined,
        speed: location.coords.speed || undefined,
        timestamp: location.timestamp,
      };

      this.currentLocation = locationData;
      log('✅ Position obtenue:', locationData);

      return locationData;
    } catch (error) {
      logError('❌ Erreur lors de la récupération de la position:', error);
      
      // Retourner la position par défaut (Abidjan)
      const defaultLocation: LocationData = {
        latitude: MAPS_CONFIG.defaultRegion.latitude,
        longitude: MAPS_CONFIG.defaultRegion.longitude,
        timestamp: Date.now(),
      };

      this.currentLocation = defaultLocation;
      return defaultLocation;
    }
  }

  /**
   * Démarre le suivi de la position en temps réel
   */
  async startLocationTracking(callback: (location: LocationData) => void): Promise<boolean> {
    try {
      log('📍 Démarrage du suivi de position...');

      const permission = await this.requestLocationPermission();
      if (!permission.granted) {
        throw new Error('Permission de géolocalisation non accordée');
      }

      // Ajouter le callback à la liste
      this.locationCallbacks.push(callback);

      // Si le suivi n'est pas déjà actif, le démarrer
      if (!this.watchId) {
        this.watchId = await Location.watchPositionAsync(
          {
            accuracy: Location.Accuracy.High,
            timeInterval: 15000, // 15 secondes
            distanceInterval: MAPS_CONFIG.location.distanceFilter,
          },
          (location) => {
            const locationData: LocationData = {
              latitude: location.coords.latitude,
              longitude: location.coords.longitude,
              accuracy: location.coords.accuracy || undefined,
              altitude: location.coords.altitude || undefined,
              heading: location.coords.heading || undefined,
              speed: location.coords.speed || undefined,
              timestamp: location.timestamp,
            };

            this.currentLocation = locationData;
            
            // Notifier tous les callbacks
            this.locationCallbacks.forEach(cb => cb(locationData));
          }
        );
      }

      log('✅ Suivi de position démarré');
      return true;
    } catch (error) {
      logError('❌ Erreur lors du démarrage du suivi:', error);
      return false;
    }
  }

  /**
   * Arrête le suivi de la position
   */
  async stopLocationTracking(callback?: (location: LocationData) => void): Promise<void> {
    try {
      if (callback) {
        // Retirer un callback spécifique
        this.locationCallbacks = this.locationCallbacks.filter(cb => cb !== callback);
      } else {
        // Vider tous les callbacks
        this.locationCallbacks = [];
      }

      // Si plus de callbacks, arrêter le suivi
      if (this.locationCallbacks.length === 0 && this.watchId) {
        this.watchId.remove();
        this.watchId = null;
        log('📍 Suivi de position arrêté');
      }
    } catch (error) {
      logError('❌ Erreur lors de l\'arrêt du suivi:', error);
    }
  }

  /**
   * Calcule la distance entre deux points en kilomètres
   */
  calculateDistance(point1: LocationCoordinates, point2: LocationCoordinates): number {
    const R = 6371; // Rayon de la Terre en km
    const dLat = this.deg2rad(point2.latitude - point1.latitude);
    const dLon = this.deg2rad(point2.longitude - point1.longitude);
    const a = 
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(this.deg2rad(point1.latitude)) * Math.cos(this.deg2rad(point2.latitude)) * 
      Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  }

  /**
   * Convertit des degrés en radians
   */
  private deg2rad(deg: number): number {
    return deg * (Math.PI/180);
  }

  /**
   * Géocodage inverse : obtient l'adresse à partir des coordonnées
   */
  async reverseGeocode(coordinates: LocationCoordinates): Promise<AddressComponent | null> {
    try {
      log('🗺️ Géocodage inverse:', coordinates);

      // Créer une promesse avec timeout personnalisé
      const reverseGeocodeWithTimeout = () => {
        return new Promise<Location.LocationGeocodedAddress[]>((resolve, reject) => {
          const timeoutId = setTimeout(() => {
            reject(new Error('Timeout du géocodage inverse (3s)'));
          }, 3000); // 3 secondes timeout

          Location.reverseGeocodeAsync(coordinates)
            .then((results) => {
              clearTimeout(timeoutId);
              resolve(results);
            })
            .catch((error) => {
              clearTimeout(timeoutId);
              reject(error);
            });
        });
      };

      const result = await reverseGeocodeWithTimeout();

      if (result && result.length > 0) {
        const address = result[0];
        const addressComponent: AddressComponent = {
          street: address.street || undefined,
          city: address.city || undefined,
          region: address.region || undefined,
          country: address.country || undefined,
          postalCode: address.postalCode || undefined,
          formattedAddress: this.formatAddress(address),
        };

        log('✅ Adresse trouvée:', addressComponent);
        return addressComponent;
      }

      return null;
    } catch (error) {
      logError('❌ Erreur lors du géocodage inverse:', error);
      return null;
    }
  }

  /**
   * Géocodage : obtient les coordonnées à partir d'une adresse
   */
  async geocode(address: string): Promise<LocationCoordinates | null> {
    try {
      log('🗺️ Géocodage:', address);

      const result = await Location.geocodeAsync(address);
      
      if (result && result.length > 0) {
        const coordinates: LocationCoordinates = {
          latitude: result[0].latitude,
          longitude: result[0].longitude,
        };

        log('✅ Coordonnées trouvées:', coordinates);
        return coordinates;
      }

      return null;
    } catch (error) {
      logError('❌ Erreur lors du géocodage:', error);
      return null;
    }
  }

  /**
   * Formate une adresse de manière lisible
   */
  private formatAddress(address: Location.LocationGeocodedAddress): string {
    const parts = [
      address.street,
      address.city,
      address.region,
      address.country,
    ].filter(Boolean);

    return parts.join(', ');
  }

  /**
   * Vérifie si une position est dans une zone donnée
   */
  isLocationInRadius(
    center: LocationCoordinates,
    point: LocationCoordinates,
    radiusKm: number
  ): boolean {
    const distance = this.calculateDistance(center, point);
    return distance <= radiusKm;
  }

  /**
   * Obtient la position actuelle en cache
   */
  getCachedLocation(): LocationData | null {
    return this.currentLocation;
  }

  /**
   * Affiche une alerte pour demander l'activation de la géolocalisation
   */
  showLocationPermissionAlert(): void {
    Alert.alert(
      'Géolocalisation requise',
      'Mientior Livraison a besoin d\'accéder à votre position pour vous proposer les meilleurs services de livraison.',
      [
        {
          text: 'Annuler',
          style: 'cancel',
        },
        {
          text: 'Paramètres',
          onPress: () => {
            // Ouvrir les paramètres de l'app
            if (Platform.OS === 'ios') {
              Location.requestForegroundPermissionsAsync();
            } else {
              Location.requestForegroundPermissionsAsync();
            }
          },
        },
      ]
    );
  }
}

export const locationService = LocationService.getInstance();
