{"buildFiles": ["C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\4212f3t5\\prefab\\armeabi-v7a\\prefab\\lib\\arm-linux-androideabi\\cmake\\fbjni\\fbjniConfig.cmake", "C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\4212f3t5\\prefab\\armeabi-v7a\\prefab\\lib\\arm-linux-androideabi\\cmake\\fbjni\\fbjniConfigVersion.cmake", "C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\4212f3t5\\prefab\\armeabi-v7a\\prefab\\lib\\arm-linux-androideabi\\cmake\\hermes-engine\\hermes-engineConfig.cmake", "C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\4212f3t5\\prefab\\armeabi-v7a\\prefab\\lib\\arm-linux-androideabi\\cmake\\hermes-engine\\hermes-engineConfigVersion.cmake", "C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\4212f3t5\\prefab\\armeabi-v7a\\prefab\\lib\\arm-linux-androideabi\\cmake\\ReactAndroid\\ReactAndroidConfig.cmake", "C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\4212f3t5\\prefab\\armeabi-v7a\\prefab\\lib\\arm-linux-androideabi\\cmake\\ReactAndroid\\ReactAndroidConfigVersion.cmake", "C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\android\\CMakeLists.txt", "C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\reanimated\\CMakeLists.txt", "C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\android\\src\\main\\cpp\\worklets\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\4212f3t5\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\4212f3t5\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"reanimated::@89a6a9b85fb42923616c": {"toolchain": "toolchain", "abi": "armeabi-v7a", "artifactName": "reanimated", "output": "C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\android\\build\\intermediates\\cxx\\Debug\\4212f3t5\\obj\\armeabi-v7a\\libreanimated.so", "runtimeFiles": ["C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\android\\build\\intermediates\\cxx\\Debug\\4212f3t5\\obj\\armeabi-v7a\\libworklets.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4921e399dc760d526b20c10474ed13ea\\transformed\\react-android-0.79.2-debug\\prefab\\modules\\reactnative\\libs\\android.armeabi-v7a\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4921e399dc760d526b20c10474ed13ea\\transformed\\react-android-0.79.2-debug\\prefab\\modules\\jsi\\libs\\android.armeabi-v7a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8050d15875717ad3c035882deb89d68f\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.armeabi-v7a\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4921e399dc760d526b20c10474ed13ea\\transformed\\react-android-0.79.2-debug\\prefab\\modules\\reactnative\\libs\\android.armeabi-v7a\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\19b228c99a9f96c9dea6bcb5eea8dce6\\transformed\\hermes-android-0.79.2-debug\\prefab\\modules\\libhermes\\libs\\android.armeabi-v7a\\libhermes.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4921e399dc760d526b20c10474ed13ea\\transformed\\react-android-0.79.2-debug\\prefab\\modules\\hermestooling\\libs\\android.armeabi-v7a\\libhermestooling.so"]}, "worklets::@a0394df2d94e5212d8bd": {"toolchain": "toolchain", "abi": "armeabi-v7a", "artifactName": "worklets", "output": "C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\android\\build\\intermediates\\cxx\\Debug\\4212f3t5\\obj\\armeabi-v7a\\libworklets.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4921e399dc760d526b20c10474ed13ea\\transformed\\react-android-0.79.2-debug\\prefab\\modules\\jsi\\libs\\android.armeabi-v7a\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8050d15875717ad3c035882deb89d68f\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.armeabi-v7a\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4921e399dc760d526b20c10474ed13ea\\transformed\\react-android-0.79.2-debug\\prefab\\modules\\reactnative\\libs\\android.armeabi-v7a\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\19b228c99a9f96c9dea6bcb5eea8dce6\\transformed\\hermes-android-0.79.2-debug\\prefab\\modules\\libhermes\\libs\\android.armeabi-v7a\\libhermes.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4921e399dc760d526b20c10474ed13ea\\transformed\\react-android-0.79.2-debug\\prefab\\modules\\hermestooling\\libs\\android.armeabi-v7a\\libhermestooling.so"]}}, "toolchains": {"toolchain": {"cCompilerExecutable": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": ["cpp"]}