package expo.modules.updates.db.entity

import android.net.Uri
import androidx.room.*
import expo.modules.updates.db.enums.HashType
import org.json.JSONObject
import java.util.*

/**
 * Data class that represents a (potential) row in the `assets` table in SQLite. The table schema is
 * autogenerated from this file.
 *
 * The `id` field here only has meaning within an individual SQLite instance, as a foreign key;
 * unlike [UpdateEntity.id], it does *not* uniquely identify an asset across multiple installations
 * of an app. [AssetEntity.hash] should be used in most places where a unique identifier is needed.
 */
@Entity(tableName = "assets", indices = [Index(value = ["key"], unique = true)])
class AssetEntity(@field:ColumnInfo(name = "key") var key: String?, var type: String?) {
  @PrimaryKey(autoGenerate = true) // 0 is treated as unset while inserting the entity into the db
  var id: Long = 0

  var url: Uri? = null

  var headers: JSONObject? = null

  @ColumnInfo(name = "extra_request_headers")
  var extraRequestHeaders: JSONObject? = null

  var metadata: JSONObject? = null

  @ColumnInfo(name = "download_time")
  var downloadTime: Date? = null

  @ColumnInfo(name = "relative_path")
  var relativePath: String? = null

  /**
   * Hex-encoded SHA-256
   */
  var hash: ByteArray? = null

  @ColumnInfo(name = "hash_type")
  var hashType = HashType.SHA256

  /**
   * Base64URL-encoded SHA-256
   */
  @ColumnInfo(name = "expected_hash")
  var expectedHash: String? = null

  @ColumnInfo(name = "marked_for_deletion")
  var markedForDeletion = false

  @Ignore
  var isLaunchAsset = false

  @Ignore
  var embeddedAssetFilename: String? = null

  @Ignore
  var resourcesFilename: String? = null

  @Ignore
  var resourcesFolder: String? = null

  @Ignore
  var scale: Float? = null

  @Ignore
  var scales: Array<Float>? = null

  internal fun getFileExtension(): String {
    val type = this.type ?: return ""
    return if (type.startsWith(".")) {
      type
    } else {
      ".$type"
    }
  }
}
