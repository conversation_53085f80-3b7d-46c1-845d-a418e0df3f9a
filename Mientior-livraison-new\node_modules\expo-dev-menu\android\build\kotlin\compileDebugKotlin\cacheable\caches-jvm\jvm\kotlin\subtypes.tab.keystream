 com.facebook.react.ReactActivity/expo.interfaces.devmenu.DevMenuManagerInterface0com.facebook.react.bridge.LifecycleEventListener2com.facebook.react.defaults.DefaultReactNativeHost1com.facebook.react.devsupport.DevMenuSettingsBase=com.facebook.react.modules.debug.interfaces.DeveloperSettingsBandroid.content.SharedPreferences.OnSharedPreferenceChangeListener0expo.interfaces.devmenu.DevMenuDelegateInterface3expo.interfaces.devmenu.DevMenuPreferencesInterface$expo.modules.core.interfaces.Packagecom.facebook.react.ReactPackageandroid.widget.FrameLayout"kotlin.properties.ReadOnlyProperty$android.hardware.SensorEventListener!android.view.View.OnTouchListener"<EMAIL>"expo.modules.kotlin.records.Record,com.facebook.react.views.view.ReactViewGroup/android.view.ViewTreeObserver.OnPreDrawListener                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   