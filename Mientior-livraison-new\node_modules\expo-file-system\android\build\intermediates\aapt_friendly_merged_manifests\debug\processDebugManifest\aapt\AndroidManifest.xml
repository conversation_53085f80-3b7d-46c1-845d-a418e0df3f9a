<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="expo.modules.filesystem" >

    <uses-sdk android:minSdkVersion="24" />

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />

    <queries>

        <!-- Query open documents -->
        <intent>
            <action android:name="android.intent.action.OPEN_DOCUMENT_TREE" />
        </intent>
    </queries>

    <application>
        <provider
            android:name="expo.modules.filesystem.FileSystemFileProvider"
            android:authorities="dollar_openBracket_applicationId_closeBracket.FileSystemFileProvider"
            android:exported="false"
            android:grantUriPermissions="true"
            tools:replace="android:authorities" >
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_system_provider_paths" />
        </provider>
    </application>

</manifest>