import React, { useCallback, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  StatusBar,
  SafeAreaView,
  RefreshControl,
  Animated,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { useHomeData } from '../../hooks/useHomeDataQuery';
import { Restaurant } from '../../services/restaurantService';
import { Order } from '../../services/orderService';
import { useServiceTypesQuery } from '../../hooks/useServiceTypesQuery';
import { usePromotionsQuery } from '../../hooks/usePromotionsQuery';
import { useCategoriesQuery } from '../../hooks/useCategoriesQuery';
import { useCategoryStore } from '../../store/categoryStore';
import { useRestaurantFiltering } from '../../hooks/useRestaurantFiltering';
import { Promotion } from '../../types/promotion';
import { Category } from '../../types/category';
import { HapticFeedback } from '../../utils/hapticFeedback';
import { accessibilityColors } from '../../constants/accessibilityTheme';

const HomeScreen: React.FC = () => {
  const navigation = useNavigation();

  // Animations
  const fadeAnim = new Animated.Value(0);
  const slideAnim = new Animated.Value(30);

  // Hooks pour les données de l'écran d'accueil
  const {
    locationDisplayName,
    unreadCount,
    hasNotifications,
    recentOrders,
    activeOrders,
    hasActiveOrders,
    hasRecentOrders,
    loading: homeLoading,
    refreshing,
    error: homeError,
    refresh,
  } = useHomeData();

  // Hook pour les types de services depuis Supabase
  const {
    serviceTypes,
    loading: serviceTypesLoading,
    error: serviceTypesError,
    refresh: refreshServiceTypes
  } = useServiceTypesQuery();

  // Hook pour les promotions depuis Supabase
  const {
    promotions,
    loading: promotionsLoading,
    error: promotionsError,
    refresh: refreshPromotions
  } = usePromotionsQuery({
    limit: 5,
    enabled: true,
    auto_refresh: false
  });

  // Hook pour les catégories depuis Supabase
  const {
    categories,
    loading: categoriesLoading,
    error: categoriesError,
    refresh: refreshCategories,
    selectCategory
  } = useCategoriesQuery({
    limit: 8,
    enabled: true,
    include_all_category: true
  });

  // Get category selection state from store
  const { selectedCategory } = useCategoryStore();

  // Hook pour le filtrage des restaurants basé sur la catégorie sélectionnée
  const {
    filteredRestaurants,
    loading: restaurantsLoading,
    error: restaurantsError,
    refetch: refetchRestaurants,
    totalCount,
    filteredCount
  } = useRestaurantFiltering({
    enabled: true,
    includeAll: true,
  });



  // Log de débogage pour les promotions
  React.useEffect(() => {
    console.log('🏠 HomeScreen: Promotions state changed', {
      loading: promotionsLoading,
      error: promotionsError,
      count: promotions.length,
      promotions: promotions.map(p => ({ id: p.id, title: p.title }))
    });
  }, [promotions, promotionsLoading, promotionsError]);

  // Log de débogage pour les catégories
  React.useEffect(() => {
    console.log('🏠 HomeScreen: Categories state changed', {
      loading: categoriesLoading,
      error: categoriesError,
      count: categories.length,
      categories: categories.map(c => ({ id: c.id, name: c.name }))
    });
  }, [categories, categoriesLoading, categoriesError]);

  // Fonction pour générer la couleur de fond basée sur la couleur principale
  const getBackgroundColor = useCallback((color: string) => {
    // Mapping des couleurs principales vers leurs versions pâles
    const colorMap: { [key: string]: string } = {
      '#FF6B35': '#FFE8E8',  // Orange -> Orange pâle
      '#4ECDC4': '#E8F8FF',  // Turquoise -> Turquoise pâle
      '#0DCAA8': '#E8FFE8',  // Vert -> Vert pâle
      '#FF6B6B': '#FFE8E8',  // Rouge médical -> Rouge pâle
      '#D4A574': '#F5F0E8',  // Beige doré -> Beige très pâle
      '#9C88FF': '#F0E8FF',  // Violet -> Violet pâle
      '#FFB347': '#FFF8E8',  // Orange doux -> Orange très pâle
      '#32CD32': '#E8F8E8',  // Vert naturel -> Vert naturel pâle
      '#8B4513': '#F0E8E0',  // Marron -> Marron pâle
    };

    return colorMap[color] || '#F0F0F0'; // Couleur par défaut si non trouvée
  }, []);







  // Animation d'entrée
  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);





  // Navigation functions
  const navigateToRestaurant = useCallback((restaurant: Restaurant) => {
    console.log('🏪 Navigating to restaurant:', restaurant.business_name);
    HapticFeedback.light(); // Feedback tactile pour navigation
    (navigation as any).navigate('Establishment', { restaurantId: restaurant.id });
  }, [navigation]);

  const navigateToOrderTracking = useCallback((order: Order) => {
    console.log('📦 Navigating to order tracking:', order.order_number);
    (navigation as any).navigate('OrderTracking', { orderId: order.id });
  }, [navigation]);

  const navigateToNotifications = useCallback(() => {
    (navigation as any).navigate('Notifications');
  }, [navigation]);

  const navigateToServiceTypes = useCallback(() => {
    console.log('🏷️ Navigating to service types');
    HapticFeedback.light();
    (navigation as any).navigate('ServiceTypes');
  }, [navigation]);

  const navigateToAddresses = useCallback(() => {
    (navigation as any).navigate('Addresses');
  }, [navigation]);

  const navigateToSearch = useCallback(() => {
    HapticFeedback.light();
    (navigation as any).navigate('Search', { query: '' });
  }, [navigation]);

  const navigateToMap = useCallback(() => {
    HapticFeedback.light();
    (navigation as any).navigate('Map');
  }, [navigation]);

  // Gestion de la sélection de catégorie avec filtrage local
  const handleCategorySelect = useCallback((category: Category) => {
    console.log('📂 Category selected:', category.name);
    HapticFeedback.light();
    selectCategory(category);

    // Le filtrage se fait automatiquement via useRestaurantFiltering
    // Pas besoin de navigation, les restaurants sont filtrés en temps réel
  }, [selectCategory]);

  // Navigation vers la page complète des restaurants de la catégorie
  const handleViewAllCategory = useCallback(() => {
    if (selectedCategory) {
      console.log('📂 Navigating to full category view:', selectedCategory.name);
      HapticFeedback.light();
      (navigation as any).navigate('CategoryRestaurants', {
        categoryId: selectedCategory.id,
        categoryName: selectedCategory.name
      });
    } else {
      // Navigation vers la liste générale des restaurants
      (navigation as any).navigate('RestaurantList', {});
    }
  }, [navigation, selectedCategory]);

  // Navigation vers les types de services
  const handleServiceTypePress = useCallback((serviceType: any) => {
    console.log('🏷️ Service type pressed:', serviceType.title || serviceType.name);
    HapticFeedback.medium();
    navigateToServiceTypes();
  }, [navigateToServiceTypes]);

  // Gestion des clics sur les promotions
  const handlePromotionPress = useCallback((promotion: Promotion) => {
    console.log('🎁 Promotion pressed:', promotion.title);
    HapticFeedback.medium();

    // Navigation vers la page appropriée selon le type de service
    if (promotion.service_type_id) {
      // Navigation vers les restaurants du type de service spécifique
      (navigation as any).navigate('CategoryRestaurants', {
        serviceTypeId: promotion.service_type_id,
        promotionId: promotion.id
      });
    } else {
      // Navigation vers la liste générale des restaurants avec la promotion
      (navigation as any).navigate('RestaurantList', {
        promotionId: promotion.id
      });
    }
  }, [navigation]);

  // Fonction pour rendre un type de service
  const renderServiceType = useCallback(({ item }: { item: any }) => (
    <TouchableOpacity
      style={styles.serviceTypeItem}
      onPress={() => handleServiceTypePress(item)}
      activeOpacity={0.8}
      accessible={true}
      accessibilityRole="button"
      accessibilityLabel={`Accéder aux services ${item.title || item.name}`}
      accessibilityHint={item.description}
    >
      <View style={[
        styles.serviceTypeIcon,
        { backgroundColor: getBackgroundColor(item.color) }
      ]}>
        <Ionicons
          name={item.icon as any}
          size={28}
          color={item.color}
        />
      </View>
      <Text style={styles.serviceTypeLabel}>{item.title || item.name}</Text>
    </TouchableOpacity>
  ), [handleServiceTypePress, getBackgroundColor]);

  // Fonction pour rendre une promotion
  const renderPromotion = useCallback(({ item }: { item: Promotion }) => (
    <TouchableOpacity
      style={styles.promotionCardNew}
      onPress={() => handlePromotionPress(item)}
      activeOpacity={0.8}
      accessible={true}
      accessibilityRole="button"
      accessibilityLabel={`Promotion: ${item.title}`}
      accessibilityHint={item.description || 'Appuyez pour voir les détails'}
    >
      <Image
        source={{ uri: item.image_url || 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400&h=240&fit=crop' }}
        style={styles.promotionImage}
        resizeMode="cover"
      />
      <View style={styles.promotionOverlay}>
        <Text style={styles.promotionTitle}>{item.title}</Text>
        {item.description && (
          <Text style={styles.promotionSubtitle}>{item.description}</Text>
        )}
      </View>
    </TouchableOpacity>
  ), [handlePromotionPress]);

  // Fonction pour obtenir la couleur de fond d'une catégorie
  const getCategoryBackgroundColor = useCallback((color: string): string => {
    const colorMap: Record<string, string> = {
      '#0DCAA8': '#E8F8F5',
      '#FF6B35': '#FFF2ED',
      '#4ECDC4': '#E8F8F7',
      '#9C88FF': '#E8F5FF',
      '#FFB347': '#FFF8E8',
      '#4CAF50': '#E8F5E8',
      '#FF5722': '#FFE8E8',
      '#FFC107': '#FFF8E1',
    };
    return colorMap[color] || '#F0F0F0';
  }, []);

  // Fonction pour rendre une catégorie
  const renderCategory = useCallback(({ item }: { item: Category }) => (
    <TouchableOpacity
      style={styles.categoryFlatListItem}
      onPress={() => handleCategorySelect(item)}
      activeOpacity={0.8}
      accessible={true}
      accessibilityRole="button"
      accessibilityLabel={`Catégorie: ${item.name}`}
      accessibilityHint={item.description || 'Appuyez pour explorer cette catégorie'}
    >
      <View style={[styles.categoryGridIcon, { backgroundColor: getCategoryBackgroundColor(item.color) }]}>
        <Ionicons name={item.icon_name as any} size={20} color={item.color} />
      </View>
      <Text style={styles.categoryGridLabel}>{item.name}</Text>
    </TouchableOpacity>
  ), [handleCategorySelect, getCategoryBackgroundColor]);



  const renderRestaurantCard = useCallback(({ item }: { item: Restaurant }) => (
    <TouchableOpacity
      style={styles.restaurantCard}
      onPress={() => navigateToRestaurant(item)}
      activeOpacity={0.9}
    >
      <View style={styles.restaurantImageContainer}>
        <Image
          source={{
            uri: item.cover_image_url || item.logo_url || 'https://images.unsplash.com/photo-1555939594-58d7cb561ad1?w=400&h=300&fit=crop'
          }}
          style={styles.restaurantImage}
        />
        {!item.is_available && (
          <View style={styles.unavailableOverlay}>
            <Text style={styles.unavailableText}>Fermé</Text>
          </View>
        )}
        <View style={styles.ratingBadge}>
          <Ionicons name="star" size={12} color="#FFD700" />
          <Text style={styles.ratingText}>{item.rating?.toFixed(1) || '4.0'}</Text>
        </View>
      </View>
      <View style={styles.restaurantInfo}>
        <Text style={styles.restaurantName} numberOfLines={1}>{item.business_name}</Text>
        <Text style={styles.restaurantDescription} numberOfLines={2}>
          {item.description || 'Restaurant africain authentique'}
        </Text>
        <View style={styles.restaurantMeta}>
          <View style={styles.metaItem}>
            <Ionicons name="time-outline" size={14} color="#666" />
            <Text style={styles.metaText}>
              {item.estimated_delivery_time ? `${item.estimated_delivery_time} min` : '25-40 min'}
            </Text>
          </View>
          <View style={styles.metaItem}>
            <Ionicons name="bicycle-outline" size={14} color="#0DCAA8" />
            <Text style={styles.deliveryFeeText}>{item.delivery_fee?.toLocaleString() || '500'} FCFA</Text>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  ), [navigateToRestaurant]);

  const renderRecentOrder = ({ item }: { item: Order }) => (
    <TouchableOpacity
      style={styles.orderCard}
      onPress={() => navigateToOrderTracking(item)}
    >
      <View style={styles.orderHeader}>
        <Text style={styles.orderNumber}>{item.order_number}</Text>
        <View style={[styles.statusBadge, { backgroundColor: item.status_color }]}>
          <Text style={styles.statusText}>{item.status}</Text>
        </View>
      </View>
      <Text style={styles.orderRestaurant}>{item.merchant_name}</Text>
      <Text style={styles.orderAmount}>{item.total_amount.toLocaleString()} FCFA</Text>
    </TouchableOpacity>
  );







  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor="#fff" />
      
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.locationContainer}>
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <Ionicons name="location-outline" size={20} color="#0DCAA8" />
            <View style={{ marginLeft: 8 }}>
              <Text style={styles.deliveryText}>Livrer à</Text>
              <TouchableOpacity onPress={navigateToAddresses} style={styles.addressRow}>
                <Text style={styles.locationText}>{locationDisplayName}</Text>
                <Ionicons name="chevron-down" size={16} color="#0DCAA8" />
              </TouchableOpacity>
            </View>
          </View>
        </View>
        <TouchableOpacity
          style={styles.notificationButton}
          onPress={navigateToNotifications}
        >
          <Image
            source={{
              uri: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face'
            }}
            style={{
              width: 40,
              height: 40,
              borderRadius: 20,
              backgroundColor: '#f0f0f0'
            }}
          />
          {hasNotifications && (
            <View style={styles.notificationBadge}>
              <Text style={styles.notificationCount}>
                {unreadCount > 99 ? '99+' : unreadCount.toString()}
              </Text>
            </View>
          )}
        </TouchableOpacity>
      </View>

      <ScrollView
        style={styles.content}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={refresh}
            colors={['#0DCAA8']}
            tintColor="#0DCAA8"
          />
        }
      >
        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <View style={styles.searchRow}>
            <TouchableOpacity
              style={styles.searchInputContainer}
              onPress={navigateToSearch}
              activeOpacity={0.8}
            >
              <Ionicons name="search-outline" size={20} color="#999" style={styles.searchIcon} />
              <Text style={styles.searchPlaceholder}>
                Rechercher un plat, un restaurant...
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.mapButton}
              onPress={navigateToMap}
              activeOpacity={0.8}
            >
              <Ionicons name="map-outline" size={20} color="#0DCAA8" />
            </TouchableOpacity>
          </View>
        </View>



        {/* Service Types Section - Dynamic from Supabase */}
        {serviceTypesLoading ? (
          <View style={styles.loadingContainer}>
            <Text style={styles.loadingText}>Chargement des services...</Text>
          </View>
        ) : serviceTypesError ? (
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>Erreur lors du chargement des services</Text>
            <TouchableOpacity onPress={refreshServiceTypes} style={styles.retryButton}>
              <Text style={styles.retryButtonText}>Réessayer</Text>
            </TouchableOpacity>
          </View>
        ) : serviceTypes.length > 0 ? (
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.servicesList}
            contentContainerStyle={{ paddingHorizontal: 16 }}
          >
            {serviceTypes.slice(0, 6).map((serviceType, index) => (
              <View key={serviceType.id} style={{ marginRight: index < serviceTypes.slice(0, 6).length - 1 ? 12 : 0 }}>
                {renderServiceType({ item: serviceType })}
              </View>
            ))}
          </ScrollView>
        ) : (
          // Fallback vers les services statiques si aucun service Supabase
          <View style={styles.servicesRow}>
            <TouchableOpacity
              style={styles.serviceTypeItem}
              onPress={() => handleServiceTypePress({ title: 'Repas', business_type: 'restaurant' })}
              activeOpacity={0.8}
            >
              <View style={[styles.serviceTypeIcon, { backgroundColor: '#FFE8E8' }]}>
                <Ionicons name="restaurant" size={28} color="#FF6B35" />
              </View>
              <Text style={styles.serviceTypeLabel}>Repas</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.serviceTypeItem}
              onPress={() => handleServiceTypePress({ title: 'Colis', business_type: 'delivery' })}
              activeOpacity={0.8}
            >
              <View style={[styles.serviceTypeIcon, { backgroundColor: '#E8F8FF' }]}>
                <Ionicons name="car" size={28} color="#4ECDC4" />
              </View>
              <Text style={styles.serviceTypeLabel}>Colis</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.serviceTypeItem}
              onPress={() => handleServiceTypePress({ title: 'Marchandises', business_type: 'grocery' })}
              activeOpacity={0.8}
            >
              <View style={[styles.serviceTypeIcon, { backgroundColor: '#E8FFE8' }]}>
                <Ionicons name="storefront" size={28} color="#0DCAA8" />
              </View>
              <Text style={styles.serviceTypeLabel}>Marchandises</Text>
            </TouchableOpacity>
          </View>
        )}

        {/* Promotions du jour - Dynamic from Supabase */}
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Promotions du jour</Text>
        </View>

        {promotionsLoading ? (
          <View style={styles.loadingContainer}>
            <Text style={styles.loadingText}>Chargement des promotions...</Text>
          </View>
        ) : promotionsError ? (
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>Erreur lors du chargement des promotions</Text>
            <TouchableOpacity onPress={refreshPromotions} style={styles.retryButton}>
              <Text style={styles.retryButtonText}>Réessayer</Text>
            </TouchableOpacity>
          </View>
        ) : promotions.length > 0 ? (
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.promotionsSection}
            contentContainerStyle={{ paddingHorizontal: 4 }}
          >
            {promotions.map((promotion, index) => (
              <View key={promotion.id} style={{ marginRight: index < promotions.length - 1 ? 8 : 0 }}>
                {renderPromotion({ item: promotion })}
              </View>
            ))}
          </ScrollView>
        ) : (
          // Fallback vers promotions statiques si aucune promotion Supabase
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.promotionsSection}
            contentContainerStyle={{ paddingHorizontal: 4 }}
          >
            <View style={styles.promotionCardNew}>
              <Image
                source={{
                  uri: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400&h=240&fit=crop'
                }}
                style={styles.promotionImage}
              />
              <View style={styles.promotionOverlay}>
                <Text style={styles.promotionTitle}>50% sur votre première</Text>
                <Text style={styles.promotionTitle}>commande</Text>
              </View>
            </View>

            <View style={styles.promotionCardNew}>
              <Image
                source={{
                  uri: 'https://images.unsplash.com/photo-1571091718767-18b5b1457add?w=400&h=240&fit=crop'
                }}
                style={styles.promotionImage}
              />
              <View style={styles.promotionOverlay}>
                <Text style={styles.promotionTitle}>Livraison</Text>
                <Text style={styles.promotionTitle}>gratuite</Text>
              </View>
            </View>
          </ScrollView>
        )}

        {/* Explorer par catégorie - Dynamic from Supabase */}
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Explorer par catégorie</Text>
        </View>

        {categoriesLoading ? (
          <View style={styles.loadingContainer}>
            <Text style={styles.loadingText}>Chargement des catégories...</Text>
          </View>
        ) : categoriesError ? (
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>Erreur lors du chargement des catégories</Text>
            <TouchableOpacity onPress={refreshCategories} style={styles.retryButton}>
              <Text style={styles.retryButtonText}>Réessayer</Text>
            </TouchableOpacity>
          </View>
        ) : categories.length > 0 ? (
          <View style={[styles.categoriesSection, { paddingHorizontal: 10 }]}>
            <View style={{ flexDirection: 'row', flexWrap: 'wrap', justifyContent: 'space-between' }}>
              {categories.map((category) => (
                <View key={category.id} style={{ width: '50%', marginBottom: 8 }}>
                  {renderCategory({ item: category })}
                </View>
              ))}
            </View>
          </View>
        ) : (
          // Fallback vers catégories statiques si aucune catégorie Supabase
          <View style={styles.categoryGrid}>
            <TouchableOpacity
              style={styles.categoryGridItem}
              onPress={() => handleCategorySelect({
                id: 'african',
                name: 'Africaine traditionnelle',
                icon_name: 'restaurant-outline',
                color: '#0DCAA8',
                is_active: true,
                sort_order: 1,
                created_at: '',
                updated_at: ''
              } as Category)}
              activeOpacity={0.8}
            >
              <View style={[styles.categoryGridIcon, { backgroundColor: '#E8F8F5' }]}>
                <Ionicons name="restaurant-outline" size={20} color="#0DCAA8" />
              </View>
              <Text style={styles.categoryGridLabel}>Africaine traditionnelle</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.categoryGridItem}
              onPress={() => handleCategorySelect({
                id: 'fast-food',
                name: 'Fast Food',
                icon_name: 'fast-food-outline',
                color: '#FF6B35',
                is_active: true,
                sort_order: 2,
                created_at: '',
                updated_at: ''
              } as Category)}
              activeOpacity={0.8}
            >
              <View style={[styles.categoryGridIcon, { backgroundColor: '#FFF2ED' }]}>
                <Ionicons name="fast-food-outline" size={20} color="#FF6B35" />
              </View>
              <Text style={styles.categoryGridLabel}>Fast Food</Text>
            </TouchableOpacity>
          </View>
        )}

        {/* Restaurants Section - Filtered by Category */}
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>
            {selectedCategory && selectedCategory.name !== 'Tous'
              ? `Restaurants - ${selectedCategory.name}`
              : 'Restaurants Populaires'
            }
          </Text>
          <TouchableOpacity
            onPress={handleViewAllCategory}
          >
            <Text style={styles.seeAllText}>Voir tout</Text>
          </TouchableOpacity>
        </View>

        {/* Category Selection Info */}
        {selectedCategory && selectedCategory.name !== 'Tous' && (
          <View style={styles.categoryInfoContainer}>
            <Text style={styles.categoryInfoText}>
              {filteredCount} restaurant{filteredCount > 1 ? 's' : ''} trouvé{filteredCount > 1 ? 's' : ''} pour "{selectedCategory.name}"
            </Text>
            <TouchableOpacity
              onPress={() => selectCategory({ id: 'all', name: 'Tous' } as Category)}
              style={styles.clearFilterButton}
            >
              <Text style={styles.clearFilterText}>Voir tous</Text>
            </TouchableOpacity>
          </View>
        )}

        {restaurantsLoading ? (
          <View style={styles.loadingContainer}>
            <Text style={styles.loadingText}>Chargement des restaurants...</Text>
          </View>
        ) : restaurantsError ? (
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>{restaurantsError}</Text>
            <TouchableOpacity style={styles.retryButton} onPress={refetchRestaurants}>
              <Text style={styles.retryButtonText}>Réessayer</Text>
            </TouchableOpacity>
          </View>
        ) : (
          <View>
            {filteredRestaurants.length > 0 ? (
              filteredRestaurants.slice(0, 6).map((restaurant) => (
                <View key={restaurant.id}>
                  {renderRestaurantCard({ item: restaurant })}
                </View>
              ))
            ) : (
              <View style={styles.emptyContainer}>
                <Text style={styles.emptyText}>
                  {selectedCategory && selectedCategory.name !== 'Tous'
                    ? `Aucun restaurant trouvé pour "${selectedCategory.name}"`
                    : 'Aucun restaurant disponible'
                  }
                </Text>
                <TouchableOpacity style={styles.retryButton} onPress={refetchRestaurants}>
                  <Text style={styles.retryButtonText}>Actualiser</Text>
                </TouchableOpacity>
              </View>
            )}
          </View>
        )}



        {/* Recent Orders Section */}
        {hasActiveOrders && (
          <>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>Commandes en cours</Text>
              <TouchableOpacity onPress={() => (navigation as any).navigate('Orders')}>
                <Text style={styles.seeAllText}>Voir tout</Text>
              </TouchableOpacity>
            </View>
            <View>
              {activeOrders.slice(0, 2).map((order) => (
                <View key={order.id}>
                  {renderRecentOrder({ item: order })}
                </View>
              ))}
            </View>
          </>
        )}

        {hasRecentOrders && !hasActiveOrders && (
          <>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>Commandes récentes</Text>
              <TouchableOpacity onPress={() => (navigation as any).navigate('Orders')}>
                <Text style={styles.seeAllText}>Voir tout</Text>
              </TouchableOpacity>
            </View>
            <View>
              {recentOrders.slice(0, 2).map((order) => (
                <View key={order.id}>
                  {renderRecentOrder({ item: order })}
                </View>
              ))}
            </View>
          </>
        )}


      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FAFAFA',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    marginTop: 50, // FIX: Changed from paddingTop to marginTop for consistency
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
  },
  locationContainer: {
    flex: 1,
  },
  deliveryText: {
    fontSize: 14,
    color: accessibilityColors.text.secondary,
    marginBottom: 2,
    fontWeight: '500',
    lineHeight: 20,
  },
  userNameText: {
    fontSize: 20,
    fontWeight: '700',
    color: accessibilityColors.text.primary,
    marginBottom: 4,
    lineHeight: 24,
  },
  addressRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  locationText: {
    fontSize: 16,
    fontWeight: '600',
    color: accessibilityColors.primary.dark,
    marginRight: 4,
    lineHeight: 22,
  },
  notificationButton: {
    position: 'relative',
    padding: 10,
    borderRadius: 12,
    backgroundColor: '#F8F9FA',
  },
  notificationBadge: {
    position: 'absolute',
    top: 6,
    right: 6,
    backgroundColor: '#FF4757',
    borderRadius: 10,
    minWidth: 18,
    height: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  notificationCount: {
    color: '#fff',
    fontSize: 11,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 12,
  },
  searchContainer: {
    marginBottom: 16,
  },
  searchRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  searchInputContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F8F9FA',
    borderRadius: 12,
    paddingHorizontal: 14,
    paddingVertical: 10,
    borderWidth: 1,
    borderColor: '#E9ECEF',
  },
  mapButton: {
    width: 48,
    height: 48,
    borderRadius: 12,
    backgroundColor: '#F8F9FA',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E9ECEF',
  },
  searchIcon: {
    marginRight: 12,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#1A1A1A',
    fontWeight: '500',
  },
  searchPlaceholder: {
    flex: 1,
    fontSize: 16,
    color: accessibilityColors.interactive.placeholder,
    fontWeight: '500',
    lineHeight: 22,
  },
  servicesList: {
    marginBottom: 18,
    paddingVertical: 8,
  },
  serviceCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 14,
    borderRadius: 12,
    marginVertical: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 6,
    elevation: 2,
  },
  serviceIconContainer: {
    width: 44,
    height: 44,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 14,
  },
  serviceIcon: {
    fontSize: 24,
  },
  serviceTextContainer: {
    flex: 1,
  },
  serviceTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: accessibilityColors.text.primary,
    marginBottom: 2,
    lineHeight: 24,
  },
  serviceSubtitle: {
    fontSize: 15,
    color: accessibilityColors.text.secondary,
    fontWeight: '500',
    lineHeight: 20,
  },
  promotionsContainer: {
    flexDirection: 'row',
    marginVertical: 10,
    gap: 6,
  },
  promotionCard: {
    flex: 1,
    backgroundColor: '#0DCAA8',
    borderRadius: 12,
    padding: 12,
    height: 70,
    justifyContent: 'center',
  },
  promotionCard2: {
    flex: 1,
    backgroundColor: '#FF6B35',
    borderRadius: 12,
    padding: 12,
    height: 70,
    justifyContent: 'center',
  },
  promotionText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
    textAlign: 'center',
  },
  promotionText2: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
    textAlign: 'center',
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 20,
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 22,
    fontWeight: '700',
    color: accessibilityColors.text.primary,
    lineHeight: 28,
  },
  seeAllText: {
    fontSize: 14,
    color: '#0DCAA8',
    fontWeight: '600',
  },
  restaurantCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    marginBottom: 12,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 3,
  },
  restaurantImageContainer: {
    position: 'relative',
  },
  restaurantImage: {
    width: '100%',
    height: 140,
  },
  unavailableOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.6)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  ratingBadge: {
    position: 'absolute',
    top: 12,
    right: 12,
    backgroundColor: 'rgba(255,255,255,0.95)',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#1A1A1A',
    marginLeft: 4,
  },
  restaurantInfo: {
    padding: 12,
  },
  restaurantName: {
    fontSize: 18,
    fontWeight: '700',
    color: accessibilityColors.text.primary,
    marginBottom: 4,
    lineHeight: 24,
  },
  restaurantDescription: {
    fontSize: 14,
    color: accessibilityColors.text.secondary,
    lineHeight: 20,
    marginBottom: 8,
    fontWeight: '400',
  },
  restaurantMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  metaItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  metaText: {
    fontSize: 14,
    color: accessibilityColors.text.secondary,
    marginLeft: 4,
    fontWeight: '500',
    lineHeight: 18,
  },
  deliveryFeeText: {
    fontSize: 14,
    color: accessibilityColors.primary.dark,
    marginLeft: 4,
    fontWeight: '700',
    lineHeight: 18,
  },
  unavailableText: {
    fontSize: 14,
    color: '#fff',
    fontWeight: 'bold',
  },
  orderCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 12,
    marginBottom: 8,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  orderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  orderNumber: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#000',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  statusText: {
    fontSize: 12,
    color: '#fff',
    fontWeight: '600',
  },
  orderRestaurant: {
    fontSize: 14,
    color: '#666',
    marginBottom: 2,
  },
  orderAmount: {
    fontSize: 14,
    fontWeight: '600',
    color: '#0DCAA8',
  },
  categoriesGrid: {
    marginBottom: 20,
  },
  categoryItem: {
    flex: 1,
    aspectRatio: 1,
    margin: 4,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.06,
    shadowRadius: 4,
    elevation: 2,
  },
  categoryIconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: 'rgba(255,255,255,0.3)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 6,
  },
  categoryIcon: {
    fontSize: 20,
  },
  categoryTitle: {
    fontSize: 12,
    fontWeight: '700',
    color: accessibilityColors.text.primary,
    textAlign: 'center',
    lineHeight: 16,
  },
  loadingContainer: {
    padding: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: accessibilityColors.text.secondary,
    marginTop: 12,
    fontWeight: '500',
    lineHeight: 22,
  },
  errorContainer: {
    padding: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  errorText: {
    fontSize: 16,
    color: accessibilityColors.status.error,
    textAlign: 'center',
    marginTop: 12,
    marginBottom: 20,
    fontWeight: '600',
    lineHeight: 22,
  },
  retryButton: {
    backgroundColor: '#0DCAA8',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 16,
  },
  retryButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  emptyContainer: {
    padding: 20,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 18,
    color: accessibilityColors.text.primary,
    textAlign: 'center',
    marginBottom: 16,
    fontWeight: '700',
    lineHeight: 24,
  },
  categoriesEmpty: {
    alignItems: 'center',
    padding: 32,
    backgroundColor: '#F8F9FA',
    borderRadius: 16,
    marginBottom: 24,
  },
  realDataButton: {
    backgroundColor: '#4CAF50',
    padding: 12,
    borderRadius: 8,
    flex: 1,
    alignItems: 'center',
  },
  realDataButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
  rlsButton: {
    backgroundColor: '#9C27B0',
    padding: 12,
    borderRadius: 8,
    flex: 1,
    alignItems: 'center',
  },
  rlsButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
  viewsButton: {
    backgroundColor: '#607D8B',
    padding: 12,
    borderRadius: 8,
    flex: 1,
    alignItems: 'center',
  },
  viewsButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
  createViewsButton: {
    backgroundColor: '#795548',
    padding: 12,
    borderRadius: 8,
    flex: 1,
    alignItems: 'center',
  },
  createViewsButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
  devButtonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: 20,
    paddingVertical: 10,
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    marginHorizontal: 20,
    marginBottom: 20,
    gap: 8,
  },
  cleanDataButton: {
    backgroundColor: '#FF9800',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    flex: 1,
  },
  cleanDataButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
    textAlign: 'center',
  },
  clearDataButton: {
    backgroundColor: '#F44336',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    flex: 1,
  },
  clearDataButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
    textAlign: 'center',
  },
  // New styles for redesigned layout
  servicesRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: 20,
    paddingHorizontal: 16,
  },
  serviceTypeItem: {
    alignItems: 'center',
    flex: 1,
  },
  serviceTypeIcon: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },

  serviceTypeLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
    textAlign: 'center',
  },
  promotionsSection: {
    marginBottom: 24,
  },
  categoriesSection: {
    marginBottom: 24,
  },
  promotionCardNew: {
    borderRadius: 12,
    overflow: 'hidden',
    marginRight: 12,
  },
  promotionImage: {
    width: 200,
    height: 120,
    borderRadius: 12,
  },
  promotionOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(0,0,0,0.4)',
    padding: 12,
  },
  promotionTitle: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  promotionSubtitle: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '400',
    opacity: 0.9,
  },

  categoryGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    paddingHorizontal: 4,
  },
  categoryGridItem: {
    width: '48%',
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  categoryFlatListItem: {
    flex: 1,
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginHorizontal: 4,
    marginBottom: 8,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  categoryGridIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },

  categoryGridLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
    flex: 1,
  },
  categoryInfoContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#F0F8FF',
    paddingHorizontal: 16,
    paddingVertical: 12,
    marginBottom: 12,
    borderRadius: 12,
    borderLeftWidth: 4,
    borderLeftColor: '#0DCAA8',
  },
  categoryInfoText: {
    fontSize: 14,
    color: accessibilityColors.text.secondary,
    fontWeight: '500',
    flex: 1,
  },
  clearFilterButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    backgroundColor: '#0DCAA8',
    borderRadius: 8,
  },
  clearFilterText: {
    fontSize: 12,
    color: '#fff',
    fontWeight: '600',
  },

});

export default HomeScreen;
