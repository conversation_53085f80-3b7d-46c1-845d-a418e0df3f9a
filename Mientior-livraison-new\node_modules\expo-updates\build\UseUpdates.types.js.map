{"version": 3, "file": "UseUpdates.types.js", "sourceRoot": "", "sources": ["../src/UseUpdates.types.ts"], "names": [], "mappings": "AAkEA;;;;;GAKG;AACH,MAAM,CAAN,IAAY,cASX;AATD,WAAY,cAAc;IACxB;;OAEG;IACH,6BAAW,CAAA;IACX;;OAEG;IACH,uCAAqB,CAAA;AACvB,CAAC,EATW,cAAc,KAAd,cAAc,QASzB", "sourcesContent": ["import type { Manifest } from './Updates.types';\n\n/**\n * Structure encapsulating information on the currently running app\n * (either the embedded bundle or a downloaded update).\n */\nexport type CurrentlyRunningInfo = {\n  /**\n   * The UUID that uniquely identifies the currently running update if `expo-updates` is enabled. The\n   * UUID is represented in its canonical string form and will always use lowercase letters.\n   * In development mode, or any other environment in which `expo-updates` is disabled, this value is undefined.\n   * @example\n   * `\"xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx\"`\n   */\n  updateId?: string;\n  /**\n   * The channel name of the current build, if configured for use with EAS Update, `undefined` otherwise.\n   */\n  channel?: string;\n  /**\n   * If `expo-updates` is enabled, this is a `Date` object representing the creation time of the update\n   * that's currently running (whether it was embedded or downloaded at runtime).\n   *\n   * In development mode, or any other environment in which `expo-updates` is disabled, this value is\n   * undefined.\n   */\n  createdAt?: Date;\n  /**\n   * This will be true if the currently running update is the one embedded in the build,\n   * and not one downloaded from the updates server.\n   */\n  isEmbeddedLaunch: boolean;\n  /**\n   * `expo-updates` does its very best to always launch monotonically newer versions of your app so\n   * you don't need to worry about backwards compatibility when you put out an update. In very rare\n   * cases, it's possible that `expo-updates` may need to fall back to the update that's embedded in\n   * the app binary, even after newer updates have been downloaded and run (an \"emergency launch\").\n   * This boolean will be `true` if the app is launching under this fallback mechanism and `false`\n   * otherwise. If you are concerned about backwards compatibility of future updates to your app, you\n   * can use this constant to provide special behavior for this rare case.\n   */\n  isEmergencyLaunch: boolean;\n  /**\n   * If `isEmergencyLaunch` is set to true, this will contain a string error message describing\n   * what failed during initialization.\n   */\n  emergencyLaunchReason: string | null;\n  /**\n   * Number of milliseconds it took to launch.\n   */\n  launchDuration?: number;\n  /**\n   * If `expo-updates` is enabled, this is the\n   * [manifest](https://docs.expo.dev/versions/latest/sdk/updates/#updatesmanifest) object for the update that's currently\n   * running.\n   *\n   * In development mode, or any other environment in which `expo-updates` is disabled, this object is\n   * empty.\n   */\n  manifest?: Partial<Manifest>;\n  /**\n   * The runtime version of the current build.\n   */\n  runtimeVersion?: string;\n};\n\n/**\n * The different possible types of updates.\n * Currently, the only supported type is `UpdateInfoType.NEW`, indicating a new update that can be downloaded and launched\n * on the device.\n * In the future, other types of updates may be added to this list.\n */\nexport enum UpdateInfoType {\n  /**\n   * This is the type for new updates found on or downloaded from the update server, that are launchable on the device.\n   */\n  NEW = 'new',\n  /**\n   * This type is used when an update is a directive to roll back to the embedded bundle.\n   */\n  ROLLBACK = 'rollback',\n}\n\n/**\n * Structure representing a new update.\n */\nexport type UpdateInfoNew = {\n  /**\n   * The type of update.\n   */\n  type: UpdateInfoType.NEW;\n  /**\n   * For updates of type `UpdateInfoType.NEW`, this is\n   * a string that uniquely identifies the update. For the manifests used in the current Expo Updates protocol (including\n   * EAS Update), this represents the update's UUID in its canonical string form and will always use lowercase letters.\n   * @example\n   * `\"xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx\"`\n   */\n  updateId: string;\n  /**\n   * For all types of updates, this is\n   * a `Date` object representing the creation time or commit time of the update.\n   */\n  createdAt: Date;\n  /**\n   * For updates of type `UpdateInfoType.NEW`, this is\n   * the [manifest](https://docs.expo.dev/versions/latest/sdk/constants/#manifest) for the update.\n   */\n  manifest: Manifest;\n};\n\n/**\n * Structure representing a rollback directive.\n */\nexport type UpdateInfoRollback = {\n  /**\n   * The type of update.\n   */\n  type: UpdateInfoType.ROLLBACK;\n  /**\n   * For updates of type `UpdateInfoType.ROLLBACK`, this is always set to `undefined`.\n   */\n  updateId: undefined;\n  /**\n   * For all types of updates, this is\n   * a `Date` object representing the creation time or commit time of the update.\n   */\n  createdAt: Date;\n  /**\n   * For updates of type `UpdateInfoType.ROLLBACK`, this is always set to `undefined`.\n   */\n  manifest: undefined;\n};\n\n/**\n * Combined structure representing any type of update.\n */\nexport type UpdateInfo = UpdateInfoNew | UpdateInfoRollback;\n\n/**\n * The type returned by [`useUpdates()`](#useupdates).\n */\nexport type UseUpdatesReturnType = {\n  /**\n   * Information on the currently running app.\n   */\n  currentlyRunning: CurrentlyRunningInfo;\n  /**\n   * Whether the startup procedure is still running. This may happen if the fallbackToCacheTimeout is shorter than the time taken to\n   * fetch a new update during app launch.\n   */\n  isStartupProcedureRunning: boolean;\n  /**\n   * If a new available update has been found, either by using [`checkForUpdateAsync()`](#updatescheckforupdateasync),\n   * or by the `UpdateEvent` listener in `useUpdates()`, this will contain the information for that update.\n   */\n  availableUpdate?: UpdateInfo;\n  /**\n   * If an available update has been downloaded, this will contain the information\n   * for that update.\n   */\n  downloadedUpdate?: UpdateInfo;\n  /**\n   * True if a new available update has been found, false otherwise.\n   */\n  isUpdateAvailable: boolean;\n  /**\n   * True if a new available update is available and has been downloaded.\n   */\n  isUpdatePending: boolean;\n  /**\n   * True if the app is currently checking for a new available update from the server.\n   */\n  isChecking: boolean;\n  /**\n   * True if the app is currently downloading an update from the server.\n   */\n  isDownloading: boolean;\n  /**\n   * True if the app is currently in the process of restarting.\n   */\n  isRestarting: boolean;\n  /**\n   * Number of times the JS has been restarted (for example, by calling reloadAsync) since app cold start.\n   */\n  restartCount: number;\n  /**\n   * If an error is returned from either the startup check for updates, or a call to [`checkForUpdateAsync()`](#updatescheckforupdateasync),\n   * the error description will appear here.\n   */\n  checkError?: Error;\n  /**\n   * If an error is returned from either a startup update download, or a call to [`fetchUpdateAsync()`](#updatesfetchupdateasync),\n   * the error description will appear here.\n   */\n  downloadError?: Error;\n  /**\n   * A `Date` object representing the last time this client checked for an available update,\n   * or `undefined` if no check has yet occurred since the app started. Does not persist across\n   * app reloads or restarts.\n   */\n  lastCheckForUpdateTimeSinceRestart?: Date;\n};\n"]}