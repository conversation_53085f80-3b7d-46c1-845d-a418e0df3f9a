import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import { resolve, dirname } from 'path';
import { fileURLToPath } from 'url';

// Charger les variables d'environnement
const envPath = resolve(dirname(fileURLToPath(import.meta.url)), '..', '.env');
dotenv.config({ path: envPath });

const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY;

console.log('🔍 Diagnostic réseau - Mientior Livraison\n');

async function diagnoseNetwork() {
  try {
    // 1. Vérifier la configuration
    console.log('1. 🔧 Vérification de la configuration...');
    console.log('Supabase URL:', supabaseUrl ? '✅ Configuré' : '❌ Manquant');
    console.log('Supabase Key:', supabaseKey ? '✅ Configuré' : '❌ Manquant');
    
    if (!supabaseUrl || !supabaseKey) {
      console.error('❌ Configuration Supabase incomplète');
      return;
    }
    
    // 2. Test de connectivité basique
    console.log('\n2. 🌐 Test de connectivité Internet...');
    try {
      const response = await fetch('https://www.google.com', { 
        method: 'HEAD',
        timeout: 5000 
      });
      console.log('✅ Connectivité Internet OK');
    } catch (error) {
      console.error('❌ Pas de connectivité Internet:', error.message);
      return;
    }
    
    // 3. Test de résolution DNS Supabase
    console.log('\n3. 🔍 Test de résolution DNS Supabase...');
    try {
      const url = new URL(supabaseUrl);
      console.log('Host Supabase:', url.hostname);
      
      const response = await fetch(supabaseUrl, { 
        method: 'HEAD',
        timeout: 10000 
      });
      console.log('✅ Serveur Supabase accessible');
      console.log('Status:', response.status);
    } catch (error) {
      console.error('❌ Serveur Supabase inaccessible:', error.message);
      
      if (error.message.includes('ENOTFOUND')) {
        console.log('💡 Problème de résolution DNS');
      } else if (error.message.includes('ECONNREFUSED')) {
        console.log('💡 Connexion refusée par le serveur');
      } else if (error.message.includes('timeout')) {
        console.log('💡 Timeout de connexion');
      }
      return;
    }
    
    // 4. Test de l'API Supabase
    console.log('\n4. 🧪 Test de l\'API Supabase...');
    
    const supabase = createClient(supabaseUrl, supabaseKey);
    
    try {
      // Test simple de l'API
      const { data, error } = await supabase
        .from('users')
        .select('count')
        .limit(1);
      
      if (error) {
        console.log('⚠️ Erreur API Supabase:', error.message);
        
        if (error.message.includes('relation') && error.message.includes('does not exist')) {
          console.log('💡 Table "users" n\'existe pas - problème de migration');
        } else if (error.message.includes('JWT')) {
          console.log('💡 Problème d\'authentification JWT');
        } else {
          console.log('💡 Erreur API générique');
        }
      } else {
        console.log('✅ API Supabase accessible');
      }
    } catch (apiError) {
      console.error('❌ Erreur lors du test API:', apiError.message);
      
      if (apiError.message.includes('Network request failed')) {
        console.log('💡 Problème de réseau lors de l\'appel API');
      }
    }
    
    // 5. Test d'authentification
    console.log('\n5. 🔐 Test du service d\'authentification...');
    try {
      const { data: authData, error: authError } = await supabase.auth.getSession();
      
      if (authError) {
        console.log('⚠️ Erreur auth service:', authError.message);
      } else {
        console.log('✅ Service d\'authentification accessible');
        console.log('Session:', authData.session ? 'Active' : 'Aucune');
      }
    } catch (authTestError) {
      console.error('❌ Erreur test auth:', authTestError.message);
    }
    
    // 6. Recommandations
    console.log('\n📋 DIAGNOSTIC RÉSEAU:');
    console.log('====================');
    
    console.log('\n🔧 SOLUTIONS POSSIBLES:');
    console.log('1. Vérifier la connexion Internet');
    console.log('2. Redémarrer l\'émulateur Android');
    console.log('3. Vérifier les paramètres proxy/firewall');
    console.log('4. Essayer sur un appareil physique');
    console.log('5. Vérifier le statut de Supabase: https://status.supabase.com');
    
    console.log('\n🚀 COMMANDES UTILES:');
    console.log('# Redémarrer l\'émulateur');
    console.log('adb kill-server && adb start-server');
    console.log('');
    console.log('# Vérifier la connectivité depuis l\'émulateur');
    console.log('adb shell ping google.com');
    console.log('');
    console.log('# Redémarrer Metro');
    console.log('npx expo start --clear');
    
  } catch (error) {
    console.error('❌ Erreur générale du diagnostic:', error.message);
  }
}

// Test de connectivité simple
async function quickConnectivityTest() {
  console.log('\n🚀 Test de connectivité rapide...\n');
  
  const tests = [
    { name: 'Google', url: 'https://www.google.com' },
    { name: 'Supabase', url: supabaseUrl },
    { name: 'GitHub', url: 'https://api.github.com' }
  ];
  
  for (const test of tests) {
    try {
      const start = Date.now();
      const response = await fetch(test.url, { 
        method: 'HEAD',
        timeout: 5000 
      });
      const duration = Date.now() - start;
      console.log(`✅ ${test.name}: ${response.status} (${duration}ms)`);
    } catch (error) {
      console.log(`❌ ${test.name}: ${error.message}`);
    }
  }
}

// Exécuter les tests
async function runDiagnostics() {
  await quickConnectivityTest();
  await diagnoseNetwork();
}

runDiagnostics();
