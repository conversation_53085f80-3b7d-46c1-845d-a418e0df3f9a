# Script PowerShell pour déplacer le projet vers un chemin plus court
# Usage: .\scripts\move-project.ps1

Write-Host "🚀 Déplacement du projet Mientior Livraison vers un chemin plus court" -ForegroundColor Green

# Définir les chemins
$currentPath = "C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new"
$newPath = "C:\dev\mientior-livraison"

Write-Host "📁 Chemin actuel: $currentPath" -ForegroundColor Yellow
Write-Host "📁 Nouveau chemin: $newPath" -ForegroundColor Yellow

# Créer le dossier de destination
if (!(Test-Path "C:\dev")) {
    Write-Host "📂 Création du dossier C:\dev..." -ForegroundColor Blue
    New-Item -ItemType Directory -Path "C:\dev" -Force
}

# Vérifier si le dossier de destination existe déjà
if (Test-Path $newPath) {
    Write-Host "⚠️  Le dossier de destination existe déjà!" -ForegroundColor Red
    $response = Read-Host "Voulez-vous le supprimer et continuer? (y/N)"
    if ($response -eq "y" -or $response -eq "Y") {
        Remove-Item $newPath -Recurse -Force
        Write-Host "🗑️  Dossier supprimé" -ForegroundColor Yellow
    } else {
        Write-Host "❌ Opération annulée" -ForegroundColor Red
        exit 1
    }
}

# Copier le projet
Write-Host "📋 Copie du projet en cours..." -ForegroundColor Blue
try {
    Copy-Item $currentPath $newPath -Recurse -Force
    Write-Host "✅ Projet copié avec succès!" -ForegroundColor Green
} catch {
    Write-Host "❌ Erreur lors de la copie: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Instructions pour la suite
Write-Host "`n🎯 PROCHAINES ÉTAPES:" -ForegroundColor Green
Write-Host "1. Ouvrir un nouveau terminal dans: $newPath" -ForegroundColor White
Write-Host "2. Exécuter: cd $newPath" -ForegroundColor White
Write-Host "3. Exécuter: npm install" -ForegroundColor White
Write-Host "4. Exécuter: npx expo run:android" -ForegroundColor White

Write-Host "`n📋 COMMANDES À EXÉCUTER:" -ForegroundColor Green
Write-Host "cd `"$newPath`"" -ForegroundColor Cyan
Write-Host "npm install" -ForegroundColor Cyan
Write-Host "npx expo run:android" -ForegroundColor Cyan

Write-Host "`n✨ Le chemin plus court devrait résoudre les problèmes de build!" -ForegroundColor Green
