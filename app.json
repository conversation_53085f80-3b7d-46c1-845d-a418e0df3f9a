{"expo": {"name": "Mientior - <PERSON><PERSON><PERSON>", "slug": "mientior-livraison-afrique", "version": "1.0.0", "main": "index.js", "orientation": "portrait", "platforms": ["ios", "android", "web"], "userInterfaceStyle": "light", "scheme": "mientior", "description": "Plateforme de livraison connectant clients, marchands et livreurs à travers l'Afrique", "splash": {"resizeMode": "contain", "backgroundColor": "#0DCAA8"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.mientior.livraison", "buildNumber": "1"}, "android": {"adaptiveIcon": {"backgroundColor": "#0DCAA8"}, "edgeToEdgeEnabled": true, "softwareKeyboardLayoutMode": "pan", "package": "com.mientior.livraison", "versionCode": 1, "permissions": ["ACCESS_FINE_LOCATION", "ACCESS_COARSE_LOCATION", "CAMERA", "NOTIFICATIONS"]}, "web": {"bundler": "metro"}, "plugins": [], "extra": {"eas": {"projectId": "3109c06c-5ec5-47a3-a214-7a15339a0f85"}}}}