{"version": 3, "file": "ExpoUpdates.js", "sourceRoot": "", "sources": ["../src/ExpoUpdates.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,mBAAmB,EAAE,MAAM,mBAAmB,CAAC;AAIxD;;GAEG;AACH,eAAe,mBAAmB,CAAoB,aAAa,CAAC,CAAC", "sourcesContent": ["import { requireNativeModule } from 'expo-modules-core';\n\nimport { ExpoUpdatesModule } from './ExpoUpdatesModule.types';\n\n/**\n * @internal\n */\nexport default requireNativeModule<ExpoUpdatesModule>('ExpoUpdates');\n"]}