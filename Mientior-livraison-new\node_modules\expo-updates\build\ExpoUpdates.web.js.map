{"version": 3, "file": "ExpoUpdates.web.js", "sourceRoot": "", "sources": ["../src/ExpoUpdates.web.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,iBAAiB,EAAE,MAAM,mBAAmB,CAAC;AAOpE,OAAO,EAML,mCAAmC,GACpC,MAAM,iBAAiB,CAAC;AAEzB,MAAM,iBAAkB,SAAQ,YAA2B;IACzD,iBAAiB,GAAY,KAAK,CAAC;IACnC,qBAAqB,GAAkB,IAAI,CAAC;IAC5C,cAAc,GAAkB,IAAI,CAAC;IACrC,gBAAgB,GAAY,KAAK,CAAC;IAClC,SAAS,GAAY,IAAI,CAAC;IAC1B,qBAAqB,GAAyB,SAAS,CAAC;IACxD,cAAc,GAAW,EAAE,CAAC;IAC5B,kBAAkB,GAAyC,QAAQ,CAAC;IACpE,OAAO,GAAW,EAAE,CAAC;IACrB,wDAAwD,GAAY,KAAK,CAAC;IAC1E,QAAQ,CAAsB;IAC9B,UAAU,CAAsB;IAChC,cAAc,CAAsB;IACpC,QAAQ,CAAwB;IAChC,WAAW,CAAsC;IAEjD,cAAc,GAKV;QACF,yBAAyB,EAAE,KAAK;QAChC,iBAAiB,EAAE,KAAK;QACxB,eAAe,EAAE,KAAK;QACtB,UAAU,EAAE,KAAK;QACjB,aAAa,EAAE,KAAK;QACpB,YAAY,EAAE,KAAK;QACnB,YAAY,EAAE,CAAC;QACf,cAAc,EAAE,CAAC;KAClB,CAAC;IAEF,KAAK,CAAC,MAAM;QACV,IAAI,OAAO,MAAM,KAAK,WAAW;YAAE,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAClE,CAAC;IAED,KAAK,CAAC,mBAAmB;QACvB,OAAO;YACL,WAAW,EAAE,KAAK;YAClB,QAAQ,EAAE,SAAS;YACnB,oBAAoB,EAAE,KAAK;YAC3B,MAAM,EAAE,mCAAmC,CAAC,6BAA6B;SAC1E,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,mBAAmB;QACvB,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,KAAK,CAAC,kBAAkB,CAAC,GAAW,EAAE,KAAoB,IAAkB,CAAC;IAE7E,KAAK,CAAC,mBAAmB,CAAC,MAAc;QACtC,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,KAAK,CAAC,oBAAoB,KAAmB,CAAC;IAE9C,KAAK,CAAC,gBAAgB;QACpB,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,oBAAoB,EAAE,KAAK,EAAE,CAAC;IAC5E,CAAC;CACF;AAED,eAAe,iBAAiB,CAAC,iBAAiB,EAAE,aAAa,CAAC,CAAC", "sourcesContent": ["import { NativeModule, registerWebModule } from 'expo-modules-core';\n\nimport {\n  UpdatesCheckAutomaticallyNativeValue,\n  UpdatesEvents,\n  UpdatesModuleInterface,\n} from './ExpoUpdatesModule.types';\nimport {\n  Manifest,\n  UpdatesNativeStateMachineContext,\n  UpdateCheckResultNotAvailable,\n  UpdatesLogEntry,\n  UpdateFetchResultFailure,\n  UpdateCheckResultNotAvailableReason,\n} from './Updates.types';\n\nclass ExpoUpdatesModule extends NativeModule<UpdatesEvents> implements UpdatesModuleInterface {\n  isEmergencyLaunch: boolean = false;\n  emergencyLaunchReason: string | null = null;\n  launchDuration: number | null = null;\n  isEmbeddedLaunch: boolean = false;\n  isEnabled: boolean = true;\n  isUsingEmbeddedAssets?: boolean | undefined = undefined;\n  runtimeVersion: string = '';\n  checkAutomatically: UpdatesCheckAutomaticallyNativeValue = 'ALWAYS';\n  channel: string = '';\n  shouldDeferToNativeForAPIMethodAvailabilityInDevelopment: boolean = false;\n  updateId?: string | undefined;\n  commitTime?: string | undefined;\n  manifestString?: string | undefined;\n  manifest?: Manifest | undefined;\n  localAssets?: Record<string, string> | undefined;\n\n  initialContext: UpdatesNativeStateMachineContext & {\n    latestManifestString?: string | undefined;\n    downloadedManifestString?: string | undefined;\n    lastCheckForUpdateTimeString?: string | undefined;\n    rollbackString?: string | undefined;\n  } = {\n    isStartupProcedureRunning: false,\n    isUpdateAvailable: false,\n    isUpdatePending: false,\n    isChecking: false,\n    isDownloading: false,\n    isRestarting: false,\n    restartCount: 0,\n    sequenceNumber: 0,\n  };\n\n  async reload(): Promise<void> {\n    if (typeof window !== 'undefined') window.location.reload(true);\n  }\n\n  async checkForUpdateAsync(): Promise<UpdateCheckResultNotAvailable> {\n    return {\n      isAvailable: false,\n      manifest: undefined,\n      isRollBackToEmbedded: false,\n      reason: UpdateCheckResultNotAvailableReason.NO_UPDATE_AVAILABLE_ON_SERVER,\n    };\n  }\n\n  async getExtraParamsAsync(): Promise<Record<string, string>> {\n    return {};\n  }\n\n  async setExtraParamAsync(key: string, value: string | null): Promise<void> {}\n\n  async readLogEntriesAsync(maxAge: number): Promise<UpdatesLogEntry[]> {\n    return [];\n  }\n\n  async clearLogEntriesAsync(): Promise<void> {}\n\n  async fetchUpdateAsync(): Promise<UpdateFetchResultFailure> {\n    return { isNew: false, manifest: undefined, isRollBackToEmbedded: false };\n  }\n}\n\nexport default registerWebModule(ExpoUpdatesModule, 'ExpoUpdates');\n"]}