,expo.modules.kotlin.exception.CodedException"expo.modules.kotlin.modules.Module3expo.modules.core.interfaces.LifecycleEventListener$android.hardware.SensorEventListener2expo.modules.core.interfaces.ActivityEventListenerkotlin.Enum$expo.modules.kotlin.types.Enumerable"expo.modules.kotlin.records.Recordjava.io.Serializable-expo.modules.location.records.LocationOptionsandroid.app.Serviceandroid.os.Binder0expo.modules.interfaces.taskManager.TaskConsumer9expo.modules.interfaces.taskManager.TaskConsumerInterface                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            