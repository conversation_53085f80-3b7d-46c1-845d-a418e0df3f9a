#!/usr/bin/env node

/**
 * Script de vérification du package name
 * Usage: node scripts/verify-package.cjs
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Vérification du Package Name pour Mientior Livraison\n');

// 1. Vérifier build.gradle
function checkBuildGradle() {
  console.log('📱 Vérification build.gradle...');
  
  const buildGradlePath = path.join(__dirname, '../android/app/build.gradle');
  
  if (!fs.existsSync(buildGradlePath)) {
    console.log('❌ build.gradle non trouvé');
    return false;
  }
  
  const content = fs.readFileSync(buildGradlePath, 'utf8');
  
  // Vérifier namespace
  const namespaceMatch = content.match(/namespace\s*['"]([^'"]+)['"]/);
  if (namespaceMatch) {
    console.log(`✅ Namespace: ${namespaceMatch[1]}`);
  }
  
  // Vérifier applicationId
  const appIdMatch = content.match(/applicationId\s*['"]([^'"]+)['"]/);
  if (appIdMatch) {
    console.log(`✅ Application ID: ${appIdMatch[1]}`);
    return appIdMatch[1] === 'com.eliseedev.mientiorlivraison';
  }
  
  return false;
}

// 2. Vérifier app.json
function checkAppJson() {
  console.log('\n📄 Vérification app.json...');
  
  const appJsonPath = path.join(__dirname, '../app.json');
  
  if (!fs.existsSync(appJsonPath)) {
    console.log('❌ app.json non trouvé');
    return false;
  }
  
  const content = JSON.parse(fs.readFileSync(appJsonPath, 'utf8'));
  
  if (content.expo?.android?.package) {
    console.log(`✅ Android package: ${content.expo.android.package}`);
    return content.expo.android.package === 'com.eliseedev.mientiorlivraison';
  }
  
  if (content.expo?.ios?.bundleIdentifier) {
    console.log(`✅ iOS bundle: ${content.expo.ios.bundleIdentifier}`);
  }
  
  return false;
}

// 3. Vérifier les fichiers Kotlin
function checkKotlinFiles() {
  console.log('\n🔧 Vérification fichiers Kotlin...');
  
  const kotlinDir = path.join(__dirname, '../android/app/src/main/java/com/eliseedev/mientiorlivraison');
  
  if (!fs.existsSync(kotlinDir)) {
    console.log('❌ Dossier Kotlin non trouvé');
    return false;
  }
  
  const files = fs.readdirSync(kotlinDir);
  console.log(`✅ Fichiers trouvés: ${files.join(', ')}`);
  
  // Vérifier MainActivity.kt
  const mainActivityPath = path.join(kotlinDir, 'MainActivity.kt');
  if (fs.existsSync(mainActivityPath)) {
    const content = fs.readFileSync(mainActivityPath, 'utf8');
    const packageMatch = content.match(/package\s+([^\s\n]+)/);
    if (packageMatch) {
      console.log(`✅ MainActivity package: ${packageMatch[1]}`);
      return packageMatch[1] === 'com.eliseedev.mientiorlivraison';
    }
  }
  
  return false;
}

// 4. Vérifier AndroidManifest.xml
function checkAndroidManifest() {
  console.log('\n📋 Vérification AndroidManifest.xml...');
  
  const manifestPath = path.join(__dirname, '../android/app/src/main/AndroidManifest.xml');
  
  if (!fs.existsSync(manifestPath)) {
    console.log('❌ AndroidManifest.xml non trouvé');
    return false;
  }
  
  const content = fs.readFileSync(manifestPath, 'utf8');
  
  // Vérifier API Key Google Maps
  if (content.includes('com.google.android.geo.API_KEY')) {
    console.log('✅ API Key Google Maps trouvée');
  } else {
    console.log('❌ API Key Google Maps manquante');
  }
  
  // Vérifier permissions
  const permissions = ['ACCESS_FINE_LOCATION', 'ACCESS_COARSE_LOCATION', 'INTERNET'];
  permissions.forEach(permission => {
    if (content.includes(permission)) {
      console.log(`✅ Permission ${permission} trouvée`);
    } else {
      console.log(`❌ Permission ${permission} manquante`);
    }
  });
  
  return true;
}

// Fonction principale
function main() {
  const results = {
    buildGradle: checkBuildGradle(),
    appJson: checkAppJson(),
    kotlinFiles: checkKotlinFiles(),
    androidManifest: checkAndroidManifest()
  };
  
  console.log('\n📊 RÉSUMÉ DE LA VÉRIFICATION\n');
  console.log('='.repeat(50));
  
  if (results.buildGradle && results.kotlinFiles) {
    console.log('🎉 PACKAGE NAME CORRECTEMENT CONFIGURÉ !');
    console.log('✅ Package: com.eliseedev.mientiorlivraison');
    console.log('✅ Fichiers Kotlin mis à jour');
    console.log('✅ Configuration Android correcte');
    
    console.log('\n🚀 PROCHAINES ÉTAPES:');
    console.log('1. Configurer Google Cloud Console avec:');
    console.log('   Package name: com.eliseedev.mientiorlivraison');
    console.log('   SHA-1: 5E:8F:16:06:2E:A3:CD:2C:4A:0D:54:78:76:BA:A6:F3:8C:AB:F6:25');
    console.log('2. Tester l\'application');
  } else {
    console.log('⚠️  PROBLÈMES DÉTECTÉS:');
    if (!results.buildGradle) console.log('   ❌ build.gradle incorrect');
    if (!results.appJson) console.log('   ❌ app.json incorrect');
    if (!results.kotlinFiles) console.log('   ❌ Fichiers Kotlin incorrects');
    if (!results.androidManifest) console.log('   ❌ AndroidManifest.xml incorrect');
  }
  
  console.log('\n' + '='.repeat(50));
}

// Exécution
main();
