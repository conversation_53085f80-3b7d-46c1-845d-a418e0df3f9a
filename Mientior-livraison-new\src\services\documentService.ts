/**
 * Service de gestion des documents et images pour Mientior Livraison
 * Gestion des permissions, sélection et upload de fichiers
 */

import * as ImagePicker from 'expo-image-picker';
import * as DocumentPicker from 'expo-document-picker';
import { Alert, Platform } from 'react-native';

// Types pour les documents
export interface DocumentAsset {
  uri: string;
  name: string;
  size?: number;
  mimeType?: string;
  type: 'image' | 'document';
}

export interface ImageAsset {
  uri: string;
  width?: number;
  height?: number;
  type?: string;
  fileSize?: number;
}

export interface DocumentSelectionOptions {
  allowMultiple?: boolean;
  types?: string[];
  copyToCacheDirectory?: boolean;
}

export interface ImageSelectionOptions {
  allowsEditing?: boolean;
  aspect?: [number, number];
  quality?: number;
  allowsMultipleSelection?: boolean;
}

class DocumentService {
  private static instance: DocumentService;

  static getInstance(): DocumentService {
    if (!DocumentService.instance) {
      DocumentService.instance = new DocumentService();
    }
    return DocumentService.instance;
  }

  /**
   * Demande les permissions pour accéder à la galerie d'images
   */
  async requestImagePermissions(): Promise<boolean> {
    try {
      console.log('📷 Demande de permission pour la galerie d\'images...');

      if (Platform.OS !== 'web') {
        const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
        
        if (status !== 'granted') {
          Alert.alert(
            'Permission requise',
            'Mientior Livraison a besoin d\'accéder à votre galerie pour sélectionner des photos.',
            [
              { text: 'Annuler', style: 'cancel' },
              { 
                text: 'Paramètres', 
                onPress: () => {
                  console.log('🔧 Redirection vers les paramètres...');
                }
              }
            ]
          );
          return false;
        }
      }

      console.log('✅ Permission galerie accordée');
      return true;
    } catch (error) {
      console.error('❌ Erreur lors de la demande de permission galerie:', error);
      return false;
    }
  }

  /**
   * Demande les permissions pour accéder à la caméra
   */
  async requestCameraPermissions(): Promise<boolean> {
    try {
      console.log('📸 Demande de permission pour la caméra...');

      if (Platform.OS !== 'web') {
        const { status } = await ImagePicker.requestCameraPermissionsAsync();
        
        if (status !== 'granted') {
          Alert.alert(
            'Permission requise',
            'Mientior Livraison a besoin d\'accéder à votre caméra pour prendre des photos.',
            [
              { text: 'Annuler', style: 'cancel' },
              { 
                text: 'Paramètres', 
                onPress: () => {
                  console.log('🔧 Redirection vers les paramètres...');
                }
              }
            ]
          );
          return false;
        }
      }

      console.log('✅ Permission caméra accordée');
      return true;
    } catch (error) {
      console.error('❌ Erreur lors de la demande de permission caméra:', error);
      return false;
    }
  }

  /**
   * Sélectionne une image depuis la galerie
   */
  async selectImageFromGallery(options: ImageSelectionOptions = {}): Promise<ImageAsset | null> {
    try {
      const hasPermission = await this.requestImagePermissions();
      if (!hasPermission) {
        return null;
      }

      console.log('🖼️ Sélection d\'image depuis la galerie...');

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: options.allowsEditing ?? true,
        aspect: options.aspect ?? [1, 1],
        quality: options.quality ?? 0.8,
        allowsMultipleSelection: options.allowsMultipleSelection ?? false,
      });

      if (result.canceled || !result.assets || result.assets.length === 0) {
        console.log('📷 Sélection d\'image annulée');
        return null;
      }

      const asset = result.assets[0];
      const imageAsset: ImageAsset = {
        uri: asset.uri,
        width: asset.width,
        height: asset.height,
        type: asset.type,
        fileSize: asset.fileSize,
      };

      console.log('✅ Image sélectionnée:', imageAsset.uri);
      return imageAsset;
    } catch (error) {
      console.error('❌ Erreur lors de la sélection d\'image:', error);
      Alert.alert('Erreur', 'Impossible de sélectionner l\'image');
      return null;
    }
  }

  /**
   * Prend une photo avec la caméra
   */
  async takePhotoWithCamera(options: ImageSelectionOptions = {}): Promise<ImageAsset | null> {
    try {
      const hasPermission = await this.requestCameraPermissions();
      if (!hasPermission) {
        return null;
      }

      console.log('📸 Prise de photo avec la caméra...');

      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: options.allowsEditing ?? true,
        aspect: options.aspect ?? [1, 1],
        quality: options.quality ?? 0.8,
      });

      if (result.canceled || !result.assets || result.assets.length === 0) {
        console.log('📸 Prise de photo annulée');
        return null;
      }

      const asset = result.assets[0];
      const imageAsset: ImageAsset = {
        uri: asset.uri,
        width: asset.width,
        height: asset.height,
        type: asset.type,
        fileSize: asset.fileSize,
      };

      console.log('✅ Photo prise:', imageAsset.uri);
      return imageAsset;
    } catch (error) {
      console.error('❌ Erreur lors de la prise de photo:', error);
      Alert.alert('Erreur', 'Impossible de prendre la photo');
      return null;
    }
  }

  /**
   * Sélectionne des documents
   */
  async selectDocuments(options: DocumentSelectionOptions = {}): Promise<DocumentAsset[]> {
    try {
      console.log('📄 Sélection de documents...');

      const result = await DocumentPicker.getDocumentAsync({
        type: options.types ?? ['image/*', 'application/pdf'],
        multiple: options.allowMultiple ?? true,
        copyToCacheDirectory: options.copyToCacheDirectory ?? true,
      });

      if (result.canceled || !result.assets) {
        console.log('📄 Sélection de documents annulée');
        return [];
      }

      const documents: DocumentAsset[] = result.assets.map((asset: DocumentPicker.DocumentPickerAsset) => ({
        uri: asset.uri,
        name: asset.name,
        size: asset.size ?? undefined,
        mimeType: asset.mimeType ?? undefined,
        type: this.getDocumentType(asset.mimeType ?? ''),
      }));

      console.log(`✅ ${documents.length} document(s) sélectionné(s)`);
      return documents;
    } catch (error) {
      console.error('❌ Erreur lors de la sélection de documents:', error);
      Alert.alert('Erreur', 'Impossible de sélectionner les documents');
      return [];
    }
  }

  /**
   * Affiche un sélecteur d'action pour choisir entre caméra et galerie
   */
  async showImagePickerOptions(options: ImageSelectionOptions = {}): Promise<ImageAsset | null> {
    return new Promise((resolve) => {
      Alert.alert(
        'Sélectionner une photo',
        'Choisissez comment vous souhaitez ajouter votre photo',
        [
          {
            text: 'Annuler',
            style: 'cancel',
            onPress: () => resolve(null),
          },
          {
            text: 'Galerie',
            onPress: async () => {
              const image = await this.selectImageFromGallery(options);
              resolve(image);
            },
          },
          {
            text: 'Caméra',
            onPress: async () => {
              const image = await this.takePhotoWithCamera(options);
              resolve(image);
            },
          },
        ]
      );
    });
  }

  /**
   * Valide la taille d'un fichier
   */
  validateFileSize(fileSize: number, maxSizeMB: number = 10): boolean {
    const maxSizeBytes = maxSizeMB * 1024 * 1024;
    return fileSize <= maxSizeBytes;
  }

  /**
   * Valide le type de fichier
   */
  validateFileType(mimeType: string, allowedTypes: string[] = ['image/*', 'application/pdf']): boolean {
    return allowedTypes.some(type => {
      if (type.endsWith('/*')) {
        const baseType = type.replace('/*', '');
        return mimeType.startsWith(baseType);
      }
      return mimeType === type;
    });
  }

  /**
   * Détermine le type de document basé sur le MIME type
   */
  private getDocumentType(mimeType: string): 'image' | 'document' {
    if (mimeType.startsWith('image/')) {
      return 'image';
    }
    return 'document';
  }

  /**
   * Formate la taille d'un fichier en format lisible
   */
  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}

export const documentService = DocumentService.getInstance();
