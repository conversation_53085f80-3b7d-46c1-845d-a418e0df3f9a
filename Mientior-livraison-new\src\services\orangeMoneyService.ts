/**
 * Service Orange Money pour l'intégration des paiements mobiles en Côte d'Ivoire
 * API Orange Money CI - Version 2024
 */

import { supabase } from './supabase';
import {
  OrangeMoneyConfig,
  OrangeMoneyPaymentRequest,
  OrangeMoneyPaymentResponse,
  OrangeMoneyStatusRequest,
  OrangeMoneyStatusResponse,
  OrangeMoneyWebhookData,
  OrangeMoneyTransaction,
  OrangeMoneyError,
  OrangeMoneyStatus,
  OrangeMoneyErrorCode,
  PhoneValidation,
  ORANGE_CI_PREFIXES,
  ORANGE_MONEY_LIMITS,
  ORANGE_MONEY_ERROR_MESSAGES,
} from '../types/orangeMoney';

// Configuration Orange Money (à déplacer vers les variables d'environnement)
const ORANGE_MONEY_CONFIG: OrangeMoneyConfig = {
  apiUrl: __DEV__ 
    ? 'https://api.orange.com/orange-money-webpay/dev/v1'
    : 'https://api.orange.com/orange-money-webpay/ci/v1',
  merchantId: process.env.EXPO_PUBLIC_ORANGE_MONEY_MERCHANT_ID || 'DEMO_MERCHANT',
  apiKey: process.env.EXPO_PUBLIC_ORANGE_MONEY_API_KEY || 'demo_api_key',
  secretKey: process.env.EXPO_PUBLIC_ORANGE_MONEY_SECRET_KEY || 'demo_secret',
  environment: __DEV__ ? 'sandbox' : 'production',
  timeout: 30000, // 30 secondes
  retryAttempts: 3,
};

class OrangeMoneyService {
  private config: OrangeMoneyConfig;

  constructor(config: OrangeMoneyConfig = ORANGE_MONEY_CONFIG) {
    this.config = config;
  }

  /**
   * Valide un numéro de téléphone Orange Money CI
   */
  validatePhoneNumber(phoneNumber: string): PhoneValidation {
    // Nettoyer le numéro (supprimer espaces, tirets, etc.)
    const cleaned = phoneNumber.replace(/[\s\-\(\)]/g, '');
    
    // Vérifier le format ivoirien
    let formatted = cleaned;
    
    // Ajouter l'indicatif pays si manquant
    if (cleaned.startsWith('0')) {
      formatted = '+225' + cleaned.substring(1);
    } else if (cleaned.startsWith('225')) {
      formatted = '+' + cleaned;
    } else if (!cleaned.startsWith('+225')) {
      formatted = '+225' + cleaned;
    }

    // Extraire les 2 premiers chiffres après +225
    const prefix = formatted.substring(4, 6);
    
    // Vérifier si c'est un préfixe Orange
    const isOrange = ORANGE_CI_PREFIXES.includes(prefix as any);
    
    // Vérifier la longueur (doit être +225XXXXXXXX = 13 caractères)
    const isValidLength = formatted.length === 13;
    
    // Vérifier que ce sont tous des chiffres après +225
    const isNumeric = /^\+225\d{8}$/.test(formatted);

    const isValid = isOrange && isValidLength && isNumeric;

    return {
      isValid,
      formatted: isValid ? formatted : phoneNumber,
      operator: isOrange ? 'orange' : 'unknown',
      message: !isValid ? this.getPhoneValidationMessage(phoneNumber, prefix) : undefined,
    };
  }

  private getPhoneValidationMessage(phoneNumber: string, prefix: string): string {
    if (!phoneNumber || phoneNumber.length < 8) {
      return 'Numéro de téléphone trop court';
    }
    if (phoneNumber.length > 15) {
      return 'Numéro de téléphone trop long';
    }
    if (!ORANGE_CI_PREFIXES.includes(prefix as any)) {
      return 'Ce numéro n\'est pas un numéro Orange Money (07, 08, 09)';
    }
    return 'Format de numéro invalide';
  }

  /**
   * Valide le montant de la transaction
   */
  validateAmount(amount: number): { isValid: boolean; message?: string } {
    if (amount < ORANGE_MONEY_LIMITS.MIN_AMOUNT) {
      return {
        isValid: false,
        message: `Montant minimum: ${ORANGE_MONEY_LIMITS.MIN_AMOUNT.toLocaleString()} FCFA`,
      };
    }

    if (amount > ORANGE_MONEY_LIMITS.MAX_AMOUNT) {
      return {
        isValid: false,
        message: `Montant maximum: ${ORANGE_MONEY_LIMITS.MAX_AMOUNT.toLocaleString()} FCFA`,
      };
    }

    return { isValid: true };
  }

  /**
   * Génère une signature pour l'authentification API
   */
  private generateSignature(data: any, timestamp: string): string {
    // Implémentation simplifiée - à remplacer par la vraie signature Orange Money
    const payload = JSON.stringify(data) + timestamp + this.config.secretKey;
    // En production, utiliser crypto.createHmac('sha256', secretKey).update(payload).digest('hex')
    return Buffer.from(payload).toString('base64').substring(0, 32);
  }

  /**
   * Effectue une requête HTTP vers l'API Orange Money
   */
  private async makeApiRequest<T>(
    endpoint: string,
    method: 'GET' | 'POST' = 'POST',
    data?: any
  ): Promise<T> {
    const timestamp = new Date().toISOString();
    const signature = this.generateSignature(data, timestamp);

    const headers = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${this.config.apiKey}`,
      'X-Merchant-Id': this.config.merchantId,
      'X-Timestamp': timestamp,
      'X-Signature': signature,
    };

    try {
      // Créer un AbortController pour gérer le timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), this.config.timeout);

      const response = await fetch(`${this.config.apiUrl}${endpoint}`, {
        method,
        headers,
        body: data ? JSON.stringify(data) : undefined,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      return result as T;
    } catch (error) {
      console.error('Orange Money API Error:', error);
      throw this.handleApiError(error);
    }
  }

  /**
   * Gère les erreurs de l'API Orange Money
   */
  private handleApiError(error: any): OrangeMoneyError {
    const timestamp = new Date().toISOString();

    if (error.name === 'AbortError' || error.message?.includes('timeout')) {
      return {
        code: 'TIMEOUT',
        message: ORANGE_MONEY_ERROR_MESSAGES.TIMEOUT,
        details: error.message,
        timestamp,
      };
    }

    if (error.message?.includes('network') || error.message?.includes('fetch')) {
      return {
        code: 'NETWORK_ERROR',
        message: ORANGE_MONEY_ERROR_MESSAGES.NETWORK_ERROR,
        details: error.message,
        timestamp,
      };
    }

    // Mapper les codes d'erreur spécifiques Orange Money
    const errorCode = this.mapErrorCode(error.code || error.message);

    return {
      code: errorCode,
      message: ORANGE_MONEY_ERROR_MESSAGES[errorCode],
      details: error.message,
      timestamp,
    };
  }

  private mapErrorCode(errorString: string): OrangeMoneyErrorCode {
    const errorLower = errorString?.toLowerCase() || '';
    
    if (errorLower.includes('insufficient') || errorLower.includes('balance')) {
      return 'INSUFFICIENT_BALANCE';
    }
    if (errorLower.includes('phone') || errorLower.includes('number')) {
      return 'INVALID_PHONE';
    }
    if (errorLower.includes('amount') || errorLower.includes('limit')) {
      return 'INVALID_AMOUNT';
    }
    if (errorLower.includes('cancelled') || errorLower.includes('cancel')) {
      return 'USER_CANCELLED';
    }
    
    return 'UNKNOWN_ERROR';
  }

  /**
   * Initie un paiement Orange Money
   */
  async initiatePayment(request: OrangeMoneyPaymentRequest): Promise<OrangeMoneyPaymentResponse> {
    console.log('🍊 Initiating Orange Money payment:', {
      amount: request.amount,
      phone: request.phoneNumber,
      orderId: request.orderId,
    });

    // Validation des données
    const phoneValidation = this.validatePhoneNumber(request.phoneNumber);
    if (!phoneValidation.isValid) {
      throw {
        code: 'INVALID_PHONE',
        message: phoneValidation.message || ORANGE_MONEY_ERROR_MESSAGES.INVALID_PHONE,
        timestamp: new Date().toISOString(),
      } as OrangeMoneyError;
    }

    const amountValidation = this.validateAmount(request.amount);
    if (!amountValidation.isValid) {
      throw {
        code: 'INVALID_AMOUNT',
        message: amountValidation.message || ORANGE_MONEY_ERROR_MESSAGES.INVALID_AMOUNT,
        timestamp: new Date().toISOString(),
      } as OrangeMoneyError;
    }

    // Préparer la requête API
    const apiRequest = {
      merchant_key: this.config.merchantId,
      currency: request.currency,
      order_id: request.orderId,
      amount: request.amount,
      return_url: request.returnUrl || 'mientior://payment/success',
      cancel_url: request.cancelUrl || 'mientior://payment/cancel',
      notif_url: request.notificationUrl || 'https://api.mientior.com/webhooks/orange-money',
      lang: 'fr',
      reference: `MIENTIOR_${request.orderId}_${Date.now()}`,
      customer_msisdn: phoneValidation.formatted,
      customer_name: request.customerName || 'Client Mientior',
      customer_email: request.customerEmail || '<EMAIL>',
    };

    try {
      // En mode développement, simuler la réponse
      if (__DEV__) {
        return this.simulatePaymentResponse(request);
      }

      // Appel API réel Orange Money
      const response = await this.makeApiRequest<any>('/webpayment', 'POST', apiRequest);

      return {
        success: true,
        transactionId: response.transaction_id || `OM_${Date.now()}`,
        externalReference: response.reference || apiRequest.reference,
        status: 'pending',
        amount: request.amount,
        currency: request.currency,
        phoneNumber: phoneValidation.formatted,
        message: 'Paiement initié avec succès',
        paymentUrl: response.payment_url,
        qrCode: response.qr_code,
        expiresAt: new Date(Date.now() + 15 * 60 * 1000).toISOString(), // 15 minutes
        createdAt: new Date().toISOString(),
      };

    } catch (error) {
      console.error('Orange Money payment initiation failed:', error);
      throw error;
    }
  }

  /**
   * Simule une réponse de paiement pour le développement
   */
  private simulatePaymentResponse(request: OrangeMoneyPaymentRequest): OrangeMoneyPaymentResponse {
    const transactionId = `OM_DEV_${Date.now()}`;
    const reference = `MIENTIOR_${request.orderId}_${Date.now()}`;

    // Simuler différents scénarios selon le montant
    const shouldFail = request.amount === 666; // Montant spécial pour tester les échecs
    const shouldTimeout = request.amount === 999; // Montant spécial pour tester les timeouts

    if (shouldFail) {
      throw {
        code: 'INSUFFICIENT_BALANCE',
        message: ORANGE_MONEY_ERROR_MESSAGES.INSUFFICIENT_BALANCE,
        timestamp: new Date().toISOString(),
      } as OrangeMoneyError;
    }

    return {
      success: true,
      transactionId,
      externalReference: reference,
      status: 'pending',
      amount: request.amount,
      currency: request.currency,
      phoneNumber: request.phoneNumber,
      message: 'Paiement initié avec succès (Mode développement)',
      paymentUrl: `https://webpay.orange.com/payment/${transactionId}`,
      qrCode: `data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==`,
      expiresAt: new Date(Date.now() + 15 * 60 * 1000).toISOString(),
      createdAt: new Date().toISOString(),
    };
  }

  /**
   * Vérifie le statut d'une transaction
   */
  async checkTransactionStatus(request: OrangeMoneyStatusRequest): Promise<OrangeMoneyStatusResponse> {
    console.log('🍊 Checking Orange Money transaction status:', request.transactionId);

    try {
      // En mode développement, simuler la vérification
      if (__DEV__) {
        return this.simulateStatusResponse(request);
      }

      // Appel API réel Orange Money
      const response = await this.makeApiRequest<any>(
        `/transactions/${request.transactionId}/status`,
        'GET'
      );

      return {
        success: true,
        transactionId: response.transaction_id,
        externalReference: response.reference,
        status: this.mapApiStatus(response.status),
        amount: response.amount,
        currency: response.currency,
        phoneNumber: response.customer_msisdn,
        completedAt: response.completed_at,
        failureReason: response.failure_reason,
        metadata: response.metadata,
      };

    } catch (error) {
      console.error('Orange Money status check failed:', error);
      throw error;
    }
  }

  /**
   * Simule une vérification de statut pour le développement
   */
  private simulateStatusResponse(request: OrangeMoneyStatusRequest): OrangeMoneyStatusResponse {
    // Simuler une progression de statut basée sur le temps
    const now = Date.now();
    const transactionTime = parseInt(request.transactionId.split('_').pop() || '0');
    const elapsed = now - transactionTime;

    let status: OrangeMoneyStatus = 'pending';
    let completedAt: string | undefined;

    if (elapsed > 10000) { // Après 10 secondes, marquer comme réussi
      status = 'success';
      completedAt = new Date().toISOString();
    } else if (elapsed > 5000) { // Après 5 secondes, marquer comme en traitement
      status = 'processing';
    }

    return {
      success: true,
      transactionId: request.transactionId,
      externalReference: request.externalReference || `REF_${request.transactionId}`,
      status,
      amount: 1000, // Montant simulé
      currency: 'XOF',
      phoneNumber: '+22507123456',
      completedAt,
      metadata: { environment: 'development' },
    };
  }

  /**
   * Mappe les statuts de l'API Orange Money vers nos statuts internes
   */
  private mapApiStatus(apiStatus: string): OrangeMoneyStatus {
    const statusMap: Record<string, OrangeMoneyStatus> = {
      'PENDING': 'pending',
      'PROCESSING': 'processing',
      'SUCCESS': 'success',
      'COMPLETED': 'success',
      'FAILED': 'failed',
      'CANCELLED': 'cancelled',
      'EXPIRED': 'expired',
      'REFUNDED': 'refunded',
    };

    return statusMap[apiStatus?.toUpperCase()] || 'pending';
  }

  /**
   * Sauvegarde une transaction dans Supabase
   */
  async saveTransaction(transaction: Partial<OrangeMoneyTransaction>): Promise<OrangeMoneyTransaction> {
    try {
      if (!supabase) {
        console.warn('⚠️ Supabase client not available for Orange Money transaction save');
        // Retourner une transaction simulée en mode offline
        return {
          id: `offline_${Date.now()}`,
          orderId: transaction.orderId || '',
          userId: transaction.userId || '',
          phoneNumber: transaction.phoneNumber || '',
          amount: transaction.amount || 0,
          currency: transaction.currency || 'XOF',
          transactionId: transaction.transactionId || '',
          externalReference: transaction.externalReference || '',
          status: transaction.status || 'pending',
          initiatedAt: transaction.initiatedAt || new Date().toISOString(),
          webhookData: transaction.webhookData,
          errorMessage: transaction.errorMessage,
          retryCount: transaction.retryCount || 0,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        } as OrangeMoneyTransaction;
      }

      const { data, error } = await supabase
        .from('orange_money_transactions')
        .insert([{
          order_id: transaction.orderId,
          user_id: transaction.userId,
          phone_number: transaction.phoneNumber,
          amount: transaction.amount,
          currency: transaction.currency || 'XOF',
          transaction_id: transaction.transactionId,
          external_reference: transaction.externalReference,
          status: transaction.status || 'pending',
          initiated_at: transaction.initiatedAt || new Date().toISOString(),
          webhook_data: transaction.webhookData,
          error_message: transaction.errorMessage,
          retry_count: transaction.retryCount || 0,
        }])
        .select()
        .single();

      if (error) {
        console.error('Error saving Orange Money transaction:', error);
        throw error;
      }

      return data as OrangeMoneyTransaction;
    } catch (error) {
      console.error('Failed to save Orange Money transaction:', error);
      throw error;
    }
  }

  /**
   * Met à jour le statut d'une transaction
   */
  async updateTransactionStatus(
    transactionId: string,
    status: OrangeMoneyStatus,
    additionalData?: Partial<OrangeMoneyTransaction>
  ): Promise<void> {
    try {
      const updateData: any = {
        status,
        updated_at: new Date().toISOString(),
      };

      if (status === 'success' || status === 'failed') {
        updateData.completed_at = new Date().toISOString();
      }

      if (additionalData?.webhookData) {
        updateData.webhook_data = additionalData.webhookData;
      }

      if (additionalData?.errorMessage) {
        updateData.error_message = additionalData.errorMessage;
      }

      if (!supabase) {
        console.warn('⚠️ Supabase client not available for Orange Money transaction update');
        return;
      }

      const { error } = await supabase
        .from('orange_money_transactions')
        .update(updateData)
        .eq('transaction_id', transactionId);

      if (error) {
        console.error('Error updating Orange Money transaction:', error);
        throw error;
      }

      console.log('🍊 Orange Money transaction updated:', transactionId, status);
    } catch (error) {
      console.error('Failed to update Orange Money transaction:', error);
      throw error;
    }
  }

  /**
   * Récupère une transaction par ID
   */
  async getTransaction(transactionId: string): Promise<OrangeMoneyTransaction | null> {
    try {
      if (!supabase) {
        console.warn('⚠️ Supabase client not available for Orange Money transaction fetch');
        return null;
      }

      const { data, error } = await supabase
        .from('orange_money_transactions')
        .select('*')
        .eq('transaction_id', transactionId)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
        console.error('Error fetching Orange Money transaction:', error);
        throw error;
      }

      return data as OrangeMoneyTransaction | null;
    } catch (error) {
      console.error('Failed to fetch Orange Money transaction:', error);
      return null;
    }
  }
}

// Instance singleton du service Orange Money
export const orangeMoneyService = new OrangeMoneyService();

// Export de la classe pour les tests
export { OrangeMoneyService };
