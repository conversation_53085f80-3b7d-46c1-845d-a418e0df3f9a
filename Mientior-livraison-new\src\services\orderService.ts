import { supabase } from './supabase';
import AsyncStorage from '@react-native-async-storage/async-storage';

export interface OrderItem {
  id: string;
  order_id: string;
  product_id: string;
  product_name: string;
  quantity: number;
  unit_price: number;
  total_price: number;
  special_instructions?: string;
}

export interface Order {
  id: string;
  order_number: string;
  client_id: string;
  merchant_id: string;
  merchant_name: string;
  merchant_logo?: string;
  delivery_address_id: string;
  delivery_address: string;
  service_type: 'restaurant' | 'colis' | 'marchandises' | 'pharmacie' | 'epicerie';
  status: 'en_attente' | 'confirmee' | 'en_preparation' | 'prete' | 'en_livraison' | 'livree' | 'annulee';
  priority: 'normale' | 'elevee' | 'urgente' | 'express';
  subtotal: number;
  delivery_fee: number;
  service_fee: number;
  taxes: number;
  discount_amount: number;
  total_amount: number;
  payment_method: string;
  payment_status: 'en_attente' | 'paye' | 'echec' | 'rembourse';
  special_instructions?: string;
  estimated_preparation_time: number;
  estimated_delivery_time: number;
  actual_preparation_time?: number;
  actual_delivery_time?: number;
  order_date: string;
  confirmed_at?: string;
  prepared_at?: string;
  picked_up_at?: string;
  delivered_at?: string;
  cancelled_at?: string;
  created_at: string;
  updated_at: string;
  // Relations
  items?: OrderItem[];
  // Calculated fields
  can_track?: boolean;
  can_cancel?: boolean;
  can_reorder?: boolean;
  status_color?: string;
  status_icon?: string;
}

export interface OrderFilters {
  status?: Order['status'][];
  service_type?: Order['service_type'];
  date_from?: string;
  date_to?: string;
  limit?: number;
}

const ORDERS_CACHE_KEY = 'user_orders_cache';
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

class OrderService {
  /**
   * Récupère les commandes d'un utilisateur
   */
  async getUserOrders(userId: string, filters?: OrderFilters): Promise<Order[]> {
    try {
      console.log('📦 Fetching orders for user:', userId);

      if (!supabase) {
        console.error('❌ Supabase not available');
        return [];
      }

      // Utiliser exclusivement la vue sécurisée Supabase
      console.log('📦 Using secure view for orders...');

      const { data: viewData, error: viewError } = await supabase
        .from('user_orders_view')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(filters?.limit || 10);

      if (viewError) {
        console.error('❌ Error with secure view:', viewError);
        // Retourner tableau vide au lieu de données offline
        return [];
      }

      // Transformer les données de la vue
      const orders = viewData?.map((viewOrder: any) => ({
        id: viewOrder.id,
        order_number: viewOrder.order_number,
        client_id: viewOrder.client_id,
        merchant_id: viewOrder.merchant_id,
        merchant_name: viewOrder.merchant_name || 'Restaurant',
        merchant_logo: viewOrder.merchant_logo,
        delivery_address_id: viewOrder.delivery_address_id,
        delivery_address: viewOrder.delivery_address || 'Adresse de livraison',
        service_type: viewOrder.service_type,
        status: viewOrder.status,
        priority: this.mapPriority(viewOrder.priority),
        subtotal: viewOrder.subtotal,
        delivery_fee: viewOrder.delivery_fee,
        service_fee: viewOrder.service_fee || 0,
        taxes: viewOrder.taxes || 0,
        discount_amount: viewOrder.discount_amount || 0,
        total_amount: viewOrder.total_amount,
        payment_method: viewOrder.payment_method,
        payment_status: this.mapPaymentStatus(viewOrder.payment_status),
        special_instructions: viewOrder.special_instructions || '',
        estimated_preparation_time: viewOrder.estimated_preparation_time || 30,
        estimated_delivery_time: viewOrder.estimated_delivery_time || 45,
        order_date: viewOrder.order_date,
        confirmed_at: viewOrder.confirmed_at,
        prepared_at: viewOrder.prepared_at,
        delivered_at: viewOrder.delivered_at,
        cancelled_at: viewOrder.cancelled_at,
        created_at: viewOrder.created_at,
        updated_at: viewOrder.updated_at,
        can_track: ['confirmee', 'en_preparation', 'prete', 'en_livraison'].includes(viewOrder.status),
        can_cancel: ['en_attente', 'confirmee'].includes(viewOrder.status),
        can_reorder: ['livree', 'annulee'].includes(viewOrder.status),
        status_color: this.getStatusColor(viewOrder.status),
        status_icon: this.getStatusIcon(viewOrder.status),
      })) || [];

      console.log(`✅ ${orders.length} orders fetched from secure view`);
      return orders;

    } catch (error) {
      console.error('❌ Error in getUserOrders:', error);
      return [];
    }
  }

  /**
   * Récupère les commandes récentes d'un utilisateur
   */
  async getRecentOrders(userId: string, limit = 5): Promise<Order[]> {
    return this.getUserOrders(userId, { limit });
  }

  /**
   * Récupère les commandes actives (en cours)
   */
  async getActiveOrders(userId: string): Promise<Order[]> {
    return this.getUserOrders(userId, {
      status: ['en_attente', 'confirmee', 'en_preparation', 'prete', 'en_livraison'],
      limit: 10,
    });
  }

  /**
   * Récupère une commande par ID
   */
  async getOrderById(orderId: string): Promise<Order | null> {
    try {
      if (!supabase) {
        const cached = await this.getCachedOrders('');
        return cached.find(o => o.id === orderId) || null;
      }

      const { data, error } = await supabase
        .from('orders')
        .select(`
          *,
          merchant_profiles(
            business_name,
            logo_url
          ),
          addresses(
            address_line_1
          ),
          order_items(
            id,
            product_id,
            quantity,
            unit_price,
            total_price,
            special_instructions,
            products(name)
          )
        `)
        .eq('id', orderId)
        .single();

      if (error || !data) {
        console.error('❌ Error fetching order:', error);
        return null;
      }

      return this.transformDatabaseOrder(data);
    } catch (error) {
      console.error('❌ Error in getOrderById:', error);
      return null;
    }
  }

  /**
   * Crée une nouvelle commande
   */
  async createOrder(orderData: {
    userId: string;
    merchantId: string;
    items: Array<{
      productId: string;
      productName: string;
      quantity: number;
      unitPrice: number;
      specialInstructions?: string;
    }>;
    deliveryAddressId: string;
    paymentMethod: string;
    subtotal: number;
    deliveryFee: number;
    serviceFee: number;
    promoDiscount: number;
    total: number;
    specialInstructions?: string;
    promoCode?: string;
  }): Promise<{ success: boolean; orderId?: string; error?: string }> {
    try {
      if (!supabase) {
        throw new Error('Supabase not available');
      }

      console.log('📦 Creating new order:', orderData);

      // Générer un numéro de commande unique
      const orderNumber = `CMD-${Date.now()}`;

      // Créer la commande principale
      const { data: order, error: orderError } = await supabase
        .from('orders')
        .insert({
          order_number: orderNumber,
          client_id: orderData.userId,
          merchant_id: orderData.merchantId,
          delivery_address_id: orderData.deliveryAddressId,
          service_type: 'restaurant', // Par défaut
          status: 'en_attente',
          priority: 'normale',
          subtotal: orderData.subtotal,
          delivery_fee: orderData.deliveryFee,
          service_fee: orderData.serviceFee,
          taxes: 0,
          discount_amount: orderData.promoDiscount,
          total_amount: orderData.total,
          payment_method: orderData.paymentMethod,
          payment_status: 'en_attente',
          special_instructions: orderData.specialInstructions || '',
          estimated_preparation_time: 30,
          estimated_delivery_time: 45,
          order_date: new Date().toISOString(),
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        })
        .select()
        .single();

      if (orderError) {
        console.error('❌ Error creating order:', orderError);
        return { success: false, error: 'Erreur lors de la création de la commande' };
      }

      console.log('✅ Order created:', order.id);

      // Créer les items de la commande
      const orderItems = orderData.items.map(item => ({
        order_id: order.id,
        product_id: item.productId,
        product_name: item.productName,
        quantity: item.quantity,
        unit_price: item.unitPrice,
        total_price: item.quantity * item.unitPrice,
        special_instructions: item.specialInstructions || '',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      }));

      const { error: itemsError } = await supabase
        .from('order_items')
        .insert(orderItems);

      if (itemsError) {
        console.error('❌ Error creating order items:', itemsError);
        // Supprimer la commande si les items n'ont pas pu être créés
        await supabase.from('orders').delete().eq('id', order.id);
        return { success: false, error: 'Erreur lors de la création des articles' };
      }

      console.log('✅ Order items created');

      // Mettre à jour le statut de paiement si nécessaire
      if (orderData.paymentMethod === 'cash') {
        await supabase
          .from('orders')
          .update({
            payment_status: 'paye',
            confirmed_at: new Date().toISOString(),
            status: 'confirmee',
            updated_at: new Date().toISOString(),
          })
          .eq('id', order.id);
      }

      return { success: true, orderId: order.id };

    } catch (error) {
      console.error('❌ Error in createOrder:', error);
      return { success: false, error: 'Une erreur inattendue est survenue' };
    }
  }

  /**
   * Annule une commande
   */
  async cancelOrder(orderId: string, reason?: string): Promise<boolean> {
    try {
      if (!supabase) {
        throw new Error('Supabase not available');
      }

      const { error } = await supabase
        .from('orders')
        .update({
          status: 'annulee',
          cancelled_at: new Date().toISOString(),
          special_instructions: reason ? `Annulée: ${reason}` : 'Annulée par le client',
          updated_at: new Date().toISOString(),
        })
        .eq('id', orderId);

      if (error) {
        console.error('❌ Error cancelling order:', error);
        return false;
      }

      console.log('✅ Order cancelled successfully:', orderId);
      return true;
    } catch (error) {
      console.error('❌ Error in cancelOrder:', error);
      return false;
    }
  }

  /**
   * Transforme les données de la base en format Order
   */
  private transformDatabaseOrder(dbOrder: any): Order {
    const order: Order = {
      id: dbOrder.id,
      order_number: dbOrder.order_number,
      client_id: dbOrder.client_id,
      merchant_id: dbOrder.merchant_id,
      merchant_name: dbOrder.merchant_profiles?.business_name || 'Restaurant',
      merchant_logo: dbOrder.merchant_profiles?.logo_url,
      delivery_address_id: dbOrder.delivery_address_id,
      delivery_address: dbOrder.addresses?.address_line_1 || 'Adresse non disponible',
      service_type: dbOrder.service_type,
      status: dbOrder.status,
      priority: dbOrder.priority,
      subtotal: dbOrder.subtotal,
      delivery_fee: dbOrder.delivery_fee,
      service_fee: dbOrder.service_fee,
      taxes: dbOrder.taxes,
      discount_amount: dbOrder.discount_amount,
      total_amount: dbOrder.total_amount,
      payment_method: dbOrder.payment_method,
      payment_status: dbOrder.payment_status || 'paye', // Valeur par défaut
      special_instructions: dbOrder.special_instructions,
      estimated_preparation_time: dbOrder.estimated_preparation_time,
      estimated_delivery_time: dbOrder.estimated_delivery_time,
      actual_preparation_time: dbOrder.actual_preparation_time,
      actual_delivery_time: dbOrder.actual_delivery_time,
      order_date: dbOrder.order_date,
      confirmed_at: dbOrder.confirmed_at,
      prepared_at: dbOrder.prepared_at,
      picked_up_at: dbOrder.picked_up_at,
      delivered_at: dbOrder.delivered_at,
      cancelled_at: dbOrder.cancelled_at,
      created_at: dbOrder.created_at,
      updated_at: dbOrder.updated_at,
    };

    // Transformer les items si présents
    if (dbOrder.order_items) {
      order.items = dbOrder.order_items.map((item: any) => ({
        id: item.id,
        order_id: order.id,
        product_id: item.product_id,
        product_name: item.products?.name || 'Produit',
        quantity: item.quantity,
        unit_price: item.unit_price,
        total_price: item.total_price,
        special_instructions: item.special_instructions,
      }));
    }

    // Calculer les champs dérivés
    order.can_track = ['confirmee', 'en_preparation', 'prete', 'en_livraison'].includes(order.status);
    order.can_cancel = ['en_attente', 'confirmee'].includes(order.status);
    order.can_reorder = ['livree', 'annulee'].includes(order.status);
    order.status_color = this.getStatusColor(order.status);
    order.status_icon = this.getStatusIcon(order.status);

    return order;
  }

  /**
   * Retourne la couleur associée à un statut
   */
  private getStatusColor(status: Order['status']): string {
    switch (status) {
      case 'en_attente': return '#FF9800';
      case 'confirmee': return '#2196F3';
      case 'en_preparation': return '#9C27B0';
      case 'prete': return '#FF5722';
      case 'en_livraison': return '#3F51B5';
      case 'livree': return '#4CAF50';
      case 'annulee': return '#F44336';
      default: return '#666';
    }
  }

  /**
   * Retourne l'icône associée à un statut
   */
  private getStatusIcon(status: Order['status']): string {
    switch (status) {
      case 'en_attente': return 'time';
      case 'confirmee': return 'checkmark-circle';
      case 'en_preparation': return 'restaurant';
      case 'prete': return 'bag-check';
      case 'en_livraison': return 'bicycle';
      case 'livree': return 'checkmark-done-circle';
      case 'annulee': return 'close-circle';
      default: return 'help';
    }
  }

  /**
   * Mappe les priorités de la base vers les types attendus
   */
  private mapPriority(priority: string | null | undefined): Order['priority'] {
    switch (priority?.toLowerCase()) {
      case 'elevee':
      case 'élevée':
      case 'high':
        return 'elevee';
      case 'urgente':
      case 'urgent':
        return 'urgente';
      case 'express':
        return 'express';
      case 'normale':
      case 'normal':
      default:
        return 'normale';
    }
  }

  /**
   * Mappe les statuts de paiement de la base vers les types attendus
   */
  private mapPaymentStatus(paymentStatus: string | null | undefined): Order['payment_status'] {
    switch (paymentStatus?.toLowerCase()) {
      case 'en_attente':
      case 'pending':
      case 'waiting':
        return 'en_attente';
      case 'paye':
      case 'paid':
      case 'success':
        return 'paye';
      case 'echec':
      case 'failed':
      case 'error':
        return 'echec';
      case 'rembourse':
      case 'refunded':
        return 'rembourse';
      default:
        return 'paye'; // Valeur par défaut sécurisée
    }
  }

  /**
   * Cache les commandes
   */
  private async cacheOrders(userId: string, orders: Order[]): Promise<void> {
    try {
      const cacheData = {
        orders,
        timestamp: Date.now(),
        userId,
      };
      await AsyncStorage.setItem(`${ORDERS_CACHE_KEY}_${userId}`, JSON.stringify(cacheData));
    } catch (error) {
      console.warn('⚠️ Failed to cache orders:', error);
    }
  }

  /**
   * Récupère les commandes depuis le cache
   */
  private async getCachedOrders(userId: string): Promise<Order[]> {
    try {
      const cached = await AsyncStorage.getItem(`${ORDERS_CACHE_KEY}_${userId}`);
      if (!cached) return []; // Retourner tableau vide au lieu de mock

      const cacheData = JSON.parse(cached);
      const isExpired = Date.now() - cacheData.timestamp > CACHE_DURATION;

      if (isExpired || cacheData.userId !== userId) {
        await AsyncStorage.removeItem(`${ORDERS_CACHE_KEY}_${userId}`);
        return []; // Retourner tableau vide au lieu de mock
      }

      console.log(`📦 Using cached orders for user ${userId}`);
      return cacheData.orders || [];
    } catch (error) {
      console.warn('⚠️ Failed to get cached orders:', error);
      return []; // Retourner tableau vide au lieu de mock
    }
  }



  /**
   * Version simplifiée qui évite les problèmes de colonnes
   */
  private async getSimplifiedOrders(userId: string): Promise<Order[]> {
    try {
      // Utiliser seulement les colonnes de base
      if (!supabase) {
        console.warn('⚠️ Supabase client not available for simplified orders');
        return [];
      }

      const { data, error } = await supabase
        .from('orders')
        .select(`
          id,
          order_number,
          client_id,
          merchant_id,
          service_type,
          status,
          subtotal,
          delivery_fee,
          total_amount,
          payment_method,
          order_date,
          created_at,
          updated_at
        `)
        .eq('client_id', userId)
        .order('created_at', { ascending: false })
        .limit(10);

      if (error) {
        console.error('❌ Error in simplified orders query:', error);
        return [];
      }

      // Transformation simplifiée
      const orders = data?.map((dbOrder: any) => ({
        id: dbOrder.id,
        order_number: dbOrder.order_number,
        client_id: dbOrder.client_id,
        merchant_id: dbOrder.merchant_id,
        merchant_name: 'Restaurant', // Valeur par défaut
        merchant_logo: undefined,
        delivery_address_id: '',
        delivery_address: 'Adresse de livraison',
        service_type: dbOrder.service_type,
        status: dbOrder.status,
        priority: 'normale' as const,
        subtotal: dbOrder.subtotal,
        delivery_fee: dbOrder.delivery_fee,
        service_fee: 0,
        taxes: 0,
        discount_amount: 0,
        total_amount: dbOrder.total_amount,
        payment_method: dbOrder.payment_method,
        payment_status: 'paye' as const,
        special_instructions: '',
        estimated_preparation_time: 30,
        estimated_delivery_time: 45,
        order_date: dbOrder.order_date,
        created_at: dbOrder.created_at,
        updated_at: dbOrder.updated_at,
        can_track: ['confirmee', 'en_preparation', 'prete', 'en_livraison'].includes(dbOrder.status),
        can_cancel: ['en_attente', 'confirmee'].includes(dbOrder.status),
        can_reorder: ['livree', 'annulee'].includes(dbOrder.status),
        status_color: this.getStatusColor(dbOrder.status),
        status_icon: this.getStatusIcon(dbOrder.status),
      })) || [];

      console.log(`✅ ${orders.length} simplified orders fetched for user ${userId}`);
      return orders;

    } catch (error) {
      console.error('❌ Error in getSimplifiedOrders:', error);
      return [];
    }
  }

  /**
   * Données mock pour le développement
   */
  private getMockOrders(userId: string): Order[] {
    return [
      {
        id: 'mock-order-1',
        order_number: 'CMD-2024-001',
        client_id: userId,
        merchant_id: 'mock-1',
        merchant_name: 'Chez Maman Africa',
        merchant_logo: 'https://images.unsplash.com/photo-1555939594-58d7cb561ad1?w=100&h=100&fit=crop',
        delivery_address_id: 'addr-1',
        delivery_address: 'Cocody, Riviera Golf, Abidjan',
        service_type: 'restaurant',
        status: 'en_livraison',
        priority: 'normale',
        subtotal: 8000,
        delivery_fee: 1500,
        service_fee: 500,
        taxes: 0,
        discount_amount: 0,
        total_amount: 10000,
        payment_method: 'Orange Money',
        payment_status: 'paye',
        estimated_preparation_time: 25,
        estimated_delivery_time: 40,
        order_date: new Date().toISOString(),
        confirmed_at: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
        created_at: new Date(Date.now() - 45 * 60 * 1000).toISOString(),
        updated_at: new Date(Date.now() - 10 * 60 * 1000).toISOString(),
        can_track: true,
        can_cancel: false,
        can_reorder: false,
        status_color: '#3F51B5',
        status_icon: 'bicycle',
      },
      {
        id: 'mock-order-2',
        order_number: 'CMD-2024-002',
        client_id: userId,
        merchant_id: 'mock-2',
        merchant_name: 'Le Petit Dakar',
        merchant_logo: 'https://images.unsplash.com/photo-1567620905732-2d1ec7ab7445?w=100&h=100&fit=crop',
        delivery_address_id: 'addr-1',
        delivery_address: 'Plateau, Abidjan',
        service_type: 'restaurant',
        status: 'livree',
        priority: 'normale',
        subtotal: 6500,
        delivery_fee: 1200,
        service_fee: 500,
        taxes: 0,
        discount_amount: 1000,
        total_amount: 7200,
        payment_method: 'MTN Mobile Money',
        payment_status: 'paye',
        estimated_preparation_time: 30,
        estimated_delivery_time: 45,
        order_date: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
        delivered_at: new Date(Date.now() - 23 * 60 * 60 * 1000).toISOString(),
        created_at: new Date(Date.now() - 25 * 60 * 60 * 1000).toISOString(),
        updated_at: new Date(Date.now() - 23 * 60 * 60 * 1000).toISOString(),
        can_track: false,
        can_cancel: false,
        can_reorder: true,
        status_color: '#4CAF50',
        status_icon: 'checkmark-done-circle',
      },
    ];
  }
}

export const orderService = new OrderService();
