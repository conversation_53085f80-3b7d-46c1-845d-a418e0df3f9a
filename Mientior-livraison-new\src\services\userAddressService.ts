import { supabase } from './supabase';
import AsyncStorage from '@react-native-async-storage/async-storage';

export interface UserAddress {
  id: string;
  user_id: string;
  label: string;
  address: string;
  details?: string;
  coordinates: {
    latitude: number;
    longitude: number;
  };
  address_type: 'home' | 'work' | 'other';
  is_default: boolean;
  created_at: string;
  updated_at: string;
}

export interface CreateAddressData {
  label: string;
  address: string;
  details?: string;
  coordinates: {
    latitude: number;
    longitude: number;
  };
  address_type: 'home' | 'work' | 'other';
  is_default?: boolean;
}

export interface UpdateAddressData extends Partial<CreateAddressData> {
  id: string;
}

const ADDRESSES_CACHE_KEY = 'user_addresses_cache';
const CACHE_DURATION = 30 * 60 * 1000; // 30 minutes

class UserAddressService {
  /**
   * Récupère toutes les adresses d'un utilisateur
   */
  async getUserAddresses(userId: string): Promise<UserAddress[]> {
    try {
      console.log('📍 Fetching user addresses for:', userId);

      if (!supabase) {
        console.error('❌ Supabase not available');
        return [];
      }

      // Utiliser exclusivement la vue sécurisée Supabase
      console.log('📍 Using secure view for addresses...');

      const { data, error } = await supabase
        .from('user_addresses_view')
        .select(`
          id,
          user_id,
          label,
          address_line_1,
          landmark,
          is_default,
          created_at,
          updated_at
        `)
        .order('is_default', { ascending: false })
        .order('created_at', { ascending: false });

      if (error) {
        console.error('❌ Error with secure addresses view:', error);
        // Retourner tableau vide au lieu de données offline
        return [];
      }

      // Transformer les données de la vue
      const addresses = data?.map((viewAddress: any) => ({
        id: viewAddress.id,
        user_id: viewAddress.user_id,
        label: viewAddress.label,
        address: `${viewAddress.address_line_1}${viewAddress.address_line_2 ? ', ' + viewAddress.address_line_2 : ''}`,
        details: viewAddress.landmark,
        coordinates: this.extractCoordinates(viewAddress.coordinates),
        address_type: viewAddress.label?.toLowerCase() === 'domicile' ? 'home' :
                     viewAddress.label?.toLowerCase() === 'bureau' ? 'work' : 'other',
        is_default: viewAddress.is_default,
        created_at: viewAddress.created_at,
        updated_at: viewAddress.updated_at,
      })) || [];

      console.log(`✅ ${addresses.length} addresses fetched from secure view`);
      return addresses;

    } catch (error) {
      console.error('❌ Error in getUserAddresses:', error);
      return []; // Retourner tableau vide au lieu de données mock
    }
  }

  /**
   * Crée une nouvelle adresse
   */
  async createAddress(userId: string, addressData: CreateAddressData): Promise<UserAddress> {
    try {
      console.log('📍 Creating new address for user:', userId);

      if (!supabase) {
        throw new Error('Supabase not available');
      }

      // Préparer les données pour la base
      const dbData = {
        user_id: userId,
        label: addressData.label,
        address_line_1: addressData.address,
        address_line_2: addressData.details,
        landmark: addressData.details,
        coordinates: `POINT(${addressData.coordinates.longitude} ${addressData.coordinates.latitude})`,
        is_default: addressData.is_default || false,
      };

      const { data, error } = await supabase
        .from('addresses')
        .insert([dbData])
        .select()
        .single();

      if (error) {
        console.error('❌ Error creating address:', error);
        throw new Error(`Erreur lors de la création de l'adresse: ${error.message}`);
      }

      // Récupérer l'adresse créée
      const { data: addressWithLocation, error: fetchError } = await supabase
        .from('addresses')
        .select('*')
        .eq('id', data.id)
        .single();

      if (fetchError) {
        console.error('❌ Error fetching created address:', fetchError);
        throw new Error('Adresse créée mais impossible de la récupérer');
      }

      const newAddress = this.transformDatabaseAddress(addressWithLocation);
      
      // Invalider le cache
      await this.invalidateCache(userId);
      
      console.log('✅ Address created successfully:', newAddress.id);
      return newAddress;

    } catch (error) {
      console.error('❌ Error in createAddress:', error);
      throw error;
    }
  }

  /**
   * Met à jour une adresse existante
   */
  async updateAddress(userId: string, updateData: UpdateAddressData): Promise<UserAddress> {
    try {
      console.log('📍 Updating address:', updateData.id);

      // Vérifier si c'est une adresse mock ou offline
      if (updateData.id.startsWith('mock-') || updateData.id.startsWith('offline-')) {
        console.log('⚠️ Cannot update mock/offline address, returning mock data');
        return {
          id: updateData.id,
          user_id: userId,
          label: updateData.label || 'Domicile',
          address: updateData.address || 'Adresse mock',
          details: updateData.details,
          coordinates: updateData.coordinates,
          address_type: updateData.address_type || 'home',
          is_default: updateData.is_default || false,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        };
      }

      if (!supabase) {
        throw new Error('Supabase not available');
      }

      // Préparer les données pour la mise à jour
      const dbData: any = {
        updated_at: new Date().toISOString(),
      };

      if (updateData.label) dbData.label = updateData.label;
      if (updateData.address) dbData.address = updateData.address;
      if (updateData.details !== undefined) dbData.details = updateData.details;
      if (updateData.address_type) dbData.address_type = updateData.address_type;
      if (updateData.is_default !== undefined) dbData.is_default = updateData.is_default;
      
      if (updateData.coordinates) {
        dbData.coordinates = `POINT(${updateData.coordinates.longitude} ${updateData.coordinates.latitude})`;
      }

      const { data, error } = await supabase
        .from('addresses')
        .update(dbData)
        .eq('id', updateData.id)
        .eq('user_id', userId)
        .select()
        .single();

      if (error) {
        console.error('❌ Error updating address:', error);
        throw new Error(`Erreur lors de la mise à jour: ${error.message}`);
      }

      // Récupérer l'adresse mise à jour
      const { data: addressWithLocation, error: fetchError } = await supabase
        .from('addresses')
        .select('*')
        .eq('id', updateData.id)
        .single();

      if (fetchError) {
        console.error('❌ Error fetching updated address:', fetchError);
        throw new Error('Adresse mise à jour mais impossible de la récupérer');
      }

      const updatedAddress = this.transformDatabaseAddress(addressWithLocation);
      
      // Invalider le cache
      await this.invalidateCache(userId);
      
      console.log('✅ Address updated successfully:', updatedAddress.id);
      return updatedAddress;

    } catch (error) {
      console.error('❌ Error in updateAddress:', error);
      throw error;
    }
  }

  /**
   * Supprime une adresse
   */
  async deleteAddress(userId: string, addressId: string): Promise<void> {
    try {
      console.log('📍 Deleting address:', addressId);

      // Vérifier si c'est une adresse mock ou offline
      if (addressId.startsWith('mock-') || addressId.startsWith('offline-')) {
        console.log('⚠️ Cannot delete mock/offline address, ignoring');
        return;
      }

      if (!supabase) {
        throw new Error('Supabase not available');
      }

      const { error } = await supabase
        .from('addresses')
        .delete()
        .eq('id', addressId)
        .eq('user_id', userId);

      if (error) {
        console.error('❌ Error deleting address:', error);
        throw new Error(`Erreur lors de la suppression: ${error.message}`);
      }

      // Invalider le cache
      await this.invalidateCache(userId);
      
      console.log('✅ Address deleted successfully:', addressId);

    } catch (error) {
      console.error('❌ Error in deleteAddress:', error);
      throw error;
    }
  }

  /**
   * Définit une adresse comme adresse par défaut
   */
  async setDefaultAddress(userId: string, addressId: string): Promise<void> {
    try {
      console.log('📍 Setting default address:', addressId);

      // Vérifier si c'est une adresse mock ou offline
      if (addressId.startsWith('mock-') || addressId.startsWith('offline-')) {
        console.log('⚠️ Cannot set mock/offline address as default, ignoring');
        return;
      }

      if (!supabase) {
        throw new Error('Supabase not available');
      }

      // La fonction de trigger se charge de désactiver les autres adresses par défaut
      const { error } = await supabase
        .from('addresses')
        .update({ is_default: true, updated_at: new Date().toISOString() })
        .eq('id', addressId)
        .eq('user_id', userId);

      if (error) {
        console.error('❌ Error setting default address:', error);
        throw new Error(`Erreur lors de la définition par défaut: ${error.message}`);
      }

      // Invalider le cache
      await this.invalidateCache(userId);
      
      console.log('✅ Default address set successfully:', addressId);

    } catch (error) {
      console.error('❌ Error in setDefaultAddress:', error);
      throw error;
    }
  }

  /**
   * Récupère l'adresse par défaut d'un utilisateur
   */
  async getDefaultAddress(userId: string): Promise<UserAddress | null> {
    try {
      const addresses = await this.getUserAddresses(userId);
      return addresses.find(addr => addr.is_default) || null;
    } catch (error) {
      console.error('❌ Error getting default address:', error);
      return null;
    }
  }

  /**
   * Transforme les données de la base en format UserAddress
   */
  private transformDatabaseAddress(dbAddress: any): UserAddress {
    return {
      id: dbAddress.id,
      user_id: dbAddress.user_id,
      label: dbAddress.label,
      address: dbAddress.address_line_1 || dbAddress.address || '',
      details: dbAddress.address_line_2 || dbAddress.landmark || dbAddress.details || '',
      coordinates: {
        latitude: dbAddress.latitude || 0,
        longitude: dbAddress.longitude || 0,
      },
      address_type: dbAddress.address_type || 'other',
      is_default: dbAddress.is_default,
      created_at: dbAddress.created_at,
      updated_at: dbAddress.updated_at,
    };
  }

  /**
   * Cache les adresses en local
   */
  private async cacheAddresses(userId: string, addresses: UserAddress[]): Promise<void> {
    try {
      const cacheData = {
        addresses,
        timestamp: Date.now(),
        userId,
      };
      await AsyncStorage.setItem(`${ADDRESSES_CACHE_KEY}_${userId}`, JSON.stringify(cacheData));
    } catch (error) {
      console.warn('⚠️ Failed to cache addresses:', error);
    }
  }

  /**
   * Récupère les adresses depuis le cache
   */
  private async getCachedAddresses(userId: string): Promise<UserAddress[]> {
    try {
      const cached = await AsyncStorage.getItem(`${ADDRESSES_CACHE_KEY}_${userId}`);
      if (!cached) return []; // Retourner tableau vide au lieu de mock

      const cacheData = JSON.parse(cached);
      const isExpired = Date.now() - cacheData.timestamp > CACHE_DURATION;

      if (isExpired || cacheData.userId !== userId) {
        await AsyncStorage.removeItem(`${ADDRESSES_CACHE_KEY}_${userId}`);
        return []; // Retourner tableau vide au lieu de mock
      }

      console.log(`📍 Using cached addresses for user ${userId}`);
      return cacheData.addresses || [];
    } catch (error) {
      console.warn('⚠️ Failed to get cached addresses:', error);
      return []; // Retourner tableau vide au lieu de mock
    }
  }

  /**
   * Invalide le cache des adresses
   */
  private async invalidateCache(userId: string): Promise<void> {
    try {
      await AsyncStorage.removeItem(`${ADDRESSES_CACHE_KEY}_${userId}`);
    } catch (error) {
      console.warn('⚠️ Failed to invalidate addresses cache:', error);
    }
  }

  /**
   * Extrait les coordonnées depuis le format PostGIS
   */
  private extractCoordinates(coordinates: any): { latitude: number; longitude: number } | undefined {
    if (!coordinates) return undefined;

    try {
      // Si c'est déjà un objet avec lat/lng
      if (coordinates.latitude && coordinates.longitude) {
        return coordinates;
      }

      // Si c'est un format PostGIS POINT
      if (typeof coordinates === 'string') {
        const match = coordinates.match(/POINT\(([^)]+)\)/);
        if (match) {
          const [lng, lat] = match[1].split(' ').map(Number);
          return { latitude: lat, longitude: lng };
        }
      }

      return undefined;
    } catch (error) {
      console.warn('⚠️ Error extracting coordinates:', error);
      return undefined;
    }
  }



  /**
   * Données mock pour le développement
   */
  private getMockAddresses(userId: string): UserAddress[] {
    return [
      {
        id: 'mock-1',
        user_id: userId,
        label: 'Domicile',
        address: 'Cocody, Riviera Golf, Abidjan',
        details: 'Résidence Les Palmiers, Villa 15',
        coordinates: { latitude: 5.3600, longitude: -4.0083 },
        address_type: 'home',
        is_default: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
      {
        id: 'mock-2',
        user_id: userId,
        label: 'Bureau',
        address: 'Plateau, Abidjan',
        details: 'Immeuble SCIAM, 8ème étage',
        coordinates: { latitude: 5.3197, longitude: -4.0267 },
        address_type: 'work',
        is_default: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
    ];
  }
}

export const userAddressService = new UserAddressService();
