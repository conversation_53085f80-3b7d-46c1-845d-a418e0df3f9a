{"version": 3, "file": "ExpoUpdatesModule.types.d.ts", "sourceRoot": "", "sources": ["../src/ExpoUpdatesModule.types.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,mBAAmB,CAAC;AAEjD,OAAO,EACL,QAAQ,EACR,0BAA0B,EAC1B,6BAA6B,EAC7B,yBAAyB,EACzB,mCAAmC,EACnC,wBAAwB,EACxB,wBAAwB,EACxB,eAAe,EACf,gCAAgC,EACjC,MAAM,iBAAiB,CAAC;AAEzB,MAAM,MAAM,aAAa,GAAG;IAC1B,oCAAoC,EAAE,CAAC,MAAM,EAAE,GAAG,KAAK,IAAI,CAAC;CAC7D,CAAC;AAEF,MAAM,MAAM,oCAAoC,GAC5C,QAAQ,GACR,qBAAqB,GACrB,OAAO,GACP,WAAW,CAAC;AAEhB;;;;GAIG;AACH,MAAM,WAAW,sBAAsB;IACrC,iBAAiB,EAAE,OAAO,CAAC;IAC3B,qBAAqB,EAAE,MAAM,GAAG,IAAI,CAAC;IACrC,cAAc,EAAE,MAAM,GAAG,IAAI,CAAC;IAC9B,gBAAgB,EAAE,OAAO,CAAC;IAC1B,SAAS,EAAE,OAAO,CAAC;IACnB,qBAAqB,CAAC,EAAE,OAAO,CAAC;IAChC;;OAEG;IACH,cAAc,EAAE,MAAM,CAAC;IACvB,kBAAkB,EAAE,oCAAoC,CAAC;IACzD;;OAEG;IACH,OAAO,EAAE,MAAM,CAAC;IAChB,wDAAwD,EAAE,OAAO,CAAC;IAClE,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB;;OAEG;IACH,cAAc,CAAC,EAAE,MAAM,CAAC;IACxB;;OAEG;IACH,QAAQ,CAAC,EAAE,QAAQ,CAAC;IACpB,WAAW,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IAErC,cAAc,EAAE,gCAAgC,GAAG;QACjD,oBAAoB,CAAC,EAAE,MAAM,CAAC;QAC9B,wBAAwB,CAAC,EAAE,MAAM,CAAC;QAClC,4BAA4B,CAAC,EAAE,MAAM,CAAC;QACtC,cAAc,CAAC,EAAE,MAAM,CAAC;KACzB,CAAC;IAEF,MAAM,EAAE,MAAM,OAAO,CAAC,IAAI,CAAC,CAAC;IAC5B,mBAAmB,EAAE,MAAM,OAAO,CAC9B,yBAAyB,GACzB,CAAC,IAAI,CAAC,0BAA0B,EAAE,UAAU,CAAC,GAC3C,CAAC;QAAE,cAAc,EAAE,MAAM,CAAA;KAAE,GAAG;QAAE,QAAQ,EAAE,QAAQ,CAAA;KAAE,CAAC,CAAC,GACxD,6BAA6B,CAChC,CAAC;IACF,mBAAmB,EAAE,MAAM,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC;IAC3D,kBAAkB,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,GAAG,IAAI,KAAK,OAAO,CAAC,IAAI,CAAC,CAAC;IACzE,mBAAmB,EAAE,CAAC,MAAM,EAAE,MAAM,KAAK,OAAO,CAAC,eAAe,EAAE,CAAC,CAAC;IACpE,oBAAoB,EAAE,MAAM,OAAO,CAAC,IAAI,CAAC,CAAC;IAC1C,gBAAgB,EAAE,MAAM,OAAO,CAC3B,CAAC,IAAI,CAAC,wBAAwB,EAAE,UAAU,CAAC,GACzC,CAAC;QAAE,cAAc,EAAE,MAAM,CAAA;KAAE,GAAG;QAAE,QAAQ,EAAE,QAAQ,CAAA;KAAE,CAAC,CAAC,GACxD,wBAAwB,GACxB,mCAAmC,CACtC,CAAC;CACH;AAED;;GAEG;AACH,MAAM,CAAC,OAAO,OAAO,iBACnB,SAAQ,YAAY,CAAC,aAAa,CAClC,YAAW,sBAAsB;IAEjC,iBAAiB,EAAE,OAAO,CAAC;IAC3B,qBAAqB,EAAE,MAAM,GAAG,IAAI,CAAC;IACrC,cAAc,EAAE,MAAM,GAAG,IAAI,CAAC;IAC9B,gBAAgB,EAAE,OAAO,CAAC;IAC1B,SAAS,EAAE,OAAO,CAAC;IACnB,qBAAqB,CAAC,EAAE,OAAO,CAAC;IAChC,cAAc,EAAE,MAAM,CAAC;IACvB,kBAAkB,EAAE,oCAAoC,CAAC;IACzD,OAAO,EAAE,MAAM,CAAC;IAChB,wDAAwD,EAAE,OAAO,CAAC;IAClE,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,cAAc,CAAC,EAAE,MAAM,CAAC;IACxB,QAAQ,CAAC,EAAE,QAAQ,CAAC;IACpB,WAAW,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IAErC,cAAc,EAAE,gCAAgC,GAAG;QACjD,oBAAoB,CAAC,EAAE,MAAM,CAAC;QAC9B,wBAAwB,CAAC,EAAE,MAAM,CAAC;QAClC,4BAA4B,CAAC,EAAE,MAAM,CAAC;QACtC,cAAc,CAAC,EAAE,MAAM,CAAC;KACzB,CAAC;IAEF,MAAM,EAAE,MAAM,OAAO,CAAC,IAAI,CAAC,CAAC;IAC5B,mBAAmB,EAAE,MAAM,OAAO,CAC9B,yBAAyB,GACzB,CAAC,IAAI,CAAC,0BAA0B,EAAE,UAAU,CAAC,GAC3C,CAAC;QAAE,cAAc,EAAE,MAAM,CAAA;KAAE,GAAG;QAAE,QAAQ,EAAE,QAAQ,CAAA;KAAE,CAAC,CAAC,GACxD,6BAA6B,CAChC,CAAC;IACF,mBAAmB,EAAE,MAAM,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC;IAC3D,kBAAkB,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,GAAG,IAAI,KAAK,OAAO,CAAC,IAAI,CAAC,CAAC;IACzE,mBAAmB,EAAE,CAAC,MAAM,EAAE,MAAM,KAAK,OAAO,CAAC,eAAe,EAAE,CAAC,CAAC;IACpE,oBAAoB,EAAE,MAAM,OAAO,CAAC,IAAI,CAAC,CAAC;IAC1C,gBAAgB,EAAE,MAAM,OAAO,CAC3B,CAAC,IAAI,CAAC,wBAAwB,EAAE,UAAU,CAAC,GACzC,CAAC;QAAE,cAAc,EAAE,MAAM,CAAA;KAAE,GAAG;QAAE,QAAQ,EAAE,QAAQ,CAAA;KAAE,CAAC,CAAC,GACxD,wBAAwB,GACxB,mCAAmC,CACtC,CAAC;CACH"}