<p>
  <a href="https://docs.expo.dev/versions/latest/sdk/updates/">
    <img
      src="../../.github/resources/expo-updates.svg"
      alt="expo-updates"
      height="64" />
  </a>
</p>

The `expo-updates` module enables your app to manage remote updates to your application code.

This module works with a server that implements the [Expo Update protocol](https://docs.expo.dev/technical-specs/expo-updates-1/).

The [EAS Update](https://docs.expo.dev/eas-update/introduction/) hosted service implements this protocol.

To build a custom server that implements the protocol, see the example server source code [here](https://github.com/expo/custom-expo-updates-server).

## Important documentation links

- [Getting started](https://docs.expo.dev/eas-update/getting-started/)
- [How EAS Update works](https://docs.expo.dev/eas-update/how-it-works/)
- [Debugging](https://docs.expo.dev/eas-update/debug/)
- [Migration from Classic Updates](https://docs.expo.dev/eas-update/migrate-from-classic-updates/)
- [Updates JS API](https://docs.expo.dev/versions/latest/sdk/updates/)

# Installation in bare React Native projects

Learn how to install expo-updates in your project in the [Installing expo-updates documentation page](https://docs.expo.dev/bare/installing-updates/).
