#!/usr/bin/env node

/**
 * Script de test pour vérifier la configuration Google Maps
 * Usage: node scripts/test-google-maps.cjs
 */

const fs = require('fs');
const path = require('path');
const https = require('https');

// Configuration
const CONFIG = {
  API_KEY: 'AIzaSyCUSlG6L03l-nE5SH9Rm8sHQLZRKuRhD3s',
  TEST_COORDINATES: {
    latitude: 5.3364, // Abidjan
    longitude: -4.0267
  }
};

console.log('🔍 Test de Configuration Google Maps pour Mientior Livraison\n');

// 1. Vérifier la présence de l'API Key dans AndroidManifest.xml
function checkAndroidManifest() {
  console.log('📱 Vérification AndroidManifest.xml...');
  
  const manifestPath = path.join(__dirname, '../android/app/src/main/AndroidManifest.xml');
  
  if (!fs.existsSync(manifestPath)) {
    console.log('❌ AndroidManifest.xml non trouvé');
    return false;
  }
  
  const manifestContent = fs.readFileSync(manifestPath, 'utf8');
  
  if (manifestContent.includes('com.google.android.geo.API_KEY')) {
    console.log('✅ Clé API Google Maps trouvée dans AndroidManifest.xml');
    
    if (manifestContent.includes(CONFIG.API_KEY)) {
      console.log('✅ Clé API correspond à celle du .env');
    } else {
      console.log('⚠️  Clé API différente de celle du .env');
    }
    return true;
  } else {
    console.log('❌ Clé API Google Maps manquante dans AndroidManifest.xml');
    return false;
  }
}

// 2. Vérifier le fichier .env
function checkEnvFile() {
  console.log('\n📄 Vérification fichier .env...');
  
  const envPath = path.join(__dirname, '../.env');
  
  if (!fs.existsSync(envPath)) {
    console.log('❌ Fichier .env non trouvé');
    return false;
  }
  
  const envContent = fs.readFileSync(envPath, 'utf8');
  
  if (envContent.includes('GOOGLE_MAPS_API_KEY')) {
    console.log('✅ GOOGLE_MAPS_API_KEY trouvée dans .env');
    return true;
  } else {
    console.log('❌ GOOGLE_MAPS_API_KEY manquante dans .env');
    return false;
  }
}

// 3. Tester l'API Google Maps
function testGoogleMapsAPI() {
  return new Promise((resolve) => {
    console.log('\n🌐 Test de l\'API Google Maps...');
    
    const url = `https://maps.googleapis.com/maps/api/geocode/json?latlng=${CONFIG.TEST_COORDINATES.latitude},${CONFIG.TEST_COORDINATES.longitude}&key=${CONFIG.API_KEY}`;
    
    https.get(url, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const response = JSON.parse(data);
          
          if (response.status === 'OK') {
            console.log('✅ API Google Maps fonctionne correctement');
            console.log(`📍 Adresse trouvée: ${response.results[0]?.formatted_address || 'N/A'}`);
            resolve(true);
          } else {
            console.log(`❌ Erreur API Google Maps: ${response.status}`);
            console.log(`💡 Message: ${response.error_message || 'Aucun message d\'erreur'}`);
            resolve(false);
          }
        } catch (error) {
          console.log('❌ Erreur parsing réponse API:', error.message);
          resolve(false);
        }
      });
    }).on('error', (error) => {
      console.log('❌ Erreur réseau:', error.message);
      resolve(false);
    });
  });
}

// 4. Vérifier les permissions Android
function checkAndroidPermissions() {
  console.log('\n🔐 Vérification permissions Android...');
  
  const manifestPath = path.join(__dirname, '../android/app/src/main/AndroidManifest.xml');
  
  if (!fs.existsSync(manifestPath)) {
    console.log('❌ AndroidManifest.xml non trouvé');
    return false;
  }
  
  const manifestContent = fs.readFileSync(manifestPath, 'utf8');
  const requiredPermissions = [
    'ACCESS_FINE_LOCATION',
    'ACCESS_COARSE_LOCATION',
    'INTERNET'
  ];
  
  let allPermissionsFound = true;
  
  requiredPermissions.forEach(permission => {
    if (manifestContent.includes(permission)) {
      console.log(`✅ Permission ${permission} trouvée`);
    } else {
      console.log(`❌ Permission ${permission} manquante`);
      allPermissionsFound = false;
    }
  });
  
  return allPermissionsFound;
}

// 5. Générer un rapport de diagnostic
function generateDiagnosticReport(results) {
  console.log('\n📊 RAPPORT DE DIAGNOSTIC\n');
  console.log('='.repeat(50));
  
  const issues = [];
  const recommendations = [];
  
  if (!results.androidManifest) {
    issues.push('❌ Configuration AndroidManifest.xml');
    recommendations.push('• Ajouter la clé API Google Maps dans AndroidManifest.xml');
  }
  
  if (!results.envFile) {
    issues.push('❌ Configuration fichier .env');
    recommendations.push('• Ajouter GOOGLE_MAPS_API_KEY dans le fichier .env');
  }
  
  if (!results.apiTest) {
    issues.push('❌ API Google Maps non fonctionnelle');
    recommendations.push('• Vérifier que l\'API key est valide');
    recommendations.push('• Activer les APIs nécessaires dans Google Cloud Console');
    recommendations.push('• Vérifier les restrictions de l\'API key');
  }
  
  if (!results.permissions) {
    issues.push('❌ Permissions Android manquantes');
    recommendations.push('• Ajouter les permissions de géolocalisation');
  }
  
  if (issues.length === 0) {
    console.log('🎉 CONFIGURATION PARFAITE !');
    console.log('✅ Tous les tests sont passés avec succès');
    console.log('✅ Google Maps devrait fonctionner correctement');
  } else {
    console.log('⚠️  PROBLÈMES DÉTECTÉS :');
    issues.forEach(issue => console.log(`   ${issue}`));
    
    console.log('\n💡 RECOMMANDATIONS :');
    recommendations.forEach(rec => console.log(`   ${rec}`));
  }
  
  console.log('\n' + '='.repeat(50));
}

// Fonction principale
async function main() {
  const results = {
    androidManifest: checkAndroidManifest(),
    envFile: checkEnvFile(),
    permissions: checkAndroidPermissions(),
    apiTest: await testGoogleMapsAPI()
  };
  
  generateDiagnosticReport(results);
}

// Exécution
main().catch(console.error);
