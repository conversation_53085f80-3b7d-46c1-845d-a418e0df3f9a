{"logs": [{"outputFile": "com.eliseedev.mientiorlivraison.app-mergeDebugResources-71:/values-si/values-si.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\1b1d6e3a1eafdeb637916b2c04d0a37e\\transformed\\exoplayer-core-2.18.1\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,131,191,254,320,396,465,554,640", "endColumns": "75,59,62,65,75,68,88,85,69", "endOffsets": "126,186,249,315,391,460,549,635,705"}, "to": {"startLines": "112,113,114,115,116,117,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "9682,9758,9818,9881,9947,10023,10092,10181,10267", "endColumns": "75,59,62,65,75,68,88,85,69", "endOffsets": "9753,9813,9876,9942,10018,10087,10176,10262,10332"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f8296c347bb3d1ec471e3402800c1db5\\transformed\\material-1.12.0\\res\\values-si\\values-si.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,269,345,422,500,591,676,778,893,976,1037,1101,1190,1257,1317,1411,1475,1538,1594,1664,1731,1786,1905,1962,2026,2080,2153,2275,2358,2441,2534,2620,2705,2837,2915,2995,3117,3203,3287,3347,3399,3465,3535,3608,3679,3756,3828,3905,3977,4047,4160,4253,4326,4416,4509,4583,4655,4746,4800,4880,4946,5030,5115,5177,5241,5304,5370,5475,5580,5675,5776,5840,5896,5976,6061,6136", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,75,76,77,90,84,101,114,82,60,63,88,66,59,93,63,62,55,69,66,54,118,56,63,53,72,121,82,82,92,85,84,131,77,79,121,85,83,59,51,65,69,72,70,76,71,76,71,69,112,92,72,89,92,73,71,90,53,79,65,83,84,61,63,62,65,104,104,94,100,63,55,79,84,74,75", "endOffsets": "264,340,417,495,586,671,773,888,971,1032,1096,1185,1252,1312,1406,1470,1533,1589,1659,1726,1781,1900,1957,2021,2075,2148,2270,2353,2436,2529,2615,2700,2832,2910,2990,3112,3198,3282,3342,3394,3460,3530,3603,3674,3751,3823,3900,3972,4042,4155,4248,4321,4411,4504,4578,4650,4741,4795,4875,4941,5025,5110,5172,5236,5299,5365,5470,5575,5670,5771,5835,5891,5971,6056,6131,6207"}, "to": {"startLines": "19,51,52,53,54,55,63,64,65,86,87,139,144,147,149,150,151,152,153,154,155,156,157,158,159,160,161,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,216,217,218", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "761,3715,3791,3868,3946,4037,4854,4956,5071,7534,7595,11437,11927,12145,12274,12368,12432,12495,12551,12621,12688,12743,12862,12919,12983,13037,13110,13455,13538,13621,13714,13800,13885,14017,14095,14175,14297,14383,14467,14527,14579,14645,14715,14788,14859,14936,15008,15085,15157,15227,15340,15433,15506,15596,15689,15763,15835,15926,15980,16060,16126,16210,16295,16357,16421,16484,16550,16655,16760,16855,16956,17020,17076,17556,17641,17716", "endLines": "22,51,52,53,54,55,63,64,65,86,87,139,144,147,149,150,151,152,153,154,155,156,157,158,159,160,161,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,216,217,218", "endColumns": "12,75,76,77,90,84,101,114,82,60,63,88,66,59,93,63,62,55,69,66,54,118,56,63,53,72,121,82,82,92,85,84,131,77,79,121,85,83,59,51,65,69,72,70,76,71,76,71,69,112,92,72,89,92,73,71,90,53,79,65,83,84,61,63,62,65,104,104,94,100,63,55,79,84,74,75", "endOffsets": "925,3786,3863,3941,4032,4117,4951,5066,5149,7590,7654,11521,11989,12200,12363,12427,12490,12546,12616,12683,12738,12857,12914,12978,13032,13105,13227,13533,13616,13709,13795,13880,14012,14090,14170,14292,14378,14462,14522,14574,14640,14710,14783,14854,14931,15003,15080,15152,15222,15335,15428,15501,15591,15684,15758,15830,15921,15975,16055,16121,16205,16290,16352,16416,16479,16545,16650,16755,16850,16951,17015,17071,17151,17636,17711,17787"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\306a6bcd4bd045cfd61a3c5eb43578e4\\transformed\\core-1.13.1\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,260,365,470,569,673,787", "endColumns": "101,102,104,104,98,103,113,100", "endOffsets": "152,255,360,465,564,668,782,883"}, "to": {"startLines": "56,57,58,59,60,61,62,227", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4122,4224,4327,4432,4537,4636,4740,18457", "endColumns": "101,102,104,104,98,103,113,100", "endOffsets": "4219,4322,4427,4532,4631,4735,4849,18553"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ff199f796fece94789ea9bd928f0e62a\\transformed\\browser-1.6.0\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,163,270,386", "endColumns": "107,106,115,104", "endOffsets": "158,265,381,486"}, "to": {"startLines": "85,140,141,142", "startColumns": "4,4,4,4", "startOffsets": "7426,11526,11633,11749", "endColumns": "107,106,115,104", "endOffsets": "7529,11628,11744,11849"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4407803e7a2d8d3f17c090093cb28e53\\transformed\\exoplayer-ui-2.18.1\\res\\values-si\\values-si.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,488,666,750,833,914,1007,1099,1162,1224,1313,1404,1475,1545,1606,1672,1811,1953,2090,2161,2240,2310,2375,2465,2554,2621,2689,2742,2800,2847,2908,2968,3035,3096,3161,3220,3285,3354,3417,3484,3538,3595,3666,3737", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,83,82,80,92,91,62,61,88,90,70,69,60,65,138,141,136,70,78,69,64,89,88,66,67,52,57,46,60,59,66,60,64,58,64,68,62,66,53,56,70,70,51", "endOffsets": "282,483,661,745,828,909,1002,1094,1157,1219,1308,1399,1470,1540,1601,1667,1806,1948,2085,2156,2235,2305,2370,2460,2549,2616,2684,2737,2795,2842,2903,2963,3030,3091,3156,3215,3280,3349,3412,3479,3533,3590,3661,3732,3784"}, "to": {"startLines": "2,11,15,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,382,583,7659,7743,7826,7907,8000,8092,8155,8217,8306,8397,8468,8538,8599,8665,8804,8946,9083,9154,9233,9303,9368,9458,9547,9614,10337,10390,10448,10495,10556,10616,10683,10744,10809,10868,10933,11002,11065,11132,11186,11243,11314,11385", "endLines": "10,14,18,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "endColumns": "17,12,12,83,82,80,92,91,62,61,88,90,70,69,60,65,138,141,136,70,78,69,64,89,88,66,67,52,57,46,60,59,66,60,64,58,64,68,62,66,53,56,70,70,51", "endOffsets": "377,578,756,7738,7821,7902,7995,8087,8150,8212,8301,8392,8463,8533,8594,8660,8799,8941,9078,9149,9228,9298,9363,9453,9542,9609,9677,10385,10443,10490,10551,10611,10678,10739,10804,10863,10928,10997,11060,11127,11181,11238,11309,11380,11432"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\59ddc64970c204fac172d4da5009756f\\transformed\\react-android-0.79.2-debug\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,206,279,347,430,499,567,643,722,805,891,960,1040,1129,1209,1292,1377,1456,1533,1613,1705,1778,1857,1929", "endColumns": "68,81,72,67,82,68,67,75,78,82,85,68,79,88,79,82,84,78,76,79,91,72,78,71,77", "endOffsets": "119,201,274,342,425,494,562,638,717,800,886,955,1035,1124,1204,1287,1372,1451,1528,1608,1700,1773,1852,1924,2002"}, "to": {"startLines": "50,66,143,145,146,148,162,163,164,211,212,213,214,219,220,221,222,223,224,225,226,228,229,230,231", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3646,5154,11854,11994,12062,12205,13232,13300,13376,17156,17239,17325,17394,17792,17881,17961,18044,18129,18208,18285,18365,18558,18631,18710,18782", "endColumns": "68,81,72,67,82,68,67,75,78,82,85,68,79,88,79,82,84,78,76,79,91,72,78,71,77", "endOffsets": "3710,5231,11922,12057,12140,12269,13295,13371,13450,17234,17320,17389,17469,17876,17956,18039,18124,18203,18280,18360,18452,18626,18705,18777,18855"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2ab0477328e0ae05829c4cc214e49234\\transformed\\play-services-base-18.2.0\\res\\values-si\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,447,567,676,814,934,1046,1140,1287,1398,1550,1677,1817,1974,2043,2100", "endColumns": "103,149,119,108,137,119,111,93,146,110,151,126,139,156,68,56,75", "endOffsets": "296,446,566,675,813,933,1045,1139,1286,1397,1549,1676,1816,1973,2042,2099,2175"}, "to": {"startLines": "67,68,69,70,71,72,73,74,76,77,78,79,80,81,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5236,5344,5498,5622,5735,5877,6001,6117,6354,6505,6620,6776,6907,7051,7212,7285,7346", "endColumns": "107,153,123,112,141,123,115,97,150,114,155,130,143,160,72,60,79", "endOffsets": "5339,5493,5617,5730,5872,5996,6112,6210,6500,6615,6771,6902,7046,7207,7280,7341,7421"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cea641498eb0b29f919daa30d9671bd0\\transformed\\play-services-basement-18.3.0\\res\\values-si\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "134", "endOffsets": "329"}, "to": {"startLines": "75", "startColumns": "4", "startOffsets": "6215", "endColumns": "138", "endOffsets": "6349"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8fb4e20b948a58ead7b50b7ad3e15312\\transformed\\appcompat-1.7.0\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,221,328,435,518,623,739,829,915,1006,1099,1193,1287,1387,1480,1575,1669,1760,1851,1935,2044,2148,2246,2356,2456,2563,2722,2821", "endColumns": "115,106,106,82,104,115,89,85,90,92,93,93,99,92,94,93,90,90,83,108,103,97,109,99,106,158,98,81", "endOffsets": "216,323,430,513,618,734,824,910,1001,1094,1188,1282,1382,1475,1570,1664,1755,1846,1930,2039,2143,2241,2351,2451,2558,2717,2816,2898"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,215", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "930,1046,1153,1260,1343,1448,1564,1654,1740,1831,1924,2018,2112,2212,2305,2400,2494,2585,2676,2760,2869,2973,3071,3181,3281,3388,3547,17474", "endColumns": "115,106,106,82,104,115,89,85,90,92,93,93,99,92,94,93,90,90,83,108,103,97,109,99,106,158,98,81", "endOffsets": "1041,1148,1255,1338,1443,1559,1649,1735,1826,1919,2013,2107,2207,2300,2395,2489,2580,2671,2755,2864,2968,3066,3176,3276,3383,3542,3641,17551"}}]}]}