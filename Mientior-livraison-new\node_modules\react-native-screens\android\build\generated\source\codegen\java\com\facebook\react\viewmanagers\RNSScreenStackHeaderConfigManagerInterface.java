/**
* This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
*
* Do not edit this file as changes may cause incorrect behavior and will be lost
* once the code is regenerated.
*
* @generated by codegen project: GeneratePropsJavaInterface.js
*/

package com.facebook.react.viewmanagers;

import android.view.View;
import androidx.annotation.Nullable;
import com.facebook.react.uimanager.ViewManagerWithGeneratedInterface;

public interface RNSScreenStackHeaderConfigManagerInterface<T extends View> extends ViewManagerWithGeneratedInterface {
  void setBackgroundColor(T view, @Nullable Integer value);
  void setBackTitle(T view, @Nullable String value);
  void setBackTitleFontFamily(T view, @Nullable String value);
  void setBackTitleFontSize(T view, int value);
  void setBackTitleVisible(T view, boolean value);
  void setColor(T view, @Nullable Integer value);
  void setDirection(T view, @Nullable String value);
  void setHidden(T view, boolean value);
  void setHideShadow(T view, boolean value);
  void setLargeTitle(T view, boolean value);
  void setLargeTitleFontFamily(T view, @Nullable String value);
  void setLargeTitleFontSize(T view, int value);
  void setLargeTitleFontWeight(T view, @Nullable String value);
  void setLargeTitleBackgroundColor(T view, @Nullable Integer value);
  void setLargeTitleHideShadow(T view, boolean value);
  void setLargeTitleColor(T view, @Nullable Integer value);
  void setTranslucent(T view, boolean value);
  void setTitle(T view, @Nullable String value);
  void setTitleFontFamily(T view, @Nullable String value);
  void setTitleFontSize(T view, int value);
  void setTitleFontWeight(T view, @Nullable String value);
  void setTitleColor(T view, @Nullable Integer value);
  void setDisableBackButtonMenu(T view, boolean value);
  void setBackButtonDisplayMode(T view, @Nullable String value);
  void setHideBackButton(T view, boolean value);
  void setBackButtonInCustomView(T view, boolean value);
  void setBlurEffect(T view, @Nullable String value);
  void setTopInsetEnabled(T view, boolean value);
}
