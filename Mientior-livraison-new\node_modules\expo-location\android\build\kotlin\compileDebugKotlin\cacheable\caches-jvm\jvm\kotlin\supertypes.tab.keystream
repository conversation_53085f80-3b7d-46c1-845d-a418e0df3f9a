2expo.modules.location.NoPermissionsModuleException5expo.modules.location.NoPermissionInManifestException=expo.modules.location.LocationBackgroundUnauthorizedException6expo.modules.location.LocationRequestRejectedException;expo.modules.location.CurrentLocationIsUnavailableException7expo.modules.location.LocationRequestCancelledException:expo.modules.location.LocationSettingsUnsatisfiedException3expo.modules.location.LocationUnauthorizedException2expo.modules.location.LocationUnavailableException.expo.modules.location.LocationUnknownException.expo.modules.location.SensorManagerUnavailable&expo.modules.location.GeocodeException(expo.modules.location.NoGeocodeException2expo.modules.location.TaskManagerNotFoundException)expo.modules.location.GeofencingException/expo.modules.location.MissingUIManagerException)expo.modules.location.ConversionException?expo.modules.location.ForegroundServiceStartNotAllowedException;expo.modules.location.ForegroundServicePermissionsException$expo.modules.location.LocationModule&expo.modules.location.LocationAccuracy3expo.modules.location.records.GeofencingRegionState6expo.modules.location.records.LocationLastKnownOptions-expo.modules.location.records.LocationOptions4expo.modules.location.records.ReverseGeocodeLocation1expo.modules.location.records.LocationTaskOptions8expo.modules.location.records.LocationTaskServiceOptions/expo.modules.location.records.GeofencingOptions$expo.modules.location.records.Region7expo.modules.location.records.PermissionRequestResponse><EMAIL>:expo.modules.location.taskConsumers.GeofencingTaskConsumer8expo.modules.location.taskConsumers.LocationTaskConsumer                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     