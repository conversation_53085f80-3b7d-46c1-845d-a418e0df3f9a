// Generated by view binder compiler. Do not edit!
package expo.modules.devlauncher.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewpager.widget.ViewPager;
import expo.modules.devlauncher.R;
import java.lang.NullPointerException;
import java.lang.Override;

public final class ErrorActivityContentViewBinding implements ViewBinding {
  @NonNull
  private final ViewPager rootView;

  @NonNull
  public final ViewPager errorViewPager;

  private ErrorActivityContentViewBinding(@NonNull ViewPager rootView,
      @NonNull ViewPager errorViewPager) {
    this.rootView = rootView;
    this.errorViewPager = errorViewPager;
  }

  @Override
  @NonNull
  public ViewPager getRoot() {
    return rootView;
  }

  @NonNull
  public static ErrorActivityContentViewBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ErrorActivityContentViewBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.error_activity_content_view, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ErrorActivityContentViewBinding bind(@NonNull View rootView) {
    if (rootView == null) {
      throw new NullPointerException("rootView");
    }

    ViewPager errorViewPager = (ViewPager) rootView;

    return new ErrorActivityContentViewBinding((ViewPager) rootView, errorViewPager);
  }
}
