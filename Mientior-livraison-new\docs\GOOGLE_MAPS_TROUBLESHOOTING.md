# 🗺️ Guide de Résolution - Carte Blanche Google Maps

## 🔍 PROBLÈME IDENTIFIÉ
La carte Google Maps s'affiche complètement blanche dans l'application Mientior Livraison.

## ✅ DIAGNOSTIC EFFECTUÉ
- ✅ API Key Google Maps configurée dans AndroidManifest.xml
- ✅ API Key présente dans le fichier .env
- ✅ Permissions Android correctement configurées
- ❌ Test API réseau échoué (problème de connectivité)

## 🛠️ SOLUTIONS ÉTAPE PAR ÉTAPE

### 1. **Vérification Google Cloud Console**

#### A. Activer les APIs nécessaires
```bash
# APIs requises dans Google Cloud Console :
- Maps SDK for Android
- Maps SDK for iOS  
- Places API
- Directions API
- Geocoding API
```

#### B. Configurer les restrictions d'API Key
```bash
# Dans Google Cloud Console > APIs & Services > Credentials
1. Sélectionner votre API Key
2. Application restrictions : Android apps
3. Ajouter le nom du package : com.livraison.afrique
4. Ajouter l'empreinte SHA-1 du certificat de débogage
```

### 2. **Génération de l'Empreinte SHA-1**

#### Pour Windows :
```powershell
# Certificat de débogage par défaut
keytool -list -v -keystore "%USERPROFILE%\.android\debug.keystore" -alias androiddebugkey -storepass android -keypass android

# Ou certificat du projet
keytool -list -v -keystore "android/app/debug.keystore" -alias androiddebugkey -storepass android -keypass android
```

#### Pour macOS/Linux :
```bash
# Certificat de débogage par défaut
keytool -list -v -keystore ~/.android/debug.keystore -alias androiddebugkey -storepass android -keypass android

# Ou certificat du projet
keytool -list -v -keystore android/app/debug.keystore -alias androiddebugkey -storepass android -keypass android
```

### 3. **Vérification de la Configuration Android**

#### A. AndroidManifest.xml
```xml
<!-- Vérifier que cette ligne est présente -->
<meta-data 
    android:name="com.google.android.geo.API_KEY" 
    android:value="AIzaSyCUSlG6L03l-nE5SH9Rm8sHQLZRKuRhD3s"/>

<!-- Permissions requises -->
<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION"/>
<uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION"/>
<uses-permission android:name="android.permission.INTERNET"/>
```

#### B. build.gradle (Module: app)
```gradle
// Vérifier le nom du package
android {
    defaultConfig {
        applicationId "com.livraison.afrique"
        // ...
    }
}
```

### 4. **Solutions de Contournement**

#### A. MapView Simplifié (Implémenté)
```typescript
<MapView
  provider={PROVIDER_GOOGLE}
  style={styles.map}
  initialRegion={{
    latitude: 5.3364, // Abidjan
    longitude: -4.0267,
    latitudeDelta: 0.0922,
    longitudeDelta: 0.0421,
  }}
  showsUserLocation={true}
  loadingEnabled={true}
  onMapReady={() => console.log('Map ready')}
/>
```

#### B. Fallback vers Mapbox
```typescript
// En cas d'échec Google Maps
import MapboxGL from '@rnmapbox/maps';

// Configuration Mapbox
MapboxGL.setAccessToken('pk.eyJ1IjoibWFwYm94IiwiYSI6ImNpejY4NXVycTA2emYycXBndHRqcmZ3N3gifQ.rJcFIG214AriISLbB6B5aw');
```

### 5. **Tests de Validation**

#### A. Test API Google Maps
```bash
# Exécuter le script de diagnostic
node scripts/test-google-maps.cjs
```

#### B. Test en Mode Debug
```bash
# Lancer l'app en mode debug
npx expo run:android --variant debug
```

#### C. Vérification des Logs
```bash
# Surveiller les logs Android
npx expo run:android --variant debug --no-build-cache
```

### 6. **Configuration Alternative - Expo Maps**

Si Google Maps continue de poser problème, utiliser Expo Maps :

```typescript
import { MapView } from 'expo-maps';

<MapView
  style={styles.map}
  initialRegion={{
    latitude: 5.3364,
    longitude: -4.0267,
    latitudeDelta: 0.0922,
    longitudeDelta: 0.0421,
  }}
  provider="google"
/>
```

### 7. **Checklist de Résolution**

- [ ] API Key valide dans Google Cloud Console
- [ ] APIs activées (Maps SDK, Places, Directions, Geocoding)
- [ ] Restrictions d'API Key configurées
- [ ] Empreinte SHA-1 ajoutée
- [ ] Nom du package correct
- [ ] Permissions Android accordées
- [ ] Test réseau réussi
- [ ] MapView simplifié testé
- [ ] Logs vérifiés

### 8. **Contacts et Support**

#### Google Cloud Support
- Console : https://console.cloud.google.com
- Documentation : https://developers.google.com/maps/documentation

#### React Native Maps
- GitHub : https://github.com/react-native-maps/react-native-maps
- Documentation : https://github.com/react-native-maps/react-native-maps/blob/master/docs/installation.md

### 9. **Prochaines Étapes**

1. **Immédiat** : Tester avec MapView simplifié
2. **Court terme** : Vérifier empreinte SHA-1 dans Google Cloud Console
3. **Moyen terme** : Implémenter fallback Mapbox si nécessaire
4. **Long terme** : Optimiser performance et style personnalisé

---

**Note** : Ce guide sera mis à jour selon les résultats des tests et les retours utilisateurs.
