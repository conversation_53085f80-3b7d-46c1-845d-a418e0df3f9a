import { MAPS_CONFIG } from '../config/environment';

export interface DirectionsRoute {
  distance: {
    text: string;
    value: number; // en mètres
  };
  duration: {
    text: string;
    value: number; // en secondes
  };
  polyline: {
    points: string;
  };
  legs: Array<{
    distance: { text: string; value: number };
    duration: { text: string; value: number };
    start_location: { lat: number; lng: number };
    end_location: { lat: number; lng: number };
    steps: Array<{
      distance: { text: string; value: number };
      duration: { text: string; value: number };
      start_location: { lat: number; lng: number };
      end_location: { lat: number; lng: number };
      polyline: { points: string };
      html_instructions: string;
    }>;
  }>;
}

export interface DirectionsResponse {
  routes: DirectionsRoute[];
  status: string;
}

export interface RouteCoordinate {
  latitude: number;
  longitude: number;
}

class DirectionsService {
  private readonly baseUrl = 'https://maps.googleapis.com/maps/api/directions/json';
  private readonly apiKey = MAPS_CONFIG.google.apiKey;

  /**
   * Calcule l'itinéraire entre deux points
   */
  async getDirections(
    origin: { latitude: number; longitude: number },
    destination: { latitude: number; longitude: number },
    options?: {
      mode?: 'driving' | 'walking' | 'bicycling' | 'transit';
      avoidTolls?: boolean;
      avoidHighways?: boolean;
      avoidFerries?: boolean;
      language?: string;
      region?: string;
    }
  ): Promise<DirectionsRoute | null> {
    try {
      const params = new URLSearchParams({
        origin: `${origin.latitude},${origin.longitude}`,
        destination: `${destination.latitude},${destination.longitude}`,
        mode: options?.mode || 'driving',
        language: options?.language || MAPS_CONFIG.google.language,
        region: options?.region || MAPS_CONFIG.google.region,
        key: this.apiKey,
      });

      if (options?.avoidTolls) params.append('avoid', 'tolls');
      if (options?.avoidHighways) params.append('avoid', 'highways');
      if (options?.avoidFerries) params.append('avoid', 'ferries');

      const response = await fetch(`${this.baseUrl}?${params.toString()}`);
      const data: DirectionsResponse = await response.json();

      if (data.status === 'OK' && data.routes.length > 0) {
        return data.routes[0];
      }

      console.warn('❌ Directions API error:', data.status);
      return null;
    } catch (error) {
      console.error('❌ Error fetching directions:', error);
      return null;
    }
  }

  /**
   * Décode une polyline encodée en coordonnées
   */
  decodePolyline(encoded: string): RouteCoordinate[] {
    const poly: RouteCoordinate[] = [];
    let index = 0;
    const len = encoded.length;
    let lat = 0;
    let lng = 0;

    while (index < len) {
      let b: number;
      let shift = 0;
      let result = 0;

      do {
        b = encoded.charCodeAt(index++) - 63;
        result |= (b & 0x1f) << shift;
        shift += 5;
      } while (b >= 0x20);

      const dlat = (result & 1) !== 0 ? ~(result >> 1) : result >> 1;
      lat += dlat;

      shift = 0;
      result = 0;

      do {
        b = encoded.charCodeAt(index++) - 63;
        result |= (b & 0x1f) << shift;
        shift += 5;
      } while (b >= 0x20);

      const dlng = (result & 1) !== 0 ? ~(result >> 1) : result >> 1;
      lng += dlng;

      poly.push({
        latitude: lat / 1e5,
        longitude: lng / 1e5,
      });
    }

    return poly;
  }

  /**
   * Calcule la distance et le temps entre plusieurs points
   */
  async getDistanceMatrix(
    origins: Array<{ latitude: number; longitude: number }>,
    destinations: Array<{ latitude: number; longitude: number }>,
    options?: {
      mode?: 'driving' | 'walking' | 'bicycling' | 'transit';
      language?: string;
      region?: string;
    }
  ): Promise<Array<Array<{ distance: { text: string; value: number }; duration: { text: string; value: number } }>> | null> {
    try {
      const originsStr = origins.map(o => `${o.latitude},${o.longitude}`).join('|');
      const destinationsStr = destinations.map(d => `${d.latitude},${d.longitude}`).join('|');

      const params = new URLSearchParams({
        origins: originsStr,
        destinations: destinationsStr,
        mode: options?.mode || 'driving',
        language: options?.language || MAPS_CONFIG.google.language,
        region: options?.region || MAPS_CONFIG.google.region,
        key: this.apiKey,
      });

      const response = await fetch(`https://maps.googleapis.com/maps/api/distancematrix/json?${params.toString()}`);
      const data = await response.json();

      if (data.status === 'OK') {
        return data.rows.map((row: any) => row.elements);
      }

      console.warn('❌ Distance Matrix API error:', data.status);
      return null;
    } catch (error) {
      console.error('❌ Error fetching distance matrix:', error);
      return null;
    }
  }

  /**
   * Formate le temps de trajet en texte lisible
   */
  formatDuration(seconds: number): string {
    if (seconds < 60) {
      return `${seconds} sec`;
    } else if (seconds < 3600) {
      const minutes = Math.round(seconds / 60);
      return `${minutes} min`;
    } else {
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.round((seconds % 3600) / 60);
      return `${hours}h ${minutes}min`;
    }
  }

  /**
   * Formate la distance en texte lisible
   */
  formatDistance(meters: number): string {
    if (meters < 1000) {
      return `${meters} m`;
    } else {
      const km = (meters / 1000).toFixed(1);
      return `${km} km`;
    }
  }
}

export const directionsService = new DirectionsService();
