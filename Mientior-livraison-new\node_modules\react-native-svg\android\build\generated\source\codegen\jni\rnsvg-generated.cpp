
/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateModuleJniCpp.js
 */

#include "rnsvg.h"

namespace facebook::react {

static facebook::jsi::Value __hostFunction_NativeSvgRenderableModuleSpecJSI_isPointInFill(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
  static jmethodID cachedMethodId = nullptr;
  return static_cast<JavaTurboModule &>(turboModule).invokeJavaMethod(rt, BooleanKind, "isPointInFill", "(Ljava/lang/Double;Lcom/facebook/react/bridge/ReadableMap;)Z", args, count, cachedMethodId);
}

static facebook::jsi::Value __hostFunction_NativeSvgRenderableModuleSpecJSI_isPointInStroke(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
  static jmethodID cachedMethodId = nullptr;
  return static_cast<JavaTurboModule &>(turboModule).invokeJavaMethod(rt, BooleanKind, "isPointInStroke", "(Ljava/lang/Double;Lcom/facebook/react/bridge/ReadableMap;)Z", args, count, cachedMethodId);
}

static facebook::jsi::Value __hostFunction_NativeSvgRenderableModuleSpecJSI_getTotalLength(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
  static jmethodID cachedMethodId = nullptr;
  return static_cast<JavaTurboModule &>(turboModule).invokeJavaMethod(rt, NumberKind, "getTotalLength", "(Ljava/lang/Double;)D", args, count, cachedMethodId);
}

static facebook::jsi::Value __hostFunction_NativeSvgRenderableModuleSpecJSI_getPointAtLength(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
  static jmethodID cachedMethodId = nullptr;
  return static_cast<JavaTurboModule &>(turboModule).invokeJavaMethod(rt, ObjectKind, "getPointAtLength", "(Ljava/lang/Double;Lcom/facebook/react/bridge/ReadableMap;)Lcom/facebook/react/bridge/WritableMap;", args, count, cachedMethodId);
}

static facebook::jsi::Value __hostFunction_NativeSvgRenderableModuleSpecJSI_getBBox(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
  static jmethodID cachedMethodId = nullptr;
  return static_cast<JavaTurboModule &>(turboModule).invokeJavaMethod(rt, ObjectKind, "getBBox", "(Ljava/lang/Double;Lcom/facebook/react/bridge/ReadableMap;)Lcom/facebook/react/bridge/WritableMap;", args, count, cachedMethodId);
}

static facebook::jsi::Value __hostFunction_NativeSvgRenderableModuleSpecJSI_getCTM(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
  static jmethodID cachedMethodId = nullptr;
  return static_cast<JavaTurboModule &>(turboModule).invokeJavaMethod(rt, ObjectKind, "getCTM", "(Ljava/lang/Double;)Lcom/facebook/react/bridge/WritableMap;", args, count, cachedMethodId);
}

static facebook::jsi::Value __hostFunction_NativeSvgRenderableModuleSpecJSI_getScreenCTM(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
  static jmethodID cachedMethodId = nullptr;
  return static_cast<JavaTurboModule &>(turboModule).invokeJavaMethod(rt, ObjectKind, "getScreenCTM", "(Ljava/lang/Double;)Lcom/facebook/react/bridge/WritableMap;", args, count, cachedMethodId);
}

static facebook::jsi::Value __hostFunction_NativeSvgRenderableModuleSpecJSI_getRawResource(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
  static jmethodID cachedMethodId = nullptr;
  return static_cast<JavaTurboModule &>(turboModule).invokeJavaMethod(rt, PromiseKind, "getRawResource", "(Ljava/lang/String;Lcom/facebook/react/bridge/Promise;)V", args, count, cachedMethodId);
}

NativeSvgRenderableModuleSpecJSI::NativeSvgRenderableModuleSpecJSI(const JavaTurboModule::InitParams &params)
  : JavaTurboModule(params) {
  methodMap_["isPointInFill"] = MethodMetadata {2, __hostFunction_NativeSvgRenderableModuleSpecJSI_isPointInFill};
  methodMap_["isPointInStroke"] = MethodMetadata {2, __hostFunction_NativeSvgRenderableModuleSpecJSI_isPointInStroke};
  methodMap_["getTotalLength"] = MethodMetadata {1, __hostFunction_NativeSvgRenderableModuleSpecJSI_getTotalLength};
  methodMap_["getPointAtLength"] = MethodMetadata {2, __hostFunction_NativeSvgRenderableModuleSpecJSI_getPointAtLength};
  methodMap_["getBBox"] = MethodMetadata {2, __hostFunction_NativeSvgRenderableModuleSpecJSI_getBBox};
  methodMap_["getCTM"] = MethodMetadata {1, __hostFunction_NativeSvgRenderableModuleSpecJSI_getCTM};
  methodMap_["getScreenCTM"] = MethodMetadata {1, __hostFunction_NativeSvgRenderableModuleSpecJSI_getScreenCTM};
  methodMap_["getRawResource"] = MethodMetadata {1, __hostFunction_NativeSvgRenderableModuleSpecJSI_getRawResource};
}
static facebook::jsi::Value __hostFunction_NativeSvgViewModuleSpecJSI_toDataURL(facebook::jsi::Runtime& rt, TurboModule &turboModule, const facebook::jsi::Value* args, size_t count) {
  static jmethodID cachedMethodId = nullptr;
  return static_cast<JavaTurboModule &>(turboModule).invokeJavaMethod(rt, VoidKind, "toDataURL", "(Ljava/lang/Double;Lcom/facebook/react/bridge/ReadableMap;Lcom/facebook/react/bridge/Callback;)V", args, count, cachedMethodId);
}

NativeSvgViewModuleSpecJSI::NativeSvgViewModuleSpecJSI(const JavaTurboModule::InitParams &params)
  : JavaTurboModule(params) {
  methodMap_["toDataURL"] = MethodMetadata {3, __hostFunction_NativeSvgViewModuleSpecJSI_toDataURL};
}

std::shared_ptr<TurboModule> rnsvg_ModuleProvider(const std::string &moduleName, const JavaTurboModule::InitParams &params) {
  if (moduleName == "RNSVGRenderableModule") {
    return std::make_shared<NativeSvgRenderableModuleSpecJSI>(params);
  }
  if (moduleName == "RNSVGSvgViewModule") {
    return std::make_shared<NativeSvgViewModuleSpecJSI>(params);
  }
  return nullptr;
}

} // namespace facebook::react
