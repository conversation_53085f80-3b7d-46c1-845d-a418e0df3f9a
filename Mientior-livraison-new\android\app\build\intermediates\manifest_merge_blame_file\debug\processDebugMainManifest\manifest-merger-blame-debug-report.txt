1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.eliseedev.mientiorlivraison"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
11-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:10:3-75
11-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:10:20-73
12    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
12-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:2:3-78
12-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:2:20-76
13    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
13-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:3:3-76
13-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:3:20-74
14    <uses-permission android:name="android.permission.CAMERA" />
14-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:4:3-62
14-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:4:20-60
15    <uses-permission android:name="android.permission.INTERNET" />
15-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:5:3-64
15-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:5:20-62
16    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
16-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:6:3-77
16-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:6:20-75
17    <uses-permission android:name="android.permission.NOTIFICATIONS" />
17-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:7:3-69
17-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:7:20-67
18    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
18-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:8:3-77
18-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:8:20-75
19    <uses-permission android:name="android.permission.RECORD_AUDIO" />
19-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:9:3-68
19-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:9:20-66
20    <uses-permission android:name="android.permission.VIBRATE" />
20-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:11:3-63
20-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:11:20-61
21    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
21-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:12:3-78
21-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:12:20-76
22
23    <queries>
23-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:13:3-19:13
24        <intent>
24-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:14:5-18:14
25            <action android:name="android.intent.action.VIEW" />
25-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:15:7-58
25-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:15:15-56
26
27            <category android:name="android.intent.category.BROWSABLE" />
27-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:16:7-67
27-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:16:17-65
28
29            <data android:scheme="https" />
29-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:17:7-37
29-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:17:13-35
30        </intent>
31        <intent>
31-->[:react-native-webview] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-10:18
32            <action android:name="org.chromium.intent.action.PAY" />
32-->[:react-native-webview] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-69
32-->[:react-native-webview] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:21-66
33        </intent>
34        <intent>
34-->[:react-native-webview] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:9-13:18
35            <action android:name="org.chromium.intent.action.IS_READY_TO_PAY" />
35-->[:react-native-webview] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-81
35-->[:react-native-webview] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:21-78
36        </intent>
37        <intent>
37-->[:react-native-webview] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:9-16:18
38            <action android:name="org.chromium.intent.action.UPDATE_PAYMENT_DETAILS" />
38-->[:react-native-webview] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-88
38-->[:react-native-webview] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:21-85
39        </intent>
40
41        <package android:name="host.exp.exponent" /> <!-- Query open documents -->
41-->[:expo-dev-launcher] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-53
41-->[:expo-dev-launcher] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:18-50
42        <intent>
42-->[:expo-document-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-document-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:9-17:18
43            <action android:name="android.intent.action.OPEN_DOCUMENT" />
43-->[:expo-document-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-document-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-74
43-->[:expo-document-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-document-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:21-71
44
45            <category android:name="android.intent.category.DEFAULT" />
45-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:31:9-67
45-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:31:19-65
46            <category android:name="android.intent.category.OPENABLE" />
46-->[:expo-document-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-document-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-73
46-->[:expo-document-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-document-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:23-70
47
48            <data android:mimeType="*/*" />
48-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:17:7-37
49        </intent>
50        <intent>
50-->[:expo-image-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:9-21:18
51
52            <!-- Required for picking images from the camera roll if targeting API 30 -->
53            <action android:name="android.media.action.IMAGE_CAPTURE" />
53-->[:expo-image-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-73
53-->[:expo-image-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:21-70
54        </intent>
55        <intent>
55-->[:expo-image-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:9-26:18
56
57            <!-- Required for picking images from the camera if targeting API 30 -->
58            <action android:name="android.media.action.ACTION_VIDEO_CAPTURE" />
58-->[:expo-image-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-80
58-->[:expo-image-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:21-77
59        </intent> <!-- Query open documents -->
60        <intent>
60-->[:expo-file-system] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:9-17:18
61            <action android:name="android.intent.action.OPEN_DOCUMENT_TREE" />
61-->[:expo-file-system] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-79
61-->[:expo-file-system] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:21-76
62        </intent>
63        <intent>
63-->[host.exp.exponent:expo.modules.sms:13.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\0531343fe10d3c1dea07e7b068516664\transformed\expo.modules.sms-13.1.4\AndroidManifest.xml:8:9-14:18
64
65            <!-- Required for file sharing if targeting API 30 -->
66            <action android:name="android.intent.action.SEND" />
66-->[host.exp.exponent:expo.modules.sms:13.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\0531343fe10d3c1dea07e7b068516664\transformed\expo.modules.sms-13.1.4\AndroidManifest.xml:11:13-65
66-->[host.exp.exponent:expo.modules.sms:13.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\0531343fe10d3c1dea07e7b068516664\transformed\expo.modules.sms-13.1.4\AndroidManifest.xml:11:21-62
67
68            <data android:mimeType="*/*" />
68-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:17:7-37
69        </intent> <!-- Fallback SENDTO -->
70        <intent>
70-->[host.exp.exponent:expo.modules.sms:13.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\0531343fe10d3c1dea07e7b068516664\transformed\expo.modules.sms-13.1.4\AndroidManifest.xml:16:9-23:18
71            <action android:name="android.intent.action.SENDTO" />
71-->[host.exp.exponent:expo.modules.sms:13.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\0531343fe10d3c1dea07e7b068516664\transformed\expo.modules.sms-13.1.4\AndroidManifest.xml:17:13-67
71-->[host.exp.exponent:expo.modules.sms:13.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\0531343fe10d3c1dea07e7b068516664\transformed\expo.modules.sms-13.1.4\AndroidManifest.xml:17:21-64
72
73            <category android:name="android.intent.category.DEFAULT" />
73-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:31:9-67
73-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:31:19-65
74            <category android:name="android.intent.category.BROWSABLE" />
74-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:16:7-67
74-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:16:17-65
75
76            <data android:scheme="sms" />
76-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:17:7-37
76-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:17:13-35
77        </intent>
78        <intent>
78-->[host.exp.exponent:expo.modules.sms:13.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\0531343fe10d3c1dea07e7b068516664\transformed\expo.modules.sms-13.1.4\AndroidManifest.xml:24:9-31:18
79            <action android:name="android.intent.action.SENDTO" />
79-->[host.exp.exponent:expo.modules.sms:13.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\0531343fe10d3c1dea07e7b068516664\transformed\expo.modules.sms-13.1.4\AndroidManifest.xml:17:13-67
79-->[host.exp.exponent:expo.modules.sms:13.1.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\0531343fe10d3c1dea07e7b068516664\transformed\expo.modules.sms-13.1.4\AndroidManifest.xml:17:21-64
80
81            <category android:name="android.intent.category.DEFAULT" />
81-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:31:9-67
81-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:31:19-65
82            <category android:name="android.intent.category.BROWSABLE" />
82-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:16:7-67
82-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:16:17-65
83
84            <data android:scheme="smsto" />
84-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:17:7-37
84-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:17:13-35
85        </intent>
86        <intent>
86-->[host.exp.exponent:expo.modules.speech:13.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\6f3f3fe67e7bb5816c905504e02db79c\transformed\expo.modules.speech-13.1.7\AndroidManifest.xml:8:9-12:18
87
88            <!-- Required for text-to-speech if targeting API 30 -->
89            <action android:name="android.intent.action.TTS_SERVICE" />
89-->[host.exp.exponent:expo.modules.speech:13.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\6f3f3fe67e7bb5816c905504e02db79c\transformed\expo.modules.speech-13.1.7\AndroidManifest.xml:11:13-72
89-->[host.exp.exponent:expo.modules.speech:13.1.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\6f3f3fe67e7bb5816c905504e02db79c\transformed\expo.modules.speech-13.1.7\AndroidManifest.xml:11:21-69
90        </intent>
91        <intent>
91-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8cc367fa8ce0cb8acb1d9a2937a5991c\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:10:9-16:18
92            <action android:name="android.intent.action.GET_CONTENT" />
92-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8cc367fa8ce0cb8acb1d9a2937a5991c\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:11:13-72
92-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8cc367fa8ce0cb8acb1d9a2937a5991c\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:11:21-69
93
94            <category android:name="android.intent.category.OPENABLE" />
94-->[:expo-document-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-document-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-73
94-->[:expo-document-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-document-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:23-70
95
96            <data android:mimeType="*/*" />
96-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:17:7-37
97        </intent>
98        <intent>
98-->[androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\797066c703e3fc2659fba207b49a8e07\transformed\camera-extensions-1.4.1\AndroidManifest.xml:23:9-25:18
99            <action android:name="androidx.camera.extensions.action.VENDOR_ACTION" />
99-->[androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\797066c703e3fc2659fba207b49a8e07\transformed\camera-extensions-1.4.1\AndroidManifest.xml:24:13-86
99-->[androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\797066c703e3fc2659fba207b49a8e07\transformed\camera-extensions-1.4.1\AndroidManifest.xml:24:21-83
100        </intent> <!-- Needs to be explicitly declared on Android R+ -->
101        <package android:name="com.google.android.apps.maps" />
101-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d6792621c3f39dfaca06ce80f6b33f1\transformed\play-services-maps-18.2.0\AndroidManifest.xml:33:9-64
101-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d6792621c3f39dfaca06ce80f6b33f1\transformed\play-services-maps-18.2.0\AndroidManifest.xml:33:18-61
102    </queries>
103
104    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
104-->[:expo-image-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:5-76
104-->[:expo-image-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:22-73
105    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
105-->[:expo-image-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:5-75
105-->[:expo-image-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:22-72
106    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
106-->[:expo-updates] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-updates\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-79
106-->[:expo-updates] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-updates\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-76
107    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
107-->[host.exp.exponent:expo.modules.network:7.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\1660d72d87cc431f2e761d31424f03f3\transformed\expo.modules.network-7.1.5\AndroidManifest.xml:7:5-76
107-->[host.exp.exponent:expo.modules.network:7.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\1660d72d87cc431f2e761d31424f03f3\transformed\expo.modules.network-7.1.5\AndroidManifest.xml:7:22-73
108    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
108-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:7:5-81
108-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:7:22-78
109    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
109-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:8:5-77
109-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:8:22-74
110    <uses-permission android:name="android.permission.WAKE_LOCK" />
110-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:25:5-68
110-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:25:22-65
111    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" /> <!-- Required by older versions of Google Play services to create IID tokens -->
111-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:28:5-77
111-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:28:22-74
112    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
112-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\380599f20dc879b965c86746b2c44cf2\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:26:5-82
112-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\380599f20dc879b965c86746b2c44cf2\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:26:22-79
113
114    <uses-feature
114-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d6792621c3f39dfaca06ce80f6b33f1\transformed\play-services-maps-18.2.0\AndroidManifest.xml:26:5-28:35
115        android:glEsVersion="0x00020000"
115-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d6792621c3f39dfaca06ce80f6b33f1\transformed\play-services-maps-18.2.0\AndroidManifest.xml:27:9-41
116        android:required="true" />
116-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d6792621c3f39dfaca06ce80f6b33f1\transformed\play-services-maps-18.2.0\AndroidManifest.xml:28:9-32
117
118    <permission
118-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\306a6bcd4bd045cfd61a3c5eb43578e4\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
119        android:name="com.eliseedev.mientiorlivraison.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
119-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\306a6bcd4bd045cfd61a3c5eb43578e4\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
120        android:protectionLevel="signature" />
120-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\306a6bcd4bd045cfd61a3c5eb43578e4\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
121
122    <uses-permission android:name="com.eliseedev.mientiorlivraison.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
122-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\306a6bcd4bd045cfd61a3c5eb43578e4\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
122-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\306a6bcd4bd045cfd61a3c5eb43578e4\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
123    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" /> <!-- for android -->
123-->[com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\514d135c1e94e8854cd72d6563419da6\transformed\installreferrer-2.2\AndroidManifest.xml:9:5-110
123-->[com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\514d135c1e94e8854cd72d6563419da6\transformed\installreferrer-2.2\AndroidManifest.xml:9:22-107
124    <!-- <uses-permission android:name="com.android.launcher.permission.READ_SETTINGS"/> -->
125    <!-- <uses-permission android:name="com.android.launcher.permission.WRITE_SETTINGS"/> -->
126    <!-- <uses-permission android:name="com.android.launcher.permission.INSTALL_SHORTCUT" /> -->
127    <!-- <uses-permission android:name="com.android.launcher.permission.UNINSTALL_SHORTCUT" /> -->
128    <!-- for Samsung -->
129    <uses-permission android:name="com.sec.android.provider.badge.permission.READ" />
129-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\6329ce871df5828ee152a72c53b9164a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:5-86
129-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\6329ce871df5828ee152a72c53b9164a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:22-83
130    <uses-permission android:name="com.sec.android.provider.badge.permission.WRITE" /> <!-- for htc -->
130-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\6329ce871df5828ee152a72c53b9164a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:5-87
130-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\6329ce871df5828ee152a72c53b9164a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:22-84
131    <uses-permission android:name="com.htc.launcher.permission.READ_SETTINGS" />
131-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\6329ce871df5828ee152a72c53b9164a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:5-81
131-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\6329ce871df5828ee152a72c53b9164a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:22-78
132    <uses-permission android:name="com.htc.launcher.permission.UPDATE_SHORTCUT" /> <!-- for sony -->
132-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\6329ce871df5828ee152a72c53b9164a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:5-83
132-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\6329ce871df5828ee152a72c53b9164a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:22-80
133    <uses-permission android:name="com.sonyericsson.home.permission.BROADCAST_BADGE" />
133-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\6329ce871df5828ee152a72c53b9164a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:5-88
133-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\6329ce871df5828ee152a72c53b9164a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:22-85
134    <uses-permission android:name="com.sonymobile.home.permission.PROVIDER_INSERT_BADGE" /> <!-- for apex -->
134-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\6329ce871df5828ee152a72c53b9164a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:5-92
134-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\6329ce871df5828ee152a72c53b9164a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:22-89
135    <uses-permission android:name="com.anddoes.launcher.permission.UPDATE_COUNT" /> <!-- for solid -->
135-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\6329ce871df5828ee152a72c53b9164a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:5-84
135-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\6329ce871df5828ee152a72c53b9164a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:22-81
136    <uses-permission android:name="com.majeur.launcher.permission.UPDATE_BADGE" /> <!-- for huawei -->
136-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\6329ce871df5828ee152a72c53b9164a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:5-83
136-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\6329ce871df5828ee152a72c53b9164a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:22-80
137    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />
137-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\6329ce871df5828ee152a72c53b9164a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:5-91
137-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\6329ce871df5828ee152a72c53b9164a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:22-88
138    <uses-permission android:name="com.huawei.android.launcher.permission.READ_SETTINGS" />
138-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\6329ce871df5828ee152a72c53b9164a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:5-92
138-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\6329ce871df5828ee152a72c53b9164a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:22-89
139    <uses-permission android:name="com.huawei.android.launcher.permission.WRITE_SETTINGS" /> <!-- for ZUK -->
139-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\6329ce871df5828ee152a72c53b9164a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:5-93
139-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\6329ce871df5828ee152a72c53b9164a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:22-90
140    <uses-permission android:name="android.permission.READ_APP_BADGE" /> <!-- for OPPO -->
140-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\6329ce871df5828ee152a72c53b9164a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:5-73
140-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\6329ce871df5828ee152a72c53b9164a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:22-70
141    <uses-permission android:name="com.oppo.launcher.permission.READ_SETTINGS" />
141-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\6329ce871df5828ee152a72c53b9164a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:5-82
141-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\6329ce871df5828ee152a72c53b9164a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:22-79
142    <uses-permission android:name="com.oppo.launcher.permission.WRITE_SETTINGS" /> <!-- for EvMe -->
142-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\6329ce871df5828ee152a72c53b9164a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:5-83
142-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\6329ce871df5828ee152a72c53b9164a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:22-80
143    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_READ" />
143-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\6329ce871df5828ee152a72c53b9164a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:5-88
143-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\6329ce871df5828ee152a72c53b9164a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:22-85
144    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_WRITE" />
144-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\6329ce871df5828ee152a72c53b9164a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:5-89
144-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\6329ce871df5828ee152a72c53b9164a\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:22-86
145
146    <application
146-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:20:3-37:17
147        android:name="com.eliseedev.mientiorlivraison.MainApplication"
147-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:20:16-47
148        android:allowBackup="true"
148-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:20:162-188
149        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
149-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\306a6bcd4bd045cfd61a3c5eb43578e4\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
150        android:debuggable="true"
151        android:extractNativeLibs="false"
152        android:icon="@mipmap/ic_launcher"
152-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:20:81-115
153        android:label="@string/app_name"
153-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:20:48-80
154        android:roundIcon="@mipmap/ic_launcher_round"
154-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:20:116-161
155        android:supportsRtl="true"
155-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:20:221-247
156        android:theme="@style/AppTheme"
156-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:20:189-220
157        android:usesCleartextTraffic="true" >
157-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\debug\AndroidManifest.xml:6:18-53
158        <meta-data
158-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:21:5-83
159            android:name="expo.modules.updates.ENABLED"
159-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:21:16-59
160            android:value="false" />
160-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:21:60-81
161        <meta-data
161-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:22:5-105
162            android:name="expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH"
162-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:22:16-80
163            android:value="ALWAYS" />
163-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:22:81-103
164        <meta-data
164-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:23:5-99
165            android:name="expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS"
165-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:23:16-79
166            android:value="0" />
166-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:23:80-97
167
168        <activity
168-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:24:5-36:16
169            android:name="com.eliseedev.mientiorlivraison.MainActivity"
169-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:24:15-43
170            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|uiMode"
170-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:24:44-134
171            android:exported="true"
171-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:24:253-276
172            android:launchMode="singleTask"
172-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:24:135-166
173            android:screenOrientation="portrait"
173-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:24:277-313
174            android:theme="@style/Theme.App.SplashScreen"
174-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:24:207-252
175            android:windowSoftInputMode="adjustPan" >
175-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:24:167-206
176            <intent-filter>
176-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:25:7-28:23
177                <action android:name="android.intent.action.MAIN" />
177-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:26:9-60
177-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:26:17-58
178
179                <category android:name="android.intent.category.LAUNCHER" />
179-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:27:9-68
179-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:27:19-66
180            </intent-filter>
181            <intent-filter>
181-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:29:7-35:23
182                <action android:name="android.intent.action.VIEW" />
182-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:15:7-58
182-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:15:15-56
183
184                <category android:name="android.intent.category.DEFAULT" />
184-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:31:9-67
184-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:31:19-65
185                <category android:name="android.intent.category.BROWSABLE" />
185-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:16:7-67
185-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:16:17-65
186
187                <data android:scheme="mientior" />
187-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:17:7-37
187-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:17:13-35
188                <data android:scheme="exp+mientior-livraison-afrique" />
188-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:17:7-37
188-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:17:13-35
189            </intent-filter>
190        </activity>
191
192        <provider
192-->[:react-native-webview] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:9-28:20
193            android:name="com.reactnativecommunity.webview.RNCWebViewFileProvider"
193-->[:react-native-webview] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-83
194            android:authorities="com.eliseedev.mientiorlivraison.fileprovider"
194-->[:react-native-webview] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-64
195            android:exported="false"
195-->[:react-native-webview] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-37
196            android:grantUriPermissions="true" >
196-->[:react-native-webview] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-47
197            <meta-data
197-->[:react-native-webview] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-27:63
198                android:name="android.support.FILE_PROVIDER_PATHS"
198-->[:react-native-webview] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:17-67
199                android:resource="@xml/file_provider_paths" />
199-->[:react-native-webview] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:17-60
200        </provider>
201        <provider
201-->[:react-native-performance] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-performance\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:39
202            android:name="com.oblador.performance.StartTimeProvider"
202-->[:react-native-performance] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-performance\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-69
203            android:authorities="com.eliseedev.mientiorlivraison.start.time.provider"
203-->[:react-native-performance] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-performance\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-71
204            android:exported="false"
204-->[:react-native-performance] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-performance\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
205            android:initOrder="200" />
205-->[:react-native-performance] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-performance\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-36
206
207        <activity
207-->[:expo-dev-launcher] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-25:20
208            android:name="expo.modules.devlauncher.launcher.DevLauncherActivity"
208-->[:expo-dev-launcher] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-81
209            android:exported="true"
209-->[:expo-dev-launcher] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-36
210            android:launchMode="singleTask"
210-->[:expo-dev-launcher] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-44
211            android:theme="@style/Theme.DevLauncher.LauncherActivity" >
211-->[:expo-dev-launcher] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-70
212            <intent-filter>
212-->[:expo-dev-launcher] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-24:29
213                <action android:name="android.intent.action.VIEW" />
213-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:15:7-58
213-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:15:15-56
214
215                <category android:name="android.intent.category.DEFAULT" />
215-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:31:9-67
215-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:31:19-65
216                <category android:name="android.intent.category.BROWSABLE" />
216-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:16:7-67
216-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:16:17-65
217
218                <data android:scheme="expo-dev-launcher" />
218-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:17:7-37
218-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:17:13-35
219            </intent-filter>
220        </activity>
221        <activity
221-->[:expo-dev-launcher] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-29:70
222            android:name="expo.modules.devlauncher.launcher.errors.DevLauncherErrorActivity"
222-->[:expo-dev-launcher] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-93
223            android:screenOrientation="portrait"
223-->[:expo-dev-launcher] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-49
224            android:theme="@style/Theme.DevLauncher.ErrorActivity" />
224-->[:expo-dev-launcher] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-dev-launcher\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-67
225        <activity
225-->[:expo-dev-menu] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-21:20
226            android:name="expo.modules.devmenu.DevMenuActivity"
226-->[:expo-dev-menu] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-64
227            android:exported="true"
227-->[:expo-dev-menu] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-36
228            android:launchMode="singleTask"
228-->[:expo-dev-menu] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-44
229            android:theme="@style/Theme.AppCompat.Transparent.NoActionBar" >
229-->[:expo-dev-menu] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-75
230            <intent-filter>
230-->[:expo-dev-menu] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-dev-menu\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-20:29
231                <action android:name="android.intent.action.VIEW" />
231-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:15:7-58
231-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:15:15-56
232
233                <category android:name="android.intent.category.DEFAULT" />
233-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:31:9-67
233-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:31:19-65
234                <category android:name="android.intent.category.BROWSABLE" />
234-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:16:7-67
234-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:16:17-65
235
236                <data android:scheme="expo-dev-menu" />
236-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:17:7-37
236-->C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\android\app\src\main\AndroidManifest.xml:17:13-35
237            </intent-filter>
238        </activity>
239
240        <service
240-->[:expo-image-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:9-42:19
241            android:name="com.google.android.gms.metadata.ModuleDependencies"
241-->[:expo-image-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:13-78
242            android:enabled="false"
242-->[:expo-image-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:32:13-36
243            android:exported="false" >
243-->[:expo-image-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:33:13-37
244            <intent-filter>
244-->[:expo-image-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:13-37:29
245                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
245-->[:expo-image-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:17-94
245-->[:expo-image-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:25-91
246            </intent-filter>
247
248            <meta-data
248-->[:expo-image-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:39:13-41:36
249                android:name="photopicker_activity:0:required"
249-->[:expo-image-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:40:17-63
250                android:value="" />
250-->[:expo-image-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:41:17-33
251        </service>
252
253        <activity
253-->[:expo-image-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:44:9-46:59
254            android:name="com.canhub.cropper.CropImageActivity"
254-->[:expo-image-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:45:13-64
255            android:exported="true"
255-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8cc367fa8ce0cb8acb1d9a2937a5991c\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:35:13-36
256            android:theme="@style/Base.Theme.AppCompat" /> <!-- https://developer.android.com/guide/topics/manifest/provider-element.html -->
256-->[:expo-image-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:46:13-56
257        <provider
257-->[:expo-image-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:48:9-56:20
258            android:name="expo.modules.imagepicker.fileprovider.ImagePickerFileProvider"
258-->[:expo-image-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:49:13-89
259            android:authorities="com.eliseedev.mientiorlivraison.ImagePickerFileProvider"
259-->[:expo-image-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:50:13-75
260            android:exported="false"
260-->[:expo-image-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:51:13-37
261            android:grantUriPermissions="true" >
261-->[:expo-image-picker] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-image-picker\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:52:13-47
262            <meta-data
262-->[:react-native-webview] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-27:63
263                android:name="android.support.FILE_PROVIDER_PATHS"
263-->[:react-native-webview] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:17-67
264                android:resource="@xml/image_picker_provider_paths" />
264-->[:react-native-webview] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:17-60
265        </provider>
266
267        <meta-data
267-->[:expo-modules-core] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-11:89
268            android:name="org.unimodules.core.AppLoader#react-native-headless"
268-->[:expo-modules-core] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-79
269            android:value="expo.modules.adapters.react.apploader.RNHeadlessAppLoader" />
269-->[:expo-modules-core] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-86
270        <meta-data
270-->[:expo-modules-core] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-15:45
271            android:name="com.facebook.soloader.enabled"
271-->[:expo-modules-core] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-57
272            android:value="true" />
272-->[:expo-modules-core] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-33
273
274        <activity
274-->[com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\59ddc64970c204fac172d4da5009756f\transformed\react-android-0.79.2-debug\AndroidManifest.xml:19:9-21:40
275            android:name="com.facebook.react.devsupport.DevSettingsActivity"
275-->[com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\59ddc64970c204fac172d4da5009756f\transformed\react-android-0.79.2-debug\AndroidManifest.xml:20:13-77
276            android:exported="false" />
276-->[com.facebook.react:react-android:0.79.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\59ddc64970c204fac172d4da5009756f\transformed\react-android-0.79.2-debug\AndroidManifest.xml:21:13-37
277
278        <provider
278-->[:expo-file-system] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:9-30:20
279            android:name="expo.modules.filesystem.FileSystemFileProvider"
279-->[:expo-file-system] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-74
280            android:authorities="com.eliseedev.mientiorlivraison.FileSystemFileProvider"
280-->[:expo-file-system] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-74
281            android:exported="false"
281-->[:expo-file-system] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-37
282            android:grantUriPermissions="true" >
282-->[:expo-file-system] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-47
283            <meta-data
283-->[:react-native-webview] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-27:63
284                android:name="android.support.FILE_PROVIDER_PATHS"
284-->[:react-native-webview] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:17-67
285                android:resource="@xml/file_system_provider_paths" />
285-->[:react-native-webview] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:17-60
286        </provider>
287
288        <service
288-->[:expo-location] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-location\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:9-14:56
289            android:name="expo.modules.location.services.LocationTaskService"
289-->[:expo-location] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-location\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-78
290            android:exported="false"
290-->[:expo-location] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-location\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-37
291            android:foregroundServiceType="location" />
291-->[:expo-location] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\expo-location\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-53
292
293        <meta-data
293-->[host.exp.exponent:expo.modules.camera:16.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1c53cf14c7eb0e5643d47e3a95b72d0\transformed\expo.modules.camera-16.1.8\AndroidManifest.xml:11:9-13:42
294            android:name="com.google.mlkit.vision.DEPENDENCIES"
294-->[host.exp.exponent:expo.modules.camera:16.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1c53cf14c7eb0e5643d47e3a95b72d0\transformed\expo.modules.camera-16.1.8\AndroidManifest.xml:12:13-64
295            android:value="barcode_ui" />
295-->[host.exp.exponent:expo.modules.camera:16.1.8] C:\Users\<USER>\.gradle\caches\8.13\transforms\f1c53cf14c7eb0e5643d47e3a95b72d0\transformed\expo.modules.camera-16.1.8\AndroidManifest.xml:13:13-39
296
297        <service
297-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:11:9-17:19
298            android:name="expo.modules.notifications.service.ExpoFirebaseMessagingService"
298-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:12:13-91
299            android:exported="false" >
299-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:13:13-37
300            <intent-filter android:priority="-1" >
300-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:14:13-16:29
300-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:14:28-49
301                <action android:name="com.google.firebase.MESSAGING_EVENT" />
301-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:15:17-78
301-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:15:25-75
302            </intent-filter>
303        </service>
304
305        <receiver
305-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:19:9-31:20
306            android:name="expo.modules.notifications.service.NotificationsService"
306-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:20:13-83
307            android:enabled="true"
307-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:21:13-35
308            android:exported="false" >
308-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:22:13-37
309            <intent-filter android:priority="-1" >
309-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:23:13-30:29
309-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:23:28-49
310                <action android:name="expo.modules.notifications.NOTIFICATION_EVENT" />
310-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:24:17-88
310-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:24:25-85
311                <action android:name="android.intent.action.BOOT_COMPLETED" />
311-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:25:17-79
311-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:25:25-76
312                <action android:name="android.intent.action.REBOOT" />
312-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:26:17-71
312-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:26:25-68
313                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
313-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:27:17-82
313-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:27:25-79
314                <action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
314-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:28:17-82
314-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:28:25-79
315                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
315-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:29:17-84
315-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:29:25-81
316            </intent-filter>
317        </receiver>
318
319        <activity
319-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:33:9-40:75
320            android:name="expo.modules.notifications.service.NotificationForwarderActivity"
320-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:34:13-92
321            android:excludeFromRecents="true"
321-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:35:13-46
322            android:exported="false"
322-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:36:13-37
323            android:launchMode="standard"
323-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:37:13-42
324            android:noHistory="true"
324-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:38:13-37
325            android:taskAffinity=""
325-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:39:13-36
326            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
326-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:40:13-72
327
328        <provider
328-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8cc367fa8ce0cb8acb1d9a2937a5991c\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:23:9-31:20
329            android:name="com.canhub.cropper.CropFileProvider"
329-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8cc367fa8ce0cb8acb1d9a2937a5991c\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:24:13-63
330            android:authorities="com.eliseedev.mientiorlivraison.cropper.fileprovider"
330-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8cc367fa8ce0cb8acb1d9a2937a5991c\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:25:13-72
331            android:exported="false"
331-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8cc367fa8ce0cb8acb1d9a2937a5991c\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:26:13-37
332            android:grantUriPermissions="true" >
332-->[com.github.CanHub:Android-Image-Cropper:4.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\8cc367fa8ce0cb8acb1d9a2937a5991c\transformed\Android-Image-Cropper-4.3.1\AndroidManifest.xml:27:13-47
333            <meta-data
333-->[:react-native-webview] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-27:63
334                android:name="android.support.FILE_PROVIDER_PATHS"
334-->[:react-native-webview] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:17-67
335                android:resource="@xml/library_file_paths" />
335-->[:react-native-webview] C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:17-60
336        </provider>
337        <provider
337-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:31:9-39:20
338            android:name="androidx.startup.InitializationProvider"
338-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:32:13-67
339            android:authorities="com.eliseedev.mientiorlivraison.androidx-startup"
339-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:33:13-68
340            android:exported="false" >
340-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:34:13-37
341            <meta-data
341-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:36:13-38:52
342                android:name="androidx.work.WorkManagerInitializer"
342-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:37:17-68
343                android:value="androidx.startup" />
343-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:38:17-49
344            <meta-data
344-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93defa7eb94a1f1ee72c0ca0f587e7d5\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
345                android:name="androidx.emoji2.text.EmojiCompatInitializer"
345-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93defa7eb94a1f1ee72c0ca0f587e7d5\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
346                android:value="androidx.startup" />
346-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93defa7eb94a1f1ee72c0ca0f587e7d5\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
347            <meta-data
347-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\5c6a9353de243fb8621eac0d0cef3eb2\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
348                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
348-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\5c6a9353de243fb8621eac0d0cef3eb2\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
349                android:value="androidx.startup" />
349-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\5c6a9353de243fb8621eac0d0cef3eb2\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
350            <meta-data
350-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\99bcfde82ee67925aa78857cee59febf\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
351                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
351-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\99bcfde82ee67925aa78857cee59febf\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
352                android:value="androidx.startup" />
352-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\99bcfde82ee67925aa78857cee59febf\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
353        </provider>
354
355        <service
355-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:41:9-46:35
356            android:name="androidx.work.impl.background.systemalarm.SystemAlarmService"
356-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:42:13-88
357            android:directBootAware="false"
357-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:43:13-44
358            android:enabled="@bool/enable_system_alarm_service_default"
358-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:44:13-72
359            android:exported="false" />
359-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:45:13-37
360        <service
360-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:47:9-53:35
361            android:name="androidx.work.impl.background.systemjob.SystemJobService"
361-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:48:13-84
362            android:directBootAware="false"
362-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:49:13-44
363            android:enabled="@bool/enable_system_job_service_default"
363-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:50:13-70
364            android:exported="true"
364-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:51:13-36
365            android:permission="android.permission.BIND_JOB_SERVICE" />
365-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:52:13-69
366        <service
366-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:54:9-59:35
367            android:name="androidx.work.impl.foreground.SystemForegroundService"
367-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:55:13-81
368            android:directBootAware="false"
368-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:56:13-44
369            android:enabled="@bool/enable_system_foreground_service_default"
369-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:57:13-77
370            android:exported="false" />
370-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:58:13-37
371
372        <receiver
372-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:61:9-66:35
373            android:name="androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver"
373-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:62:13-88
374            android:directBootAware="false"
374-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:63:13-44
375            android:enabled="true"
375-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:64:13-35
376            android:exported="false" />
376-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:65:13-37
377        <receiver
377-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:67:9-77:20
378            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy"
378-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:68:13-106
379            android:directBootAware="false"
379-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:69:13-44
380            android:enabled="false"
380-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:70:13-36
381            android:exported="false" >
381-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:71:13-37
382            <intent-filter>
382-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:73:13-76:29
383                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
383-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:74:17-87
383-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:74:25-84
384                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
384-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:75:17-90
384-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:75:25-87
385            </intent-filter>
386        </receiver>
387        <receiver
387-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:78:9-88:20
388            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy"
388-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:79:13-104
389            android:directBootAware="false"
389-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:80:13-44
390            android:enabled="false"
390-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:81:13-36
391            android:exported="false" >
391-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:82:13-37
392            <intent-filter>
392-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:84:13-87:29
393                <action android:name="android.intent.action.BATTERY_OKAY" />
393-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:85:17-77
393-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:85:25-74
394                <action android:name="android.intent.action.BATTERY_LOW" />
394-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:86:17-76
394-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:86:25-73
395            </intent-filter>
396        </receiver>
397        <receiver
397-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:89:9-99:20
398            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy"
398-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:90:13-104
399            android:directBootAware="false"
399-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:91:13-44
400            android:enabled="false"
400-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:92:13-36
401            android:exported="false" >
401-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:93:13-37
402            <intent-filter>
402-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:95:13-98:29
403                <action android:name="android.intent.action.DEVICE_STORAGE_LOW" />
403-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:96:17-83
403-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:96:25-80
404                <action android:name="android.intent.action.DEVICE_STORAGE_OK" />
404-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:97:17-82
404-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:97:25-79
405            </intent-filter>
406        </receiver>
407        <receiver
407-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:100:9-109:20
408            android:name="androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy"
408-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:101:13-103
409            android:directBootAware="false"
409-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:102:13-44
410            android:enabled="false"
410-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:103:13-36
411            android:exported="false" >
411-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:104:13-37
412            <intent-filter>
412-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:106:13-108:29
413                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
413-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:107:17-79
413-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:107:25-76
414            </intent-filter>
415        </receiver>
416        <receiver
416-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:110:9-121:20
417            android:name="androidx.work.impl.background.systemalarm.RescheduleReceiver"
417-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:111:13-88
418            android:directBootAware="false"
418-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:112:13-44
419            android:enabled="false"
419-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:113:13-36
420            android:exported="false" >
420-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:114:13-37
421            <intent-filter>
421-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:116:13-120:29
422                <action android:name="android.intent.action.BOOT_COMPLETED" />
422-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:25:17-79
422-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:25:25-76
423                <action android:name="android.intent.action.TIME_SET" />
423-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:118:17-73
423-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:118:25-70
424                <action android:name="android.intent.action.TIMEZONE_CHANGED" />
424-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:119:17-81
424-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:119:25-78
425            </intent-filter>
426        </receiver>
427        <receiver
427-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:122:9-131:20
428            android:name="androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver"
428-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:123:13-99
429            android:directBootAware="false"
429-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:124:13-44
430            android:enabled="@bool/enable_system_alarm_service_default"
430-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:125:13-72
431            android:exported="false" >
431-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:126:13-37
432            <intent-filter>
432-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:128:13-130:29
433                <action android:name="androidx.work.impl.background.systemalarm.UpdateProxies" />
433-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:129:17-98
433-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:129:25-95
434            </intent-filter>
435        </receiver>
436        <receiver
436-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:132:9-142:20
437            android:name="androidx.work.impl.diagnostics.DiagnosticsReceiver"
437-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:133:13-78
438            android:directBootAware="false"
438-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:134:13-44
439            android:enabled="true"
439-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:135:13-35
440            android:exported="true"
440-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:136:13-36
441            android:permission="android.permission.DUMP" >
441-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:137:13-57
442            <intent-filter>
442-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:139:13-141:29
443                <action android:name="androidx.work.diagnostics.REQUEST_DIAGNOSTICS" />
443-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:140:17-88
443-->[androidx.work:work-runtime:2.7.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\0af32c27ab9fd6f7a6af4b4d4066eeae\transformed\work-runtime-2.7.1\AndroidManifest.xml:140:25-85
444            </intent-filter>
445        </receiver>
446
447        <service
447-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d1f7b5139d06634cc3f34d76f8142494\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
448            android:name="androidx.room.MultiInstanceInvalidationService"
448-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d1f7b5139d06634cc3f34d76f8142494\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
449            android:directBootAware="true"
449-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d1f7b5139d06634cc3f34d76f8142494\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
450            android:exported="false" />
450-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d1f7b5139d06634cc3f34d76f8142494\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
451
452        <uses-library
452-->[androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\797066c703e3fc2659fba207b49a8e07\transformed\camera-extensions-1.4.1\AndroidManifest.xml:29:9-31:40
453            android:name="androidx.camera.extensions.impl"
453-->[androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\797066c703e3fc2659fba207b49a8e07\transformed\camera-extensions-1.4.1\AndroidManifest.xml:30:13-59
454            android:required="false" />
454-->[androidx.camera:camera-extensions:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\797066c703e3fc2659fba207b49a8e07\transformed\camera-extensions-1.4.1\AndroidManifest.xml:31:13-37
455
456        <service
456-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\024473314cb477a873a41df1f2dc78f6\transformed\camera-camera2-1.4.1\AndroidManifest.xml:24:9-33:19
457            android:name="androidx.camera.core.impl.MetadataHolderService"
457-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\024473314cb477a873a41df1f2dc78f6\transformed\camera-camera2-1.4.1\AndroidManifest.xml:25:13-75
458            android:enabled="false"
458-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\024473314cb477a873a41df1f2dc78f6\transformed\camera-camera2-1.4.1\AndroidManifest.xml:26:13-36
459            android:exported="false" >
459-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\024473314cb477a873a41df1f2dc78f6\transformed\camera-camera2-1.4.1\AndroidManifest.xml:27:13-37
460            <meta-data
460-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\024473314cb477a873a41df1f2dc78f6\transformed\camera-camera2-1.4.1\AndroidManifest.xml:30:13-32:89
461                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
461-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\024473314cb477a873a41df1f2dc78f6\transformed\camera-camera2-1.4.1\AndroidManifest.xml:31:17-103
462                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
462-->[androidx.camera:camera-camera2:1.4.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\024473314cb477a873a41df1f2dc78f6\transformed\camera-camera2-1.4.1\AndroidManifest.xml:32:17-86
463        </service>
464
465        <receiver
465-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\380599f20dc879b965c86746b2c44cf2\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:29:9-40:20
466            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
466-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\380599f20dc879b965c86746b2c44cf2\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:30:13-78
467            android:exported="true"
467-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\380599f20dc879b965c86746b2c44cf2\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:31:13-36
468            android:permission="com.google.android.c2dm.permission.SEND" >
468-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\380599f20dc879b965c86746b2c44cf2\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:32:13-73
469            <intent-filter>
469-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\380599f20dc879b965c86746b2c44cf2\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:33:13-35:29
470                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
470-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\380599f20dc879b965c86746b2c44cf2\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:34:17-81
470-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\380599f20dc879b965c86746b2c44cf2\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:34:25-78
471            </intent-filter>
472
473            <meta-data
473-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\380599f20dc879b965c86746b2c44cf2\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:37:13-39:40
474                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
474-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\380599f20dc879b965c86746b2c44cf2\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:38:17-92
475                android:value="true" />
475-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\380599f20dc879b965c86746b2c44cf2\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:39:17-37
476        </receiver>
477        <!--
478             FirebaseMessagingService performs security checks at runtime,
479             but set to not exported to explicitly avoid allowing another app to call it.
480        -->
481        <service
481-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\380599f20dc879b965c86746b2c44cf2\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:46:9-53:19
482            android:name="com.google.firebase.messaging.FirebaseMessagingService"
482-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\380599f20dc879b965c86746b2c44cf2\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:47:13-82
483            android:directBootAware="true"
483-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\380599f20dc879b965c86746b2c44cf2\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:48:13-43
484            android:exported="false" >
484-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\380599f20dc879b965c86746b2c44cf2\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:49:13-37
485            <intent-filter android:priority="-500" >
485-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:14:13-16:29
485-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:14:28-49
486                <action android:name="com.google.firebase.MESSAGING_EVENT" />
486-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:15:17-78
486-->[host.exp.exponent:expo.modules.notifications:0.31.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\771f9cec8459956bf1becdd4a8560e2f\transformed\expo.modules.notifications-0.31.3\AndroidManifest.xml:15:25-75
487            </intent-filter>
488        </service>
489        <service
489-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\380599f20dc879b965c86746b2c44cf2\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:54:9-63:19
490            android:name="com.google.firebase.components.ComponentDiscoveryService"
490-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\380599f20dc879b965c86746b2c44cf2\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:55:13-84
491            android:directBootAware="true"
491-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cb6a07ae6c113830fda664a1a3f74969\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
492            android:exported="false" >
492-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\380599f20dc879b965c86746b2c44cf2\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:56:13-37
493            <meta-data
493-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\380599f20dc879b965c86746b2c44cf2\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:57:13-59:85
494                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
494-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\380599f20dc879b965c86746b2c44cf2\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:58:17-122
495                android:value="com.google.firebase.components.ComponentRegistrar" />
495-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\380599f20dc879b965c86746b2c44cf2\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:59:17-82
496            <meta-data
496-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\380599f20dc879b965c86746b2c44cf2\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:60:13-62:85
497                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
497-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\380599f20dc879b965c86746b2c44cf2\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:61:17-119
498                android:value="com.google.firebase.components.ComponentRegistrar" />
498-->[com.google.firebase:firebase-messaging:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\380599f20dc879b965c86746b2c44cf2\transformed\firebase-messaging-24.0.1\AndroidManifest.xml:62:17-82
499            <meta-data
499-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7a6de7bf0b1470187fab593af8c6930\transformed\firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
500                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
500-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7a6de7bf0b1470187fab593af8c6930\transformed\firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
501                android:value="com.google.firebase.components.ComponentRegistrar" />
501-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7a6de7bf0b1470187fab593af8c6930\transformed\firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
502            <meta-data
502-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7a6de7bf0b1470187fab593af8c6930\transformed\firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
503                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
503-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7a6de7bf0b1470187fab593af8c6930\transformed\firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
504                android:value="com.google.firebase.components.ComponentRegistrar" />
504-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7a6de7bf0b1470187fab593af8c6930\transformed\firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
505            <meta-data
505-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4014643dd64b18ee06fdc929b4f60dfa\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
506                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
506-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4014643dd64b18ee06fdc929b4f60dfa\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
507                android:value="com.google.firebase.components.ComponentRegistrar" />
507-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4014643dd64b18ee06fdc929b4f60dfa\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
508            <meta-data
508-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cb6a07ae6c113830fda664a1a3f74969\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
509                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
509-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cb6a07ae6c113830fda664a1a3f74969\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
510                android:value="com.google.firebase.components.ComponentRegistrar" />
510-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cb6a07ae6c113830fda664a1a3f74969\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
511            <meta-data
511-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4d8cc4ca0e35a89a493aab958b2c0a2c\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
512                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
512-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4d8cc4ca0e35a89a493aab958b2c0a2c\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
513                android:value="com.google.firebase.components.ComponentRegistrar" />
513-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4d8cc4ca0e35a89a493aab958b2c0a2c\transformed\firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
514        </service>
515
516        <provider
516-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cb6a07ae6c113830fda664a1a3f74969\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
517            android:name="com.google.firebase.provider.FirebaseInitProvider"
517-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cb6a07ae6c113830fda664a1a3f74969\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
518            android:authorities="com.eliseedev.mientiorlivraison.firebaseinitprovider"
518-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cb6a07ae6c113830fda664a1a3f74969\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
519            android:directBootAware="true"
519-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cb6a07ae6c113830fda664a1a3f74969\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
520            android:exported="false"
520-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cb6a07ae6c113830fda664a1a3f74969\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
521            android:initOrder="100" />
521-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cb6a07ae6c113830fda664a1a3f74969\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
522
523        <meta-data
523-->[com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac0dda40f5036986d8403279f04f501a\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:8:9-10:69
524            android:name="com.google.android.gms.version"
524-->[com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac0dda40f5036986d8403279f04f501a\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:9:13-58
525            android:value="@integer/google_play_services_version" /> <!-- Needs to be explicitly declared on P+ -->
525-->[com.google.maps.android:android-maps-utils:3.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\ac0dda40f5036986d8403279f04f501a\transformed\android-maps-utils-3.8.2\AndroidManifest.xml:10:13-66
526        <uses-library
526-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d6792621c3f39dfaca06ce80f6b33f1\transformed\play-services-maps-18.2.0\AndroidManifest.xml:39:9-41:40
527            android:name="org.apache.http.legacy"
527-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d6792621c3f39dfaca06ce80f6b33f1\transformed\play-services-maps-18.2.0\AndroidManifest.xml:40:13-50
528            android:required="false" />
528-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6d6792621c3f39dfaca06ce80f6b33f1\transformed\play-services-maps-18.2.0\AndroidManifest.xml:41:13-37
529        <!--
530        This activity is an invisible delegate activity to start scanner activity
531        and receive result, so it's unnecessary to support screen orientation and
532        we can avoid any side effect from activity recreation in any case.
533        -->
534        <activity
534-->[com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7e13feca8295c54dbb8ebd825318ae5\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:15:9-20:20
535            android:name="com.google.mlkit.vision.codescanner.internal.GmsBarcodeScanningDelegateActivity"
535-->[com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7e13feca8295c54dbb8ebd825318ae5\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:16:13-107
536            android:exported="false"
536-->[com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7e13feca8295c54dbb8ebd825318ae5\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:17:13-37
537            android:screenOrientation="portrait" >
537-->[com.google.android.gms:play-services-code-scanner:16.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b7e13feca8295c54dbb8ebd825318ae5\transformed\play-services-code-scanner-16.1.0\AndroidManifest.xml:18:13-49
538        </activity>
539
540        <service
540-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c482101d03f7fe7ae56d665c96568830\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:9:9-15:19
541            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
541-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c482101d03f7fe7ae56d665c96568830\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:10:13-91
542            android:directBootAware="true"
542-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c9f3d34fca54ef9c7958f49d384f7aba\transformed\common-18.9.0\AndroidManifest.xml:17:13-43
543            android:exported="false" >
543-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c482101d03f7fe7ae56d665c96568830\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:11:13-37
544            <meta-data
544-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c482101d03f7fe7ae56d665c96568830\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:12:13-14:85
545                android:name="com.google.firebase.components:com.google.mlkit.vision.barcode.internal.BarcodeRegistrar"
545-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c482101d03f7fe7ae56d665c96568830\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:13:17-120
546                android:value="com.google.firebase.components.ComponentRegistrar" />
546-->[com.google.android.gms:play-services-mlkit-barcode-scanning:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c482101d03f7fe7ae56d665c96568830\transformed\play-services-mlkit-barcode-scanning-18.3.0\AndroidManifest.xml:14:17-82
547            <meta-data
547-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8a12a52861f5caec9654dea7893e7e4e\transformed\vision-common-17.3.0\AndroidManifest.xml:12:13-14:85
548                android:name="com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar"
548-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8a12a52861f5caec9654dea7893e7e4e\transformed\vision-common-17.3.0\AndroidManifest.xml:13:17-124
549                android:value="com.google.firebase.components.ComponentRegistrar" />
549-->[com.google.mlkit:vision-common:17.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\8a12a52861f5caec9654dea7893e7e4e\transformed\vision-common-17.3.0\AndroidManifest.xml:14:17-82
550            <meta-data
550-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c9f3d34fca54ef9c7958f49d384f7aba\transformed\common-18.9.0\AndroidManifest.xml:20:13-22:85
551                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
551-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c9f3d34fca54ef9c7958f49d384f7aba\transformed\common-18.9.0\AndroidManifest.xml:21:17-120
552                android:value="com.google.firebase.components.ComponentRegistrar" />
552-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c9f3d34fca54ef9c7958f49d384f7aba\transformed\common-18.9.0\AndroidManifest.xml:22:17-82
553        </service>
554
555        <provider
555-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c9f3d34fca54ef9c7958f49d384f7aba\transformed\common-18.9.0\AndroidManifest.xml:9:9-13:38
556            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
556-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c9f3d34fca54ef9c7958f49d384f7aba\transformed\common-18.9.0\AndroidManifest.xml:10:13-78
557            android:authorities="com.eliseedev.mientiorlivraison.mlkitinitprovider"
557-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c9f3d34fca54ef9c7958f49d384f7aba\transformed\common-18.9.0\AndroidManifest.xml:11:13-69
558            android:exported="false"
558-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c9f3d34fca54ef9c7958f49d384f7aba\transformed\common-18.9.0\AndroidManifest.xml:12:13-37
559            android:initOrder="99" />
559-->[com.google.mlkit:common:18.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c9f3d34fca54ef9c7958f49d384f7aba\transformed\common-18.9.0\AndroidManifest.xml:13:13-35
560
561        <activity
561-->[com.google.android.gms:play-services-base:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ab0477328e0ae05829c4cc214e49234\transformed\play-services-base-18.2.0\AndroidManifest.xml:20:9-22:45
562            android:name="com.google.android.gms.common.api.GoogleApiActivity"
562-->[com.google.android.gms:play-services-base:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ab0477328e0ae05829c4cc214e49234\transformed\play-services-base-18.2.0\AndroidManifest.xml:20:19-85
563            android:exported="false"
563-->[com.google.android.gms:play-services-base:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ab0477328e0ae05829c4cc214e49234\transformed\play-services-base-18.2.0\AndroidManifest.xml:22:19-43
564            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
564-->[com.google.android.gms:play-services-base:18.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\2ab0477328e0ae05829c4cc214e49234\transformed\play-services-base-18.2.0\AndroidManifest.xml:21:19-78
565
566        <receiver
566-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\99bcfde82ee67925aa78857cee59febf\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
567            android:name="androidx.profileinstaller.ProfileInstallReceiver"
567-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\99bcfde82ee67925aa78857cee59febf\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
568            android:directBootAware="false"
568-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\99bcfde82ee67925aa78857cee59febf\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
569            android:enabled="true"
569-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\99bcfde82ee67925aa78857cee59febf\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
570            android:exported="true"
570-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\99bcfde82ee67925aa78857cee59febf\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
571            android:permission="android.permission.DUMP" >
571-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\99bcfde82ee67925aa78857cee59febf\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
572            <intent-filter>
572-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\99bcfde82ee67925aa78857cee59febf\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
573                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
573-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\99bcfde82ee67925aa78857cee59febf\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
573-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\99bcfde82ee67925aa78857cee59febf\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
574            </intent-filter>
575            <intent-filter>
575-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\99bcfde82ee67925aa78857cee59febf\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
576                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
576-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\99bcfde82ee67925aa78857cee59febf\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
576-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\99bcfde82ee67925aa78857cee59febf\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
577            </intent-filter>
578            <intent-filter>
578-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\99bcfde82ee67925aa78857cee59febf\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
579                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
579-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\99bcfde82ee67925aa78857cee59febf\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
579-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\99bcfde82ee67925aa78857cee59febf\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
580            </intent-filter>
581            <intent-filter>
581-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\99bcfde82ee67925aa78857cee59febf\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
582                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
582-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\99bcfde82ee67925aa78857cee59febf\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
582-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\99bcfde82ee67925aa78857cee59febf\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
583            </intent-filter>
584        </receiver>
585
586        <service
586-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\2dacf4110cea599a348e4be8992dc6ba\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
587            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
587-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\2dacf4110cea599a348e4be8992dc6ba\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
588            android:exported="false" >
588-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\2dacf4110cea599a348e4be8992dc6ba\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
589            <meta-data
589-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\2dacf4110cea599a348e4be8992dc6ba\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
590                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
590-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\2dacf4110cea599a348e4be8992dc6ba\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
591                android:value="cct" />
591-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\2dacf4110cea599a348e4be8992dc6ba\transformed\transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
592        </service>
593        <service
593-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\1513070d6931c9d145a3a48af88f4f0b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
594            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
594-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\1513070d6931c9d145a3a48af88f4f0b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
595            android:exported="false"
595-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\1513070d6931c9d145a3a48af88f4f0b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
596            android:permission="android.permission.BIND_JOB_SERVICE" >
596-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\1513070d6931c9d145a3a48af88f4f0b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
597        </service>
598
599        <receiver
599-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\1513070d6931c9d145a3a48af88f4f0b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
600            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
600-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\1513070d6931c9d145a3a48af88f4f0b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
601            android:exported="false" />
601-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.13\transforms\1513070d6931c9d145a3a48af88f4f0b\transformed\transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
602    </application>
603
604</manifest>
