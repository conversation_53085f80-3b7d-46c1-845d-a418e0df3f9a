@expo.modules.interfaces.filesystem.AppDirectoriesModuleInterface+expo.modules.core.interfaces.InternalModuleokio.ForwardingSink<EMAIL>,expo.modules.kotlin.exception.CodedException"androidx.core.content.FileProvider"expo.modules.kotlin.modules.Module4expo.modules.filesystem.FileSystemModule.TaskHandlerokhttp3.ResponseBodyexpo.modules.core.BasePackage"expo.modules.kotlin.records.Record$expo.modules.kotlin.types.Enumerablekotlin.Enum+expo.modules.filesystem.next.FileSystemPath+expo.modules.kotlin.sharedobjects.SharedRefjava.lang.AutoCloseable.expo.modules.kotlin.sharedobjects.SharedObject                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            