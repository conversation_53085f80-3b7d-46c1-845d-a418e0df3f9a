# 🔧 Configuration Google Cloud Console pour Mientior Livraison

## 📋 INFORMATIONS IMPORTANTES

### **Détails du Projet**
- **Package Name**: `com.livraisonafrique.mobile`
- **SHA-1 Fingerprint**: `5E:8F:16:06:2E:A3:CD:2C:4A:0D:54:78:76:BA:A6:F3:8C:AB:F6:25`
- **API Key**: `AIzaSyCUSlG6L03l-nE5SH9Rm8sHQLZRKuRhD3s`

## 🚀 ÉTAPES DE CONFIGURATION

### **1. Accéder à Google Cloud Console**
1. Aller sur https://console.cloud.google.com
2. Se connecter avec votre compte Google
3. Sélectionner ou créer un projet

### **2. Activer les APIs Nécessaires**
Aller dans **APIs & Services > Library** et activer :
- ✅ **Maps SDK for Android**
- ✅ **Maps SDK for iOS** 
- ✅ **Places API**
- ✅ **Directions API**
- ✅ **Geocoding API**

### **3. Configurer l'API Key**
1. Aller dans **APIs & Services > Credentials**
2. Cliquer sur votre API Key : `AIzaSyCUSlG6L03l-nE5SH9Rm8sHQLZRKuRhD3s`
3. Dans **Application restrictions**, sélectionner **Android apps**
4. Cliquer **Add an item**
5. Entrer les informations suivantes :

```
Package name: com.livraisonafrique.mobile
SHA-1 certificate fingerprint: 5E:8F:16:06:2E:A3:CD:2C:4A:0D:54:78:76:BA:A6:F3:8C:AB:F6:25
```

6. Cliquer **Save**

### **4. Configurer les Restrictions d'API**
Dans **API restrictions**, sélectionner **Restrict key** et cocher :
- Maps SDK for Android
- Maps SDK for iOS
- Places API
- Directions API
- Geocoding API

## 🔍 VÉRIFICATION DE LA CONFIGURATION

### **Test de l'API Key**
```bash
# Tester l'API Geocoding
curl "https://maps.googleapis.com/maps/api/geocode/json?latlng=5.3364,-4.0267&key=AIzaSyCUSlG6L03l-nE5SH9Rm8sHQLZRKuRhD3s"
```

### **Réponse Attendue**
```json
{
  "results": [
    {
      "formatted_address": "Abidjan, Côte d'Ivoire",
      ...
    }
  ],
  "status": "OK"
}
```

## ⚠️ PROBLÈMES COURANTS

### **1. Carte Blanche**
**Causes possibles :**
- API Key non configurée
- Package name incorrect
- SHA-1 fingerprint manquant
- APIs non activées

**Solutions :**
1. Vérifier la configuration ci-dessus
2. Attendre 5-10 minutes pour la propagation
3. Redémarrer l'application

### **2. Erreur "API Key not valid"**
**Solutions :**
1. Vérifier que l'API Key est correcte
2. Vérifier les restrictions d'application
3. Vérifier que les APIs sont activées

### **3. Erreur "This app is not authorized"**
**Solutions :**
1. Vérifier le package name : `com.livraisonafrique.mobile`
2. Vérifier l'empreinte SHA-1
3. Régénérer l'empreinte si nécessaire

## 🛠️ COMMANDES UTILES

### **Régénérer l'empreinte SHA-1**
```bash
keytool -list -v -keystore "android/app/debug.keystore" -alias androiddebugkey -storepass android -keypass android
```

### **Vérifier le package name**
```bash
grep -r "applicationId" android/app/build.gradle
```

### **Tester la configuration**
```bash
node scripts/test-google-maps.cjs
```

## 📱 CONFIGURATION ANDROID

### **AndroidManifest.xml**
Vérifier que cette ligne est présente :
```xml
<meta-data 
    android:name="com.google.android.geo.API_KEY" 
    android:value="AIzaSyCUSlG6L03l-nE5SH9Rm8sHQLZRKuRhD3s"/>
```

### **Permissions**
```xml
<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION"/>
<uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION"/>
<uses-permission android:name="android.permission.INTERNET"/>
```

## 🎯 CHECKLIST FINAL

- [ ] APIs activées dans Google Cloud Console
- [ ] API Key configurée avec restrictions Android
- [ ] Package name ajouté : `com.livraisonafrique.mobile`
- [ ] SHA-1 fingerprint ajouté : `5E:8F:16:06:2E:A3:CD:2C:4A:0D:54:78:76:BA:A6:F3:8C:AB:F6:25`
- [ ] API Key dans AndroidManifest.xml
- [ ] Permissions Android configurées
- [ ] Test API réussi
- [ ] Application redémarrée

## 🕐 TEMPS D'ATTENTE

Après configuration, attendre **5-10 minutes** pour que les changements se propagent dans le système Google.

## 📞 SUPPORT

Si le problème persiste après configuration :
1. Vérifier les logs Android : `npx expo run:android`
2. Tester l'API directement avec curl
3. Vérifier la facturation Google Cloud (si applicable)
4. Contacter le support Google Cloud

---

**Note** : Cette configuration est spécifique au projet Mientior Livraison avec le package `com.livraisonafrique.mobile`.
