CMake Warning in C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/CMakeLists.txt:
  The object file directory

    C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/android/app/.cxx/Debug/2d6fr4g5/arm64-v8a/rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/./

  has 188 characters.  The maximum full path to an object file is 250
  characters (see CMAKE_OBJECT_PATH_MAX).  Object file

    react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o

  cannot be safely placed under this directory.  The build may not work
  correctly.


CMake Warning in C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/CMakeLists.txt:
  The object file directory

    C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/android/app/.cxx/Debug/2d6fr4g5/arm64-v8a/rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/./

  has 208 characters.  The maximum full path to an object file is 250
  characters (see CMAKE_OBJECT_PATH_MAX).  Object file

    react/renderer/components/rngesturehandler_codegen/Props.cpp.o

  cannot be safely placed under this directory.  The build may not work
  correctly.


CMake Warning in C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-performance/android/build/generated/source/codegen/jni/CMakeLists.txt:
  The object file directory

    C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/android/app/.cxx/Debug/2d6fr4g5/arm64-v8a/RNPerformanceSpec_autolinked_build/CMakeFiles/react_codegen_RNPerformanceSpec.dir/./

  has 194 characters.  The maximum full path to an object file is 250
  characters (see CMAKE_OBJECT_PATH_MAX).  Object file

    react/renderer/components/RNPerformanceSpec/ComponentDescriptors.cpp.o

  cannot be safely placed under this directory.  The build may not work
  correctly.


CMake Warning in C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/CMakeLists.txt:
  The object file directory

    C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/android/app/.cxx/Debug/2d6fr4g5/arm64-v8a/RNVectorIconsSpec_autolinked_build/CMakeFiles/react_codegen_RNVectorIconsSpec.dir/./

  has 194 characters.  The maximum full path to an object file is 250
  characters (see CMAKE_OBJECT_PATH_MAX).  Object file

    react/renderer/components/RNVectorIconsSpec/ComponentDescriptors.cpp.o

  cannot be safely placed under this directory.  The build may not work
  correctly.


CMake Warning in C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-webview/android/build/generated/source/codegen/jni/CMakeLists.txt:
  The object file directory

    C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/android/app/.cxx/Debug/2d6fr4g5/arm64-v8a/RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/./

  has 188 characters.  The maximum full path to an object file is 250
  characters (see CMAKE_OBJECT_PATH_MAX).  Object file

    react/renderer/components/RNCWebViewSpec/RNCWebViewSpecJSI-generated.cpp.o

  cannot be safely placed under this directory.  The build may not work
  correctly.


CMake Warning in C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt:
  The object file directory

    C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/android/app/.cxx/Debug/2d6fr4g5/arm64-v8a/safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/./

  has 190 characters.  The maximum full path to an object file is 250
  characters (see CMAKE_OBJECT_PATH_MAX).  Object file

    C_/Users/<USER>/Documents/Mientior_livraison/Mientior-livraison-new/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp.o

  cannot be safely placed under this directory.  The build may not work
  correctly.


CMake Warning in C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt:
  The object file directory

    C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/android/app/.cxx/Debug/2d6fr4g5/arm64-v8a/rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/./

  has 178 characters.  The maximum full path to an object file is 250
  characters (see CMAKE_OBJECT_PATH_MAX).  Object file

    C_/Users/<USER>/Documents/Mientior_livraison/Mientior-livraison-new/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o

  cannot be safely placed under this directory.  The build may not work
  correctly.


