
/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateComponentDescriptorH.js
 */

#pragma once

#include <react/renderer/components/rnscreens/ShadowNodes.h>
#include <react/renderer/core/ConcreteComponentDescriptor.h>
#include <react/renderer/componentregistry/ComponentDescriptorProviderRegistry.h>

namespace facebook::react {

using RNSScreenContainerComponentDescriptor = ConcreteComponentDescriptor<RNSScreenContainerShadowNode>;
using RNSScreenContentWrapperComponentDescriptor = ConcreteComponentDescriptor<RNSScreenContentWrapperShadowNode>;
using RNSScreenFooterComponentDescriptor = ConcreteComponentDescriptor<RNSScreenFooterShadowNode>;
using RNSScreenNavigationContainerComponentDescriptor = ConcreteComponentDescriptor<RNSScreenNavigationContainerShadowNode>;
using RNSScreenStackComponentDescriptor = ConcreteComponentDescriptor<RNSScreenStackShadowNode>;
using RNSSearchBarComponentDescriptor = ConcreteComponentDescriptor<RNSSearchBarShadowNode>;

void rnscreens_registerComponentDescriptorsFromCodegen(
  std::shared_ptr<const ComponentDescriptorProviderRegistry> registry);

} // namespace facebook::react
