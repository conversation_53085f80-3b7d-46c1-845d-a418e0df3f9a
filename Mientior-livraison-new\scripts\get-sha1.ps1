# Script PowerShell pour extraire l'empreinte SHA-1 du certificat de débogage Android
# Auteur: Mientior Livraison Team
# Usage: .\get-sha1.ps1

Write-Host "🔍 Extraction de l'empreinte SHA-1 du certificat de débogage Android" -ForegroundColor Green
Write-Host "=================================================================" -ForegroundColor Green
Write-Host ""

# Chemins possibles pour le keystore
$keystorePaths = @(
    "android\app\debug.keystore",
    "$env:USERPROFILE\.android\debug.keystore",
    "$env:ANDROID_HOME\debug.keystore"
)

$keystoreFound = $false
$keystorePath = ""

# Rechercher le keystore
foreach ($path in $keystorePaths) {
    if (Test-Path $path) {
        $keystoreFound = $true
        $keystorePath = $path
        Write-Host "✅ Keystore trouvé: $path" -ForegroundColor Green
        break
    }
}

if (-not $keystoreFound) {
    Write-Host "❌ Aucun keystore trouvé dans les emplacements suivants:" -ForegroundColor Red
    foreach ($path in $keystorePaths) {
        Write-Host "   - $path" -ForegroundColor Yellow
    }
    exit 1
}

Write-Host ""
Write-Host "📋 Informations du certificat:" -ForegroundColor Cyan
Write-Host "   Keystore: $keystorePath"
Write-Host "   Alias: androiddebugkey"
Write-Host "   Store Password: android"
Write-Host "   Key Password: android"
Write-Host ""

# Essayer différentes commandes keytool
$keytoolCommands = @(
    "keytool",
    "$env:JAVA_HOME\bin\keytool.exe",
    "$env:JAVA_HOME\bin\keytool",
    "C:\Program Files\Java\jdk*\bin\keytool.exe",
    "C:\Program Files\Android\Android Studio\jre\bin\keytool.exe"
)

$keytoolFound = $false
$keytoolPath = ""

foreach ($cmd in $keytoolCommands) {
    try {
        if ($cmd -like "*\*") {
            # Gérer les chemins avec wildcards
            $expandedPaths = Get-ChildItem $cmd -ErrorAction SilentlyContinue
            if ($expandedPaths) {
                $keytoolPath = $expandedPaths[0].FullName
                $keytoolFound = $true
                break
            }
        } else {
            # Tester la commande directement
            $null = & $cmd -help 2>$null
            if ($LASTEXITCODE -eq 0 -or $LASTEXITCODE -eq 1) {
                $keytoolPath = $cmd
                $keytoolFound = $true
                break
            }
        }
    } catch {
        # Continuer avec la prochaine commande
    }
}

if (-not $keytoolFound) {
    Write-Host "❌ Keytool non trouvé. Veuillez installer Java JDK ou Android Studio." -ForegroundColor Red
    Write-Host ""
    Write-Host "💡 Solutions alternatives:" -ForegroundColor Yellow
    Write-Host "   1. Installer Java JDK et ajouter JAVA_HOME aux variables d'environnement"
    Write-Host "   2. Utiliser Android Studio: Build > Generate Signed Bundle/APK > View Certificate"
    Write-Host "   3. Utiliser la commande Gradle: ./gradlew signingReport"
    exit 1
}

Write-Host "✅ Keytool trouvé: $keytoolPath" -ForegroundColor Green
Write-Host ""

# Exécuter keytool pour obtenir les informations du certificat
Write-Host "🔄 Extraction des empreintes..." -ForegroundColor Cyan

try {
    $output = & $keytoolPath -list -v -keystore $keystorePath -alias androiddebugkey -storepass android -keypass android 2>&1
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Extraction réussie!" -ForegroundColor Green
        Write-Host ""
        Write-Host "📄 Sortie complète de keytool:" -ForegroundColor Cyan
        Write-Host "================================" -ForegroundColor Cyan
        $output | ForEach-Object { Write-Host $_ }
        Write-Host ""
        
        # Extraire SHA-1
        $sha1Pattern = "SHA1:\s*([A-F0-9:]{59})"
        $sha1Match = [regex]::Match($output, $sha1Pattern)
        
        if ($sha1Match.Success) {
            $sha1 = $sha1Match.Groups[1].Value
            Write-Host "🔑 EMPREINTE SHA-1:" -ForegroundColor Green
            Write-Host "   $sha1" -ForegroundColor Yellow
        } else {
            Write-Host "⚠️  Empreinte SHA-1 non trouvée dans la sortie" -ForegroundColor Yellow
        }
        
        # Extraire SHA-256
        $sha256Pattern = "SHA256:\s*([A-F0-9:]{95})"
        $sha256Match = [regex]::Match($output, $sha256Pattern)
        
        if ($sha256Match.Success) {
            $sha256 = $sha256Match.Groups[1].Value
            Write-Host ""
            Write-Host "🔑 EMPREINTE SHA-256:" -ForegroundColor Green
            Write-Host "   $sha256" -ForegroundColor Yellow
        }
        
        Write-Host ""
        Write-Host "📋 RÉSUMÉ POUR GOOGLE CLOUD CONSOLE:" -ForegroundColor Cyan
        Write-Host "====================================" -ForegroundColor Cyan
        Write-Host "   Nom de package: com.livraisonafrique.mobile"
        if ($sha1Match.Success) {
            Write-Host "   Empreinte SHA-1: $sha1"
        }
        if ($sha256Match.Success) {
            Write-Host "   Empreinte SHA-256: $sha256"
        }
        
    } else {
        Write-Host "❌ Erreur lors de l'exécution de keytool" -ForegroundColor Red
        Write-Host "Sortie d'erreur:" -ForegroundColor Yellow
        $output | ForEach-Object { Write-Host $_ -ForegroundColor Yellow }
    }
    
} catch {
    Write-Host "❌ Exception lors de l'exécution: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "🎯 Script terminé!" -ForegroundColor Green
