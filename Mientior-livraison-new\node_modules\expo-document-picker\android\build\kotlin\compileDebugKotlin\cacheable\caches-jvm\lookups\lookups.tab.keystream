  Activity android.app  	RESULT_OK android.app.Activity  startActivityForResult android.app.Activity  ClipData android.content  Context android.content  Intent android.content  Item android.content.ClipData  	getItemAt android.content.ClipData  	itemCount android.content.ClipData  uri android.content.ClipData.Item  getType android.content.ContentResolver  openInputStream android.content.ContentResolver  query android.content.ContentResolver  cacheDir android.content.Context  contentResolver android.content.Context  ACTION_OPEN_DOCUMENT android.content.Intent  CATEGORY_OPENABLE android.content.Intent  EXTRA_ALLOW_MULTIPLE android.content.Intent  EXTRA_MIME_TYPES android.content.Intent  Intent android.content.Intent  addCategory android.content.Intent  apply android.content.Intent  clipData android.content.Intent  data android.content.Intent  putExtra android.content.Intent  toTypedArray android.content.Intent  type android.content.Intent  Cursor android.database  getColumnIndex android.database.Cursor  getInt android.database.Cursor  	getString android.database.Cursor  isNull android.database.Cursor  moveToFirst android.database.Cursor  use android.database.Cursor  Uri android.net  fromFile android.net.Uri  let android.net.Uri  toString android.net.Uri  OpenableColumns android.provider  DISPLAY_NAME  android.provider.OpenableColumns  SIZE  android.provider.OpenableColumns  
FileUtilities expo.modules.core.utilities  generateOutputPath )expo.modules.core.utilities.FileUtilities  Activity expo.modules.documentpicker  Boolean expo.modules.documentpicker  CodedException expo.modules.documentpicker  Context expo.modules.documentpicker  DocumentDetails expo.modules.documentpicker  DocumentDetailsReader expo.modules.documentpicker  DocumentInfo expo.modules.documentpicker  DocumentPickerModule expo.modules.documentpicker  DocumentPickerOptions expo.modules.documentpicker  DocumentPickerResult expo.modules.documentpicker  
Exceptions expo.modules.documentpicker  FailedToCopyToCacheException expo.modules.documentpicker  FailedToReadDocumentException expo.modules.documentpicker  Field expo.modules.documentpicker  File expo.modules.documentpicker  FileOutputStream expo.modules.documentpicker  
FileUtilities expo.modules.documentpicker  
FilenameUtils expo.modules.documentpicker  IOException expo.modules.documentpicker  IOUtils expo.modules.documentpicker  Int expo.modules.documentpicker  Intent expo.modules.documentpicker  
IsNotEmpty expo.modules.documentpicker  List expo.modules.documentpicker  Module expo.modules.documentpicker  OPEN_DOCUMENT_CODE expo.modules.documentpicker  OpenableColumns expo.modules.documentpicker  PickingInProgressException expo.modules.documentpicker  Promise expo.modules.documentpicker  Record expo.modules.documentpicker  String expo.modules.documentpicker  Uri expo.modules.documentpicker  apply expo.modules.documentpicker  copyToCacheDirectory expo.modules.documentpicker  currentActivity expo.modules.documentpicker  handleMultipleSelection expo.modules.documentpicker  handleSingleSelection expo.modules.documentpicker  let expo.modules.documentpicker  listOf expo.modules.documentpicker  
mutableListOf expo.modules.documentpicker  pendingPromise expo.modules.documentpicker  toTypedArray expo.modules.documentpicker  until expo.modules.documentpicker  use expo.modules.documentpicker  copy +expo.modules.documentpicker.DocumentDetails  let +expo.modules.documentpicker.DocumentDetails  mimeType +expo.modules.documentpicker.DocumentDetails  name +expo.modules.documentpicker.DocumentDetails  size +expo.modules.documentpicker.DocumentDetails  uri +expo.modules.documentpicker.DocumentDetails  DocumentDetails 1expo.modules.documentpicker.DocumentDetailsReader  OpenableColumns 1expo.modules.documentpicker.DocumentDetailsReader  context 1expo.modules.documentpicker.DocumentDetailsReader  let 1expo.modules.documentpicker.DocumentDetailsReader  read 1expo.modules.documentpicker.DocumentDetailsReader  use 1expo.modules.documentpicker.DocumentDetailsReader  Activity 0expo.modules.documentpicker.DocumentPickerModule  DocumentDetailsReader 0expo.modules.documentpicker.DocumentPickerModule  DocumentInfo 0expo.modules.documentpicker.DocumentPickerModule  DocumentPickerResult 0expo.modules.documentpicker.DocumentPickerModule  
Exceptions 0expo.modules.documentpicker.DocumentPickerModule  FailedToCopyToCacheException 0expo.modules.documentpicker.DocumentPickerModule  FailedToReadDocumentException 0expo.modules.documentpicker.DocumentPickerModule  File 0expo.modules.documentpicker.DocumentPickerModule  FileOutputStream 0expo.modules.documentpicker.DocumentPickerModule  
FileUtilities 0expo.modules.documentpicker.DocumentPickerModule  
FilenameUtils 0expo.modules.documentpicker.DocumentPickerModule  IOUtils 0expo.modules.documentpicker.DocumentPickerModule  Intent 0expo.modules.documentpicker.DocumentPickerModule  ModuleDefinition 0expo.modules.documentpicker.DocumentPickerModule  OPEN_DOCUMENT_CODE 0expo.modules.documentpicker.DocumentPickerModule  PickingInProgressException 0expo.modules.documentpicker.DocumentPickerModule  Uri 0expo.modules.documentpicker.DocumentPickerModule  
appContext 0expo.modules.documentpicker.DocumentPickerModule  apply 0expo.modules.documentpicker.DocumentPickerModule  context 0expo.modules.documentpicker.DocumentPickerModule  copyDocumentToCacheDirectory 0expo.modules.documentpicker.DocumentPickerModule  copyToCacheDirectory 0expo.modules.documentpicker.DocumentPickerModule  currentActivity 0expo.modules.documentpicker.DocumentPickerModule  handleMultipleSelection 0expo.modules.documentpicker.DocumentPickerModule  handleSingleSelection 0expo.modules.documentpicker.DocumentPickerModule  let 0expo.modules.documentpicker.DocumentPickerModule  listOf 0expo.modules.documentpicker.DocumentPickerModule  
mutableListOf 0expo.modules.documentpicker.DocumentPickerModule  pendingPromise 0expo.modules.documentpicker.DocumentPickerModule  readDocumentDetails 0expo.modules.documentpicker.DocumentPickerModule  toTypedArray 0expo.modules.documentpicker.DocumentPickerModule  until 0expo.modules.documentpicker.DocumentPickerModule  use 0expo.modules.documentpicker.DocumentPickerModule  copyToCacheDirectory 1expo.modules.documentpicker.DocumentPickerOptions  multiple 1expo.modules.documentpicker.DocumentPickerOptions  type 1expo.modules.documentpicker.DocumentPickerOptions  
AppContext expo.modules.kotlin  Promise expo.modules.kotlin  currentActivity expo.modules.kotlin.AppContext  reactContext expo.modules.kotlin.AppContext  resolve expo.modules.kotlin.Promise  OnActivityResultPayload expo.modules.kotlin.events  
component1 2expo.modules.kotlin.events.OnActivityResultPayload  
component2 2expo.modules.kotlin.events.OnActivityResultPayload  
component3 2expo.modules.kotlin.events.OnActivityResultPayload  CodedException expo.modules.kotlin.exception  
Exceptions expo.modules.kotlin.exception  MissingActivity (expo.modules.kotlin.exception.Exceptions  ReactContextLost (expo.modules.kotlin.exception.Exceptions  AsyncFunctionBuilder expo.modules.kotlin.functions  AsyncFunctionComponent expo.modules.kotlin.functions  Module expo.modules.kotlin.modules  ModuleDefinition expo.modules.kotlin.modules  ModuleDefinitionBuilder expo.modules.kotlin.modules  ModuleDefinitionData expo.modules.kotlin.modules  Name ;expo.modules.kotlin.modules.InternalModuleDefinitionBuilder  OnActivityResult ;expo.modules.kotlin.modules.InternalModuleDefinitionBuilder  
appContext "expo.modules.kotlin.modules.Module  Activity 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  
AsyncFunction 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  DocumentPickerResult 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  Intent 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  Name 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  OPEN_DOCUMENT_CODE 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  OnActivityResult 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  PickingInProgressException 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  apply 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  copyToCacheDirectory 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  currentActivity 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  handleMultipleSelection 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  handleSingleSelection 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  pendingPromise 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  toTypedArray 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  
AsyncFunction 3expo.modules.kotlin.objects.ObjectDefinitionBuilder  AsyncFunctionWithPromise 3expo.modules.kotlin.objects.ObjectDefinitionBuilder  Field expo.modules.kotlin.records  
IsNotEmpty expo.modules.kotlin.records  Record expo.modules.kotlin.records  File java.io  FileOutputStream java.io  IOException java.io  InputStream java.io  use java.io.FileOutputStream  printStackTrace java.io.IOException  use java.io.InputStream  Array kotlin  	Function1 kotlin  	Function2 kotlin  Nothing kotlin  apply kotlin  let kotlin  use kotlin  not kotlin.Boolean  	compareTo 
kotlin.Int  let 
kotlin.Int  let 
kotlin.String  printStackTrace kotlin.Throwable  IntIterator kotlin.collections  List kotlin.collections  MutableList kotlin.collections  listOf kotlin.collections  
mutableListOf kotlin.collections  toTypedArray kotlin.collections  hasNext kotlin.collections.IntIterator  next kotlin.collections.IntIterator  get kotlin.collections.List  size kotlin.collections.List  toTypedArray kotlin.collections.List  add kotlin.collections.MutableList  use 	kotlin.io  	CharRange 
kotlin.ranges  IntRange 
kotlin.ranges  	LongRange 
kotlin.ranges  	UIntRange 
kotlin.ranges  
ULongRange 
kotlin.ranges  until 
kotlin.ranges  iterator kotlin.ranges.IntProgression  iterator kotlin.ranges.IntRange  
FilenameUtils org.apache.commons.io  IOUtils org.apache.commons.io  getExtension #org.apache.commons.io.FilenameUtils  copy org.apache.commons.io.IOUtils                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      