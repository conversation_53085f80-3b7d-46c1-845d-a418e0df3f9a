# 🌐 Guide de Résolution des Problèmes Réseau - Mientior Livraison

## 🚨 Problème Identifié

L'application affiche des erreurs de réseau :
```
ERROR [TypeError: Network request failed]
ERROR Erreur de connexion: [AuthRetryableFetchError: Network request failed]
```

## 🔍 Diagnostic

### ✅ Ce qui fonctionne :
- Connexion Internet de l'ordinateur de développement
- Serveur Supabase accessible depuis le navigateur
- Configuration Supabase correcte (URL et clés API)

### ❌ Problème identifié :
- **Émulateur Android** : Problème de connectivité réseau
- **Configuration réseau** : L'émulateur ne peut pas accéder à Internet

## 🔧 Solutions

### Solution 1: Redémarrer l'Émulateur Android

```bash
# Arrêter tous les processus ADB
adb kill-server

# Redémarrer ADB
adb start-server

# Redémarrer l'émulateur depuis Android Studio
# ou fermer et relancer l'émulateur
```

### Solution 2: Vérifier la Connectivité de l'Émulateur

```bash
# Tester la connectivité depuis l'émulateur
adb shell ping google.com

# Si ça ne fonctionne pas, essayer :
adb shell ping 8.8.8.8
```

### Solution 3: Configurer le Proxy/DNS de l'Émulateur

1. **Ouvrir Android Studio**
2. **Aller dans AVD Manager**
3. **Éditer l'émulateur** (icône crayon)
4. **Advanced Settings**
5. **Network** :
   - Speed: Full
   - Latency: None
   - DNS: 8.8.8.8, 8.8.4.4

### Solution 4: Utiliser un Appareil Physique

```bash
# Activer le mode développeur sur votre téléphone
# Connecter via USB
# Vérifier la détection
adb devices

# Lancer l'app sur l'appareil physique
npx expo run:android --device
```

### Solution 5: Redémarrer Metro avec Cache Clear

```bash
# Arrêter Metro (Ctrl+C)
# Nettoyer le cache et redémarrer
npx expo start --clear
```

### Solution 6: Configuration Réseau Windows (si applicable)

```bash
# Vérifier les paramètres de pare-feu Windows
# Autoriser Android Studio et l'émulateur
# Désactiver temporairement l'antivirus
```

## 🧪 Tests de Connectivité

### Test 1: Connectivité Basique
```bash
# Depuis votre ordinateur
node scripts/diagnose-network.js
```

### Test 2: Test depuis l'Émulateur
```bash
# Ouvrir le navigateur dans l'émulateur
# Aller sur https://www.google.com
# Si ça ne fonctionne pas = problème émulateur
```

### Test 3: Test API Supabase
```bash
# Depuis le navigateur de l'émulateur
# Aller sur votre URL Supabase
# Devrait afficher une page 404 (normal)
```

## 🔄 Procédure de Résolution Complète

### Étape 1: Diagnostic Rapide
```bash
# 1. Tester la connectivité de base
ping google.com

# 2. Tester Supabase depuis votre PC
node scripts/diagnose-network.js

# 3. Vérifier l'émulateur
adb shell ping google.com
```

### Étape 2: Solutions par Ordre de Priorité

1. **Redémarrer l'émulateur** (solution la plus courante)
2. **Utiliser un appareil physique** (solution de contournement)
3. **Reconfigurer l'émulateur** (solution technique)
4. **Vérifier les paramètres réseau** (solution système)

### Étape 3: Vérification
```bash
# Après chaque solution, tester :
npx expo start
# Ouvrir l'app et vérifier les logs
```

## 📱 Améliorations Implémentées

### Gestion d'Erreur Améliorée
- ✅ Messages d'erreur plus clairs
- ✅ Timeouts configurés (10-15 secondes)
- ✅ Mode dégradé en cas de problème réseau
- ✅ Retry automatique pour certaines opérations

### Logs Améliorés
```
⚠️ Problème réseau lors de l'initialisation, mode hors ligne
⚠️ Initialisation auth en mode dégradé (problème réseau)
💡 Problème de connexion réseau. Veuillez vérifier votre connexion Internet et réessayer.
```

## 🎯 Résultats Attendus

Après résolution :
- ✅ Disparition des erreurs `Network request failed`
- ✅ Authentification fonctionnelle
- ✅ Connexion Supabase stable
- ✅ Navigation fluide dans l'app

## 🆘 Si Rien ne Fonctionne

### Option 1: Mode Développement Local
```bash
# Utiliser l'app en mode hors ligne temporairement
# Les données mockées sont disponibles
```

### Option 2: Changer d'Émulateur
```bash
# Créer un nouvel émulateur Android
# Utiliser une version Android différente
# Essayer un émulateur x86 vs ARM
```

### Option 3: Utiliser Expo Go
```bash
# Installer Expo Go sur votre téléphone
# Scanner le QR code
# Tester sur appareil physique
```

## 📞 Support

Si le problème persiste :
1. Vérifier le statut Supabase : https://status.supabase.com
2. Tester sur un autre réseau (hotspot mobile)
3. Vérifier les paramètres de proxy d'entreprise
4. Contacter le support technique

---

**Note** : Ces erreurs réseau sont courantes en développement React Native et sont généralement liées à la configuration de l'émulateur Android plutôt qu'au code de l'application.
