{"version": 3, "file": "Updates.types.d.ts", "sourceRoot": "", "sources": ["../src/Updates.types.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,mBAAmB,EAAE,gBAAgB,EAAE,MAAM,gBAAgB,CAAC;AAEvE,MAAM,MAAM,QAAQ,GAAG,mBAAmB,GAAG,gBAAgB,CAAC;AAE9D,oBAAY,mCAAmC;IAC7C;;OAEG;IACH,6BAA6B,8BAA8B;IAC3D;;;OAGG;IACH,mCAAmC,oCAAoC;IACvE;;;OAGG;IACH,wBAAwB,2BAA2B;IACnD;;;OAGG;IACH,qCAAqC,sCAAsC;IAC3E;;OAEG;IACH,oBAAoB,oCAAoC;CACzD;AAED;;GAEG;AACH,MAAM,MAAM,yBAAyB,GAAG;IACtC;;OAEG;IACH,WAAW,EAAE,KAAK,CAAC;IACnB;;OAEG;IACH,QAAQ,EAAE,SAAS,CAAC;IACpB;;OAEG;IACH,oBAAoB,EAAE,IAAI,CAAC;IAC3B;;OAEG;IACH,MAAM,EAAE,SAAS,CAAC;CACnB,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,0BAA0B,GAAG;IACvC;;OAEG;IACH,WAAW,EAAE,IAAI,CAAC;IAClB;;OAEG;IACH,QAAQ,EAAE,QAAQ,CAAC;IACnB;;OAEG;IACH,oBAAoB,EAAE,KAAK,CAAC;IAC5B;;OAEG;IACH,MAAM,EAAE,SAAS,CAAC;CACnB,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,6BAA6B,GAAG;IAC1C;;OAEG;IACH,WAAW,EAAE,KAAK,CAAC;IACnB;;OAEG;IACH,QAAQ,EAAE,SAAS,CAAC;IACpB;;OAEG;IACH,oBAAoB,EAAE,KAAK,CAAC;IAC5B;;OAEG;IACH,MAAM,EAAE,mCAAmC,CAAC;CAC7C,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,iBAAiB,GACzB,yBAAyB,GACzB,0BAA0B,GAC1B,6BAA6B,CAAC;AAElC;;GAEG;AACH,MAAM,MAAM,wBAAwB,GAAG;IACrC;;;OAGG;IACH,KAAK,EAAE,IAAI,CAAC;IACZ;;OAEG;IACH,QAAQ,EAAE,QAAQ,CAAC;IACnB;;OAEG;IACH,oBAAoB,EAAE,KAAK,CAAC;CAC7B,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,wBAAwB,GAAG;IACrC;;;OAGG;IACH,KAAK,EAAE,KAAK,CAAC;IACb;;OAEG;IACH,QAAQ,EAAE,SAAS,CAAC;IACpB;;OAEG;IACH,oBAAoB,EAAE,KAAK,CAAC;CAC7B,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,mCAAmC,GAAG;IAChD;;;OAGG;IACH,KAAK,EAAE,KAAK,CAAC;IACb;;OAEG;IACH,QAAQ,EAAE,SAAS,CAAC;IACpB;;OAEG;IACH,oBAAoB,EAAE,IAAI,CAAC;CAC5B,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,iBAAiB,GACzB,wBAAwB,GACxB,wBAAwB,GACxB,mCAAmC,CAAC;AAExC;;GAEG;AACH,MAAM,MAAM,eAAe,GAAG;IAC5B;;OAEG;IACH,SAAS,EAAE,MAAM,CAAC;IAClB;;OAEG;IACH,OAAO,EAAE,MAAM,CAAC;IAChB;;OAEG;IACH,IAAI,EAAE,mBAAmB,CAAC;IAC1B;;OAEG;IACH,KAAK,EAAE,oBAAoB,CAAC;IAC5B;;OAEG;IACH,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB;;OAEG;IACH,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB;;OAEG;IACH,UAAU,CAAC,EAAE,MAAM,EAAE,CAAC;CACvB,CAAC;AAEF;;GAEG;AACH,oBAAY,mBAAmB;IAC7B,IAAI,SAAS;IACb,oBAAoB,uBAAuB;IAC3C,2BAA2B,6BAA6B;IACxD,yBAAyB,4BAA4B;IACrD,4BAA4B,8BAA8B;IAC1D,yBAAyB,2BAA2B;IACpD,qBAAqB,uBAAuB;IAC5C,qBAAqB,uBAAuB;IAC5C,gBAAgB,mBAAmB;IACnC,oBAAoB,wBAAwB;IAC5C,OAAO,YAAY;CACpB;AAED;;GAEG;AACH,oBAAY,oBAAoB;IAC9B,KAAK,UAAU;IACf,KAAK,UAAU;IACf,IAAI,SAAS;IACb,IAAI,SAAS;IACb,KAAK,UAAU;IACf,KAAK,UAAU;CAChB;AAED;;;;;GAKG;AACH,oBAAY,8BAA8B;IACxC;;OAEG;IACH,OAAO,YAAY;IACnB;;OAEG;IACH,iBAAiB,sBAAsB;IACvC;;OAEG;IACH,SAAS,cAAc;IACvB;;OAEG;IACH,KAAK,UAAU;CAChB;AAED;;GAEG;AACH,MAAM,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AAEjD;;GAEG;AACH,MAAM,MAAM,0BAA0B,GAAG;IAEvC,UAAU,EAAE,MAAM,CAAC;CACpB,CAAC;AAEF;;;;GAIG;AACH,MAAM,MAAM,gCAAgC,GAAG;IAC7C,yBAAyB,EAAE,OAAO,CAAC;IACnC,iBAAiB,EAAE,OAAO,CAAC;IAC3B,eAAe,EAAE,OAAO,CAAC;IACzB,UAAU,EAAE,OAAO,CAAC;IACpB,aAAa,EAAE,OAAO,CAAC;IACvB,YAAY,EAAE,OAAO,CAAC;IACtB,YAAY,EAAE,MAAM,CAAC;IACrB,cAAc,CAAC,EAAE,QAAQ,CAAC;IAC1B,kBAAkB,CAAC,EAAE,QAAQ,CAAC;IAC9B,QAAQ,CAAC,EAAE,0BAA0B,CAAC;IACtC,UAAU,CAAC,EAAE,KAAK,CAAC;IACnB,aAAa,CAAC,EAAE,KAAK,CAAC;IACtB,sBAAsB,CAAC,EAAE,IAAI,CAAC;IAC9B,cAAc,EAAE,MAAM,CAAC;CACxB,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,6BAA6B,GAAG;IAE1C,OAAO,EAAE,gCAAgC,CAAC;CAC3C,CAAC"}