/**
 * Configuration des variables d'environnement pour Mientior Livraison
 * Gestion centralisée des API keys et configurations
 */

// Interface pour la configuration de l'environnement
interface EnvironmentConfig {
  // API Keys
  googleMapsApiKey: string;
  mapboxApiKey: string;
  supabaseUrl: string;
  supabaseAnonKey: string;
  
  // Configuration de l'app
  nodeEnv: string;
  appEnv: string;
  debugMode: boolean;
  logLevel: string;
  
  // Configuration des paiements
  orangeMoneyApiKey: string;
  mtnMoneyApiKey: string;
  moovMoneyApiKey: string;
  
  // Configuration des livraisons
  deliveryBaseFee: number;
  deliveryPerKmFee: number;
  communityDiscountRate: number;
  
  // Configuration de géolocalisation
  defaultLatitude: number;
  defaultLongitude: number;
  defaultZoomLevel: number;
  locationAccuracyThreshold: number;
  
  // Configuration des notifications
  expoPushToken: string;
}

// Configuration par défaut
const defaultConfig: EnvironmentConfig = {
  // API Keys
  googleMapsApiKey: 'AIzaSyBOti4mM-6x9WDnZIjIeyb7GPvdUiRpOSA',
  mapboxApiKey: 'pk.eyJ1IjoibWllbnRpb3IiLCJhIjoiY2x0ZXh0ZXh0In0.example_token_here',
  supabaseUrl: 'https://hlvstikqlbyhofkkgrac.supabase.co',
  supabaseAnonKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.example_key_here',
  
  // Configuration de l'app
  nodeEnv: 'development',
  appEnv: 'development',
  debugMode: true,
  logLevel: 'debug',
  
  // Configuration des paiements
  orangeMoneyApiKey: '',
  mtnMoneyApiKey: '',
  moovMoneyApiKey: '',
  
  // Configuration des livraisons
  deliveryBaseFee: 1000,
  deliveryPerKmFee: 200,
  communityDiscountRate: 0.4,
  
  // Configuration de géolocalisation (Abidjan, Côte d'Ivoire)
  defaultLatitude: 5.3364,
  defaultLongitude: -4.0267,
  defaultZoomLevel: 13,
  locationAccuracyThreshold: 10,
  
  // Configuration des notifications
  expoPushToken: '',
};

// Export de la configuration
export const ENV = defaultConfig;

// Fonction utilitaire pour vérifier si on est en mode développement
export const isDevelopment = (): boolean => {
  return ENV.nodeEnv === 'development' || ENV.appEnv === 'development';
};

// Fonction utilitaire pour vérifier si on est en mode production
export const isProduction = (): boolean => {
  return ENV.nodeEnv === 'production' || ENV.appEnv === 'production';
};

// Fonction utilitaire pour le logging conditionnel
export const log = (message: string, ...args: any[]): void => {
  if (ENV.debugMode) {
    console.log(`[Mientior] ${message}`, ...args);
  }
};

// Fonction utilitaire pour les erreurs
export const logError = (message: string, error?: any): void => {
  if (ENV.debugMode) {
    console.error(`[Mientior Error] ${message}`, error);
  }
};

// Validation de la configuration
export const validateConfig = (): boolean => {
  const requiredKeys = [
    'googleMapsApiKey',
    'supabaseUrl',
    'supabaseAnonKey',
  ];
  
  const missingKeys = requiredKeys.filter(key => !ENV[key as keyof EnvironmentConfig]);
  
  if (missingKeys.length > 0) {
    logError('Configuration manquante:', missingKeys);
    return false;
  }
  
  log('✅ Configuration validée avec succès');
  return true;
};

// Configuration spécifique pour les cartes
export const MAPS_CONFIG = {
  // Configuration Google Maps
  google: {
    apiKey: ENV.googleMapsApiKey,
    region: 'CI', // Côte d'Ivoire
    language: 'fr', // Français
  },
  
  // Configuration Mapbox
  mapbox: {
    apiKey: ENV.mapboxApiKey,
    styleUrl: 'mapbox://styles/mapbox/streets-v11',
  },
  
  // Configuration par défaut des cartes
  defaultRegion: {
    latitude: ENV.defaultLatitude,
    longitude: ENV.defaultLongitude,
    latitudeDelta: 0.05,
    longitudeDelta: 0.05,
  },
  
  // Configuration des marqueurs
  markers: {
    communityPoint: {
      color: '#0DCAA8',
      size: 'medium',
    },
    deliverer: {
      color: '#4ECDC4',
      size: 'small',
    },
    customer: {
      color: '#FF6B35',
      size: 'small',
    },
  },
  
  // Configuration de la géolocalisation
  location: {
    enableHighAccuracy: true,
    timeout: 10000, // Réduit à 10 secondes
    maximumAge: 10000,
    distanceFilter: ENV.locationAccuracyThreshold,
  },
};

// Configuration des paiements Mobile Money
export const PAYMENT_CONFIG = {
  orangeMoney: {
    apiKey: ENV.orangeMoneyApiKey,
    baseUrl: 'https://api.orange.com/orange-money-webpay/ci/v1',
    merchantKey: 'your_merchant_key_here',
  },
  
  mtnMoney: {
    apiKey: ENV.mtnMoneyApiKey,
    baseUrl: 'https://sandbox.momodeveloper.mtn.com',
    subscriptionKey: 'your_subscription_key_here',
  },
  
  moovMoney: {
    apiKey: ENV.moovMoneyApiKey,
    baseUrl: 'https://api.moov-africa.ci/v1',
    merchantId: 'your_merchant_id_here',
  },
};

// Configuration des livraisons
export const DELIVERY_CONFIG = {
  fees: {
    base: ENV.deliveryBaseFee,
    perKm: ENV.deliveryPerKmFee,
    communityDiscount: ENV.communityDiscountRate,
  },
  
  limits: {
    maxDistance: 25, // km
    maxWeight: 50, // kg
    maxDeliveryTime: 120, // minutes
  },
  
  zones: {
    abidjan: {
      center: { latitude: 5.3364, longitude: -4.0267 },
      radius: 25, // km
    },
    bouake: {
      center: { latitude: 7.6944, longitude: -5.0300 },
      radius: 15, // km
    },
    yamoussoukro: {
      center: { latitude: 6.8276, longitude: -5.2893 },
      radius: 10, // km
    },
  },
};

export default ENV;
