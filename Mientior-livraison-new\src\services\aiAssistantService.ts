/**
 * Service d'Assistant IA Conversationnel pour Mientior Livraison
 * Support multilingue avec contexte culturel africain
 */

import { ENV } from '../config/environment';

// Types pour l'Assistant IA
export interface AIMessage {
  id: string;
  content: string;
  role: 'user' | 'assistant' | 'system';
  timestamp: Date;
  language: SupportedLanguage;
  context?: AIContext;
  confidence?: number;
  metadata?: Record<string, any>;
}

export interface AIContext {
  userId: string;
  userRole: 'client' | 'livreur' | 'marchand';
  currentLocation?: {
    latitude: number;
    longitude: number;
    city: string;
    country: string;
  };
  orderHistory?: any[];
  preferences?: UserPreferences;
  sessionId: string;
}

export interface UserPreferences {
  language: SupportedLanguage;
  culturalContext: CulturalContext;
  communicationStyle: 'formal' | 'casual' | 'friendly';
  topics: string[];
}

export interface CulturalContext {
  region: 'cote_divoire' | 'burkina_faso' | 'mali' | 'senegal' | 'ghana';
  ethnicity?: string;
  localCustoms: string[];
  preferredGreeting: string;
  timeZone: string;
}

export type SupportedLanguage = 'fr' | 'dioula' | 'baoulé' | 'en' | 'auto';

export interface AIResponse {
  message: string;
  confidence: number;
  language: SupportedLanguage;
  intent: AIIntent;
  actions?: AIAction[];
  suggestions?: string[];
  escalateToHuman?: boolean;
}

export interface AIIntent {
  category: 'order' | 'delivery' | 'payment' | 'support' | 'general' | 'cultural';
  subcategory: string;
  confidence: number;
  parameters?: Record<string, any>;
}

export interface AIAction {
  type: 'navigate' | 'call_api' | 'show_info' | 'escalate' | 'translate';
  payload: Record<string, any>;
}

class AIAssistantService {
  private static instance: AIAssistantService;
  private apiKey: string;
  private baseUrl: string;
  private conversationHistory: Map<string, AIMessage[]> = new Map();
  private culturalKnowledge: Map<string, any> = new Map();

  constructor() {
    this.apiKey = ENV.openaiApiKey || '';
    this.baseUrl = 'https://api.openai.com/v1';
    this.initializeCulturalKnowledge();
  }

  static getInstance(): AIAssistantService {
    if (!AIAssistantService.instance) {
      AIAssistantService.instance = new AIAssistantService();
    }
    return AIAssistantService.instance;
  }

  /**
   * Initialiser les connaissances culturelles africaines
   */
  private initializeCulturalKnowledge(): void {
    this.culturalKnowledge.set('greetings', {
      fr: ['Bonjour', 'Bonsoir', 'Salut'],
      dioula: ['I ni ce', 'I ni wula', 'I ka kene wa'],
      baoulé: ['Akwaba', 'Yako', 'Mia akwaba'],
    });

    this.culturalKnowledge.set('food_culture', {
      cote_divoire: {
        popular_dishes: ['Attiéké', 'Foutou', 'Kedjenou', 'Alloco', 'Garba'],
        meal_times: { breakfast: '7:00', lunch: '13:00', dinner: '19:00' },
        dietary_preferences: ['halal', 'vegetarian_options'],
      },
    });

    this.culturalKnowledge.set('local_expressions', {
      dioula: {
        'comment_allez_vous': 'I ka kene wa?',
        'merci': 'I ni ce',
        'au_revoir': 'Kan ben',
      },
      baoulé: {
        'comment_allez_vous': 'Ete sen?',
        'merci': 'Akpe',
        'au_revoir': 'Yako',
      },
    });
  }

  /**
   * Détecter la langue du message utilisateur
   */
  async detectLanguage(text: string): Promise<SupportedLanguage> {
    try {
      // Détection simple basée sur des mots-clés
      const frenchKeywords = ['bonjour', 'merci', 'comment', 'livraison', 'commande'];
      const dioulaKeywords = ['ni ce', 'ka kene', 'bara', 'wula'];
      const baoulKeywords = ['akwaba', 'yako', 'akpe', 'ete sen'];

      const lowerText = text.toLowerCase();

      if (dioulaKeywords.some(keyword => lowerText.includes(keyword))) {
        return 'dioula';
      }
      if (baoulKeywords.some(keyword => lowerText.includes(keyword))) {
        return 'baoulé';
      }
      if (frenchKeywords.some(keyword => lowerText.includes(keyword))) {
        return 'fr';
      }

      // Par défaut, français pour la Côte d'Ivoire
      return 'fr';
    } catch (error) {
      console.error('Erreur détection langue:', error);
      return 'fr';
    }
  }

  /**
   * Analyser l'intention du message
   */
  async analyzeIntent(message: string, context: AIContext): Promise<AIIntent> {
    const lowerMessage = message.toLowerCase();

    // Mots-clés pour différentes catégories
    const intentKeywords = {
      order: ['commander', 'commande', 'menu', 'restaurant', 'plat', 'manger'],
      delivery: ['livraison', 'livreur', 'adresse', 'temps', 'retard', 'où'],
      payment: ['payer', 'paiement', 'orange money', 'mtn', 'carte', 'prix'],
      support: ['aide', 'problème', 'bug', 'erreur', 'assistance', 'support'],
      cultural: ['tradition', 'culture', 'local', 'coutume', 'fête', 'événement'],
    };

    let bestMatch = { category: 'general', confidence: 0.3 };

    for (const [category, keywords] of Object.entries(intentKeywords)) {
      const matches = keywords.filter(keyword => lowerMessage.includes(keyword));
      const confidence = matches.length / keywords.length;

      if (confidence > bestMatch.confidence) {
        bestMatch = { category, confidence };
      }
    }

    return {
      category: bestMatch.category as any,
      subcategory: 'general',
      confidence: bestMatch.confidence,
      parameters: this.extractParameters(message, bestMatch.category),
    };
  }

  /**
   * Extraire les paramètres du message
   */
  private extractParameters(message: string, category: string): Record<string, any> {
    const params: Record<string, any> = {};

    // Extraction basique de paramètres
    if (category === 'order') {
      const restaurantMatch = message.match(/restaurant\s+([a-zA-Z\s]+)/i);
      if (restaurantMatch) params.restaurant = restaurantMatch[1].trim();

      const dishMatch = message.match(/plat\s+([a-zA-Z\s]+)/i);
      if (dishMatch) params.dish = dishMatch[1].trim();
    }

    if (category === 'delivery') {
      const addressMatch = message.match(/adresse\s+([a-zA-Z0-9\s,]+)/i);
      if (addressMatch) params.address = addressMatch[1].trim();
    }

    return params;
  }

  /**
   * Générer une réponse contextuelle
   */
  async generateResponse(
    message: string,
    context: AIContext,
    language: SupportedLanguage
  ): Promise<AIResponse> {
    try {
      const intent = await this.analyzeIntent(message, context);
      const culturalContext = this.getCulturalContext(context);

      // Construire le prompt avec contexte culturel
      const systemPrompt = this.buildSystemPrompt(context, language, culturalContext);
      const userPrompt = this.buildUserPrompt(message, intent, context);

      // Générer la réponse (simulation pour l'instant)
      const response = await this.callAIAPI(systemPrompt, userPrompt, language);

      return {
        message: response.content,
        confidence: response.confidence,
        language,
        intent,
        actions: this.generateActions(intent, context),
        suggestions: this.generateSuggestions(intent, context, language),
        escalateToHuman: response.confidence < 0.7 || intent.category === 'support',
      };
    } catch (error) {
      console.error('Erreur génération réponse IA:', error);
      return this.getFallbackResponse(language);
    }
  }

  /**
   * Construire le prompt système avec contexte culturel
   */
  private buildSystemPrompt(
    context: AIContext,
    language: SupportedLanguage,
    culturalContext: any
  ): string {
    const basePrompt = `Tu es Miento, l'assistant IA de Mientior Livraison, la plateforme de livraison collaborative en Côte d'Ivoire.

CONTEXTE CULTUREL:
- Tu parles ${language === 'fr' ? 'français' : language === 'dioula' ? 'dioula' : 'baoulé'}
- Tu connais la culture ivoirienne et les traditions locales
- Tu es respectueux des coutumes et des expressions locales
- Tu utilises des salutations appropriées selon l'heure et la culture

RÔLE UTILISATEUR: ${context.userRole}
LOCALISATION: ${context.currentLocation?.city || 'Abidjan'}, Côte d'Ivoire

CONNAISSANCES SPÉCIALISÉES:
- Restaurants et plats locaux (Attiéké, Foutou, Kedjenou, etc.)
- Système de livraison communautaire
- Paiements mobile money (Orange Money, MTN Money)
- Géographie et quartiers d'Abidjan

STYLE DE COMMUNICATION:
- Chaleureux et amical
- Utilise des expressions locales appropriées
- Adapte le niveau de formalité selon le contexte
- Propose des solutions pratiques et culturellement appropriées`;

    return basePrompt;
  }

  /**
   * Construire le prompt utilisateur
   */
  private buildUserPrompt(message: string, intent: AIIntent, context: AIContext): string {
    return `Message utilisateur: "${message}"

Intention détectée: ${intent.category} (confiance: ${intent.confidence})
Paramètres: ${JSON.stringify(intent.parameters)}

Réponds de manière utile et culturellement appropriée.`;
  }

  /**
   * Appeler l'API IA (simulation pour l'instant)
   */
  private async callAIAPI(
    systemPrompt: string,
    userPrompt: string,
    language: SupportedLanguage
  ): Promise<{ content: string; confidence: number }> {
    // Simulation de réponse IA
    // En production, utiliser OpenAI GPT-4 ou Azure Cognitive Services

    const responses = {
      fr: {
        order: "Bonjour ! Je serais ravi de vous aider avec votre commande. Quel type de plat vous fait envie aujourd'hui ? Nous avons d'excellents restaurants proposant de l'attiéké, du foutou, ou des spécialités internationales.",
        delivery: "Je vais vérifier le statut de votre livraison. Pouvez-vous me donner votre numéro de commande ? En attendant, sachez que nos livreurs communautaires font tout leur possible pour respecter les délais.",
        payment: "Pour les paiements, nous acceptons Orange Money, MTN Money et Moov Money. C'est simple et sécurisé ! Avez-vous besoin d'aide pour configurer votre méthode de paiement ?",
        support: "Je comprends votre préoccupation. Laissez-moi vous mettre en contact avec notre équipe support qui pourra vous aider personnellement.",
        general: "Akwaba ! Bienvenue sur Mientior Livraison. Comment puis-je vous aider aujourd'hui ?",
      },
      dioula: {
        order: "I ni ce ! N'bε fε ka i dεmε ni i ka baaraji ye. Munna dumuni bε i fε bi ? An bε baara kε ni restaurant caman ye minnu bε attiéké, foutou ani dumuni wεrεw dilan.",
        general: "I ni ce ! I ka na Mientior Livraison kan. N bε se ka i dεmε cogo min na bi ?",
      },
      baoulé: {
        order: "Akwaba ! Mia fε ka bo i dεmε ni i ka baaraji ye. Munna dumuni bε i fε bi ?",
        general: "Akwaba ! I ka na Mientior kan. Mia se ka i dεmε cogo min na ?",
      },
    };

    // Normaliser la langue pour éviter les erreurs d'indexation
    const normalizedLanguage = language === 'auto' || language === 'en' ? 'fr' : language;
    const langResponses = responses[normalizedLanguage as keyof typeof responses] || responses.fr;
    const content = langResponses.general;

    return {
      content,
      confidence: 0.85,
    };
  }

  /**
   * Obtenir le contexte culturel
   */
  private getCulturalContext(context: AIContext): any {
    return {
      region: context.currentLocation?.city || 'abidjan',
      timeOfDay: new Date().getHours(),
      culturalPreferences: context.preferences?.culturalContext,
    };
  }

  /**
   * Générer des actions basées sur l'intention
   */
  private generateActions(intent: AIIntent, context: AIContext): AIAction[] {
    const actions: AIAction[] = [];

    switch (intent.category) {
      case 'order':
        actions.push({
          type: 'navigate',
          payload: { screen: 'RestaurantList' },
        });
        break;
      case 'delivery':
        actions.push({
          type: 'navigate',
          payload: { screen: 'OrderTracking' },
        });
        break;
      case 'payment':
        actions.push({
          type: 'navigate',
          payload: { screen: 'PaymentMethods' },
        });
        break;
      case 'support':
        actions.push({
          type: 'escalate',
          payload: { reason: 'user_request', priority: 'normal' },
        });
        break;
    }

    return actions;
  }

  /**
   * Générer des suggestions
   */
  private generateSuggestions(
    intent: AIIntent,
    context: AIContext,
    language: SupportedLanguage
  ): string[] {
    const suggestions = {
      fr: {
        order: [
          "Voir les restaurants populaires",
          "Découvrir les plats locaux",
          "Consulter les promotions",
        ],
        delivery: [
          "Suivre ma commande",
          "Contacter le livreur",
          "Modifier l'adresse",
        ],
        payment: [
          "Configurer Orange Money",
          "Voir les méthodes de paiement",
          "Aide au paiement",
        ],
        support: [
          "Contacter le support",
          "Signaler un problème",
          "FAQ",
        ],
        cultural: [
          "Découvrir les plats locaux",
          "Traditions culinaires",
          "Événements culturels",
        ],
        general: [
          "Comment commander ?",
          "Quels sont les frais de livraison ?",
          "Comment payer avec Orange Money ?",
        ],
      },
    };

    // Normaliser la langue pour éviter les erreurs d'indexation
    const normalizedLanguage = language === 'auto' || language === 'en' || language === 'dioula' || language === 'baoulé' ? 'fr' : language;
    return suggestions[normalizedLanguage as keyof typeof suggestions]?.[intent.category] || suggestions.fr.general;
  }

  /**
   * Réponse de fallback en cas d'erreur
   */
  private getFallbackResponse(language: SupportedLanguage): AIResponse {
    const fallbacks = {
      fr: "Désolé, je rencontre une difficulté technique. Un agent va vous aider sous peu.",
      dioula: "Hakili to, n bε gεlεya dɔ la. Mɔgɔ dɔ bε na ka i dεmε.",
      baoulé: "Yako, mia gεlεya dɔ sɔrɔ. Mɔgɔ bε na ka i bo.",
    };

    // Normaliser la langue pour éviter les erreurs d'indexation
    const normalizedLanguage = language === 'auto' || language === 'en' ? 'fr' : language;

    return {
      message: fallbacks[normalizedLanguage as keyof typeof fallbacks] || fallbacks.fr,
      confidence: 0.5,
      language,
      intent: { category: 'general', subcategory: 'error', confidence: 0.5 },
      escalateToHuman: true,
    };
  }

  /**
   * Sauvegarder l'historique de conversation
   */
  saveConversationHistory(sessionId: string, message: AIMessage): void {
    if (!this.conversationHistory.has(sessionId)) {
      this.conversationHistory.set(sessionId, []);
    }
    this.conversationHistory.get(sessionId)!.push(message);

    // Limiter l'historique à 50 messages
    const history = this.conversationHistory.get(sessionId)!;
    if (history.length > 50) {
      history.splice(0, history.length - 50);
    }
  }

  /**
   * Obtenir l'historique de conversation
   */
  getConversationHistory(sessionId: string): AIMessage[] {
    return this.conversationHistory.get(sessionId) || [];
  }
}

export const aiAssistantService = AIAssistantService.getInstance();
