{"logs": [{"outputFile": "com.eliseedev.mientiorlivraison.app-mergeDebugResources-71:/values-sw/values-sw.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4407803e7a2d8d3f17c090093cb28e53\\transformed\\exoplayer-ui-2.18.1\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,289,506,706,785,862,947,1037,1125,1201,1267,1360,1455,1522,1586,1647,1722,1835,1952,2065,2139,2220,2293,2371,2462,2551,2619,2697,2750,2808,2856,2917,2978,3045,3109,3175,3238,3297,3363,3432,3498,3550,3616,3699,3782", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,78,76,84,89,87,75,65,92,94,66,63,60,74,112,116,112,73,80,72,77,90,88,67,77,52,57,47,60,60,66,63,65,62,58,65,68,65,51,65,82,82,57", "endOffsets": "284,501,701,780,857,942,1032,1120,1196,1262,1355,1450,1517,1581,1642,1717,1830,1947,2060,2134,2215,2288,2366,2457,2546,2614,2692,2745,2803,2851,2912,2973,3040,3104,3170,3233,3292,3358,3427,3493,3545,3611,3694,3777,3835"}, "to": {"startLines": "2,11,15,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,384,601,7762,7841,7918,8003,8093,8181,8257,8323,8416,8511,8578,8642,8703,8778,8891,9008,9121,9195,9276,9349,9427,9518,9607,9675,10402,10455,10513,10561,10622,10683,10750,10814,10880,10943,11002,11068,11137,11203,11255,11321,11404,11487", "endLines": "10,14,18,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "endColumns": "17,12,12,78,76,84,89,87,75,65,92,94,66,63,60,74,112,116,112,73,80,72,77,90,88,67,77,52,57,47,60,60,66,63,65,62,58,65,68,65,51,65,82,82,57", "endOffsets": "379,596,796,7836,7913,7998,8088,8176,8252,8318,8411,8506,8573,8637,8698,8773,8886,9003,9116,9190,9271,9344,9422,9513,9602,9670,9748,10450,10508,10556,10617,10678,10745,10809,10875,10938,10997,11063,11132,11198,11250,11316,11399,11482,11540"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8fb4e20b948a58ead7b50b7ad3e15312\\transformed\\appcompat-1.7.0\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,307,415,505,610,727,810,892,983,1076,1171,1265,1365,1458,1553,1647,1738,1829,1911,2012,2120,2219,2326,2438,2542,2704,2801", "endColumns": "102,98,107,89,104,116,82,81,90,92,94,93,99,92,94,93,90,90,81,100,107,98,106,111,103,161,96,82", "endOffsets": "203,302,410,500,605,722,805,887,978,1071,1166,1260,1360,1453,1548,1642,1733,1824,1906,2007,2115,2214,2321,2433,2537,2699,2796,2879"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,215", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "960,1063,1162,1270,1360,1465,1582,1665,1747,1838,1931,2026,2120,2220,2313,2408,2502,2593,2684,2766,2867,2975,3074,3181,3293,3397,3559,17753", "endColumns": "102,98,107,89,104,116,82,81,90,92,94,93,99,92,94,93,90,90,81,100,107,98,106,111,103,161,96,82", "endOffsets": "1058,1157,1265,1355,1460,1577,1660,1742,1833,1926,2021,2115,2215,2308,2403,2497,2588,2679,2761,2862,2970,3069,3176,3288,3392,3554,3651,17831"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\59ddc64970c204fac172d4da5009756f\\transformed\\react-android-0.79.2-debug\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,210,281,350,433,502,570,649,734,817,900,972,1062,1152,1231,1314,1398,1480,1556,1632,1719,1794,1877,1952", "endColumns": "68,85,70,68,82,68,67,78,84,82,82,71,89,89,78,82,83,81,75,75,86,74,82,74,77", "endOffsets": "119,205,276,345,428,497,565,644,729,812,895,967,1057,1147,1226,1309,1393,1475,1551,1627,1714,1789,1872,1947,2025"}, "to": {"startLines": "50,66,143,145,146,148,162,163,164,211,212,213,214,219,220,221,222,223,224,225,226,228,229,230,231", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3656,5168,11958,12102,12171,12317,13364,13432,13511,17425,17508,17591,17663,18082,18172,18251,18334,18418,18500,18576,18652,18840,18915,18998,19073", "endColumns": "68,85,70,68,82,68,67,78,84,82,82,71,89,89,78,82,83,81,75,75,86,74,82,74,77", "endOffsets": "3720,5249,12024,12166,12249,12381,13427,13506,13591,17503,17586,17658,17748,18167,18246,18329,18413,18495,18571,18647,18734,18910,18993,19068,19146"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\306a6bcd4bd045cfd61a3c5eb43578e4\\transformed\\core-1.13.1\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,449,556,663,778", "endColumns": "93,101,96,100,106,106,114,100", "endOffsets": "144,246,343,444,551,658,773,874"}, "to": {"startLines": "56,57,58,59,60,61,62,227", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4134,4228,4330,4427,4528,4635,4742,18739", "endColumns": "93,101,96,100,106,106,114,100", "endOffsets": "4223,4325,4422,4523,4630,4737,4852,18835"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ff199f796fece94789ea9bd928f0e62a\\transformed\\browser-1.6.0\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,169,270,387", "endColumns": "113,100,116,102", "endOffsets": "164,265,382,485"}, "to": {"startLines": "85,140,141,142", "startColumns": "4,4,4,4", "startOffsets": "7515,11637,11738,11855", "endColumns": "113,100,116,102", "endOffsets": "7624,11733,11850,11953"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\1b1d6e3a1eafdeb637916b2c04d0a37e\\transformed\\exoplayer-core-2.18.1\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,118,177,239,306,384,465,552,634", "endColumns": "62,58,61,66,77,80,86,81,69", "endOffsets": "113,172,234,301,379,460,547,629,699"}, "to": {"startLines": "112,113,114,115,116,117,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "9753,9816,9875,9937,10004,10082,10163,10250,10332", "endColumns": "62,58,61,66,77,80,86,81,69", "endOffsets": "9811,9870,9932,9999,10077,10158,10245,10327,10397"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f8296c347bb3d1ec471e3402800c1db5\\transformed\\material-1.12.0\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,259,335,409,482,579,668,767,896,979,1044,1112,1204,1277,1340,1426,1488,1551,1616,1684,1747,1801,1933,1990,2052,2106,2180,2318,2399,2479,2582,2666,2746,2878,2963,3050,3191,3279,3358,3412,3465,3531,3603,3685,3756,3841,3913,3988,4059,4132,4238,4335,4409,4504,4601,4675,4760,4860,4913,4998,5066,5154,5244,5306,5370,5433,5500,5617,5729,5840,5951,6009,6066,6147,6232,6313", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,75,73,72,96,88,98,128,82,64,67,91,72,62,85,61,62,64,67,62,53,131,56,61,53,73,137,80,79,102,83,79,131,84,86,140,87,78,53,52,65,71,81,70,84,71,74,70,72,105,96,73,94,96,73,84,99,52,84,67,87,89,61,63,62,66,116,111,110,110,57,56,80,84,80,79", "endOffsets": "254,330,404,477,574,663,762,891,974,1039,1107,1199,1272,1335,1421,1483,1546,1611,1679,1742,1796,1928,1985,2047,2101,2175,2313,2394,2474,2577,2661,2741,2873,2958,3045,3186,3274,3353,3407,3460,3526,3598,3680,3751,3836,3908,3983,4054,4127,4233,4330,4404,4499,4596,4670,4755,4855,4908,4993,5061,5149,5239,5301,5365,5428,5495,5612,5724,5835,5946,6004,6061,6142,6227,6308,6388"}, "to": {"startLines": "19,51,52,53,54,55,63,64,65,86,87,139,144,147,149,150,151,152,153,154,155,156,157,158,159,160,161,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,216,217,218", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "801,3725,3801,3875,3948,4045,4857,4956,5085,7629,7694,11545,12029,12254,12386,12472,12534,12597,12662,12730,12793,12847,12979,13036,13098,13152,13226,13596,13677,13757,13860,13944,14024,14156,14241,14328,14469,14557,14636,14690,14743,14809,14881,14963,15034,15119,15191,15266,15337,15410,15516,15613,15687,15782,15879,15953,16038,16138,16191,16276,16344,16432,16522,16584,16648,16711,16778,16895,17007,17118,17229,17287,17344,17836,17921,18002", "endLines": "22,51,52,53,54,55,63,64,65,86,87,139,144,147,149,150,151,152,153,154,155,156,157,158,159,160,161,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,216,217,218", "endColumns": "12,75,73,72,96,88,98,128,82,64,67,91,72,62,85,61,62,64,67,62,53,131,56,61,53,73,137,80,79,102,83,79,131,84,86,140,87,78,53,52,65,71,81,70,84,71,74,70,72,105,96,73,94,96,73,84,99,52,84,67,87,89,61,63,62,66,116,111,110,110,57,56,80,84,80,79", "endOffsets": "955,3796,3870,3943,4040,4129,4951,5080,5163,7689,7757,11632,12097,12312,12467,12529,12592,12657,12725,12788,12842,12974,13031,13093,13147,13221,13359,13672,13752,13855,13939,14019,14151,14236,14323,14464,14552,14631,14685,14738,14804,14876,14958,15029,15114,15186,15261,15332,15405,15511,15608,15682,15777,15874,15948,16033,16133,16186,16271,16339,16427,16517,16579,16643,16706,16773,16890,17002,17113,17224,17282,17339,17420,17916,17997,18077"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2ab0477328e0ae05829c4cc214e49234\\transformed\\play-services-base-18.2.0\\res\\values-sw\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,293,445,566,671,830,951,1066,1176,1337,1439,1589,1712,1858,2013,2077,2148", "endColumns": "99,151,120,104,158,120,114,109,160,101,149,122,145,154,63,70,91", "endOffsets": "292,444,565,670,829,950,1065,1175,1336,1438,1588,1711,1857,2012,2076,2147,2239"}, "to": {"startLines": "67,68,69,70,71,72,73,74,76,77,78,79,80,81,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5254,5358,5514,5639,5748,5911,6036,6155,6415,6580,6686,6840,6967,7117,7276,7344,7419", "endColumns": "103,155,124,108,162,124,118,113,164,105,153,126,149,158,67,74,95", "endOffsets": "5353,5509,5634,5743,5906,6031,6150,6264,6575,6681,6835,6962,7112,7271,7339,7414,7510"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cea641498eb0b29f919daa30d9671bd0\\transformed\\play-services-basement-18.3.0\\res\\values-sw\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "141", "endOffsets": "336"}, "to": {"startLines": "75", "startColumns": "4", "startOffsets": "6269", "endColumns": "145", "endOffsets": "6410"}}]}]}