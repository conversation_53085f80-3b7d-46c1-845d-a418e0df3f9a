{"installationFolder": "C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\android\\build\\intermediates\\prefab_package\\debug\\prefab", "gradlePath": ":react-native-reanimated", "packageInfo": {"packageName": "react-native-reanimated", "packageVersion": "3.17.5", "packageSchemaVersion": 2, "packageDependencies": [], "modules": [{"moduleName": "reanimated", "moduleHeaders": "C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\android\\build\\prefab-headers\\reanimated", "moduleExportLibraries": [], "abis": []}, {"moduleName": "worklets", "moduleHeaders": "C:\\Users\\<USER>\\Documents\\Mientior livraison\\Mientior-livraison-new\\node_modules\\react-native-reanimated\\android\\build\\prefab-headers\\worklets", "moduleExportLibraries": [], "abis": []}]}}