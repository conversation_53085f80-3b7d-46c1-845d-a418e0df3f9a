import { supabase } from './supabase';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Location from 'expo-location';

export interface Restaurant {
  id: string;
  user_id: string;
  business_name: string;
  // CRITICAL FIX: Use ACTUAL Supabase enum values only
  business_type: 'restaurant' | 'colis' | 'marchandises' | 'pharmacie' | 'epicerie' | 'autres';
  description?: string;
  logo_url?: string;
  cover_image_url?: string;
  address: string;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
  phone?: string;
  email?: string;
  is_verified: boolean;
  is_open: boolean;
  opening_hours?: any;
  average_preparation_time?: number;
  minimum_order_amount?: number;
  delivery_fee: number;
  delivery_radius_km?: number;
  rating: number;
  total_orders: number;
  created_at: string;
  updated_at: string;
  // Calculated fields
  distance?: number;
  is_available?: boolean;
  estimated_delivery_time?: number;
}

export interface RestaurantFilters {
  business_type?: Restaurant['business_type'];
  is_open?: boolean;
  is_verified?: boolean;
  max_distance?: number;
  min_rating?: number;
  max_delivery_fee?: number;
  search_query?: string;
  user_location?: {
    latitude: number;
    longitude: number;
  };
}

const RESTAURANTS_CACHE_KEY = 'restaurants_cache';
const CACHE_DURATION = 10 * 60 * 1000; // 10 minutes

class RestaurantService {
  private cache: Map<string, { data: Restaurant[]; timestamp: number }> = new Map();
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes

  /**
   * Récupère tous les restaurants avec filtres optionnels
   */
  async getRestaurants(filters?: RestaurantFilters): Promise<Restaurant[]> {
    try {
      console.log('🍽️ Fetching restaurants with filters:', filters);

      // Generate cache key based on filters
      const cacheKey = this.generateCacheKey(filters);

      // Check cache first
      const cached = this.getFromCache(cacheKey);
      if (cached) {
        console.log('🍽️ Returning cached restaurants');
        return cached;
      }

      if (!supabase) {
        console.log('⚠️ Supabase not available, returning empty array');
        return [];
      }

      // Utiliser la vue simple pour éviter les problèmes RLS
      console.log('🍽️ Using restaurants simple view for clean data...');
      let query = supabase
        .from('restaurants_simple_view')
        .select(`
          user_id,
          business_name,
          business_type,
          description,
          address,
          phone,
          email,
          is_verified,
          is_available,
          delivery_fee,
          rating,
          logo_url,
          cover_image_url,
          estimated_delivery_time,
          distance
        `);

      // Appliquer les filtres de manière optimisée
      if (filters?.business_type) {
        query = query.eq('business_type', filters.business_type);
      }

      if (filters?.is_open !== undefined) {
        query = query.eq('is_open', filters.is_open);
      }

      if (filters?.is_verified !== undefined) {
        query = query.eq('is_verified', filters.is_verified);
      }

      if (filters?.min_rating) {
        query = query.gte('rating', filters.min_rating);
      }

      if (filters?.search_query) {
        query = query.ilike('business_name', `%${filters.search_query}%`);
      }

      // Ordonner par rating et nombre de commandes
      query = query.order('rating', { ascending: false })
                   .order('total_orders', { ascending: false });

      const { data, error } = await query;

      if (error) {
        console.error('❌ Error fetching restaurants:', error);
        // Try to return cached data as fallback
        const cached = this.getFromCache(this.generateCacheKey(filters));
        if (cached) {
          console.log('🍽️ Returning cached restaurants after error');
          return cached;
        }
        return [];
      }

      // Transformation simplifiée - les données de la vue sont déjà propres
      let restaurants = data?.map((dbRestaurant: any) => ({
        id: dbRestaurant.user_id,
        user_id: dbRestaurant.user_id,
        business_name: dbRestaurant.business_name || 'Restaurant',
        business_type: dbRestaurant.business_type,
        description: dbRestaurant.description || 'Restaurant africain authentique',
        logo_url: dbRestaurant.logo_url,
        cover_image_url: dbRestaurant.cover_image_url || dbRestaurant.logo_url,
        address: dbRestaurant.address,
        coordinates: (() => {
          const coords = RestaurantService.generateCoordinatesFromAddress(dbRestaurant.address, dbRestaurant.user_id);
          console.log(`📍 Generated coordinates for ${dbRestaurant.business_name}:`, coords);
          return coords;
        })(),
        phone: dbRestaurant.phone,
        email: dbRestaurant.email,
        is_verified: dbRestaurant.is_verified,
        is_open: true, // Toujours ouvert pour les tests
        opening_hours: {},
        average_preparation_time: 30,
        minimum_order_amount: 1000,
        delivery_fee: dbRestaurant.delivery_fee || 500,
        delivery_radius_km: 10,
        rating: dbRestaurant.rating || 4.0,
        total_orders: dbRestaurant.total_orders || 0,
        distance: dbRestaurant.distance || 0,
        estimated_delivery_time: dbRestaurant.estimated_delivery_time || 30,
        is_available: dbRestaurant.is_available !== false,
        created_at: dbRestaurant.created_at,
        updated_at: dbRestaurant.updated_at,
      })) || [];

      // Les données de la vue sont déjà optimisées - pas besoin de calculs complexes
      console.log(`✅ ${restaurants.length} restaurants fetched from clean view`);

      // Cache the results
      this.setCache(cacheKey, restaurants);

      return restaurants;

    } catch (error) {
      console.error('❌ Error in getRestaurants:', error);

      // Try to return cached data
      const cached = this.getFromCache(cacheKey);
      if (cached) {
        console.log('🍽️ Returning cached restaurants after error');
        return cached;
      }

      // Fallback to empty array
      return [];
    }
  }

  /**
   * Récupère les restaurants proches d'une localisation
   */
  async getNearbyRestaurants(
    userLocation: { latitude: number; longitude: number },
    radiusKm = 10
  ): Promise<Restaurant[]> {
    return this.getRestaurants({
      user_location: userLocation,
      max_distance: radiusKm,
      is_open: true,
    });
  }

  /**
   * Recherche des restaurants par nom ou type (version améliorée avec vue simple)
   */
  async searchRestaurants(query: string, userLocation?: { latitude: number; longitude: number }): Promise<Restaurant[]> {
    try {
      console.log('🔍 Searching restaurants for:', query);

      if (!query.trim()) {
        return [];
      }

      // Utiliser la vue simple pour éviter les problèmes RLS
      const { data, error } = await supabase
        .from('restaurants_simple_view')
        .select(`
          user_id,
          business_name,
          business_type,
          description,
          address,
          phone,
          email,
          is_verified,
          is_available,
          delivery_fee,
          rating,
          logo_url,
          cover_image_url,
          estimated_delivery_time,
          distance
        `)
        .or(`business_name.ilike.%${query.trim()}%,description.ilike.%${query.trim()}%`)
        .eq('is_verified', true)
        .limit(20);

      if (error) {
        console.error('❌ Error searching restaurants:', error);
        return [];
      }

      const restaurants = data?.map((dbRestaurant: any) => ({
        id: dbRestaurant.user_id,
        user_id: dbRestaurant.user_id,
        business_name: dbRestaurant.business_name || 'Restaurant',
        business_type: dbRestaurant.business_type,
        description: dbRestaurant.description || 'Restaurant africain authentique',
        logo_url: dbRestaurant.logo_url,
        cover_image_url: dbRestaurant.cover_image_url || dbRestaurant.logo_url,
        address: dbRestaurant.address,
        coordinates: RestaurantService.generateCoordinatesFromAddress(dbRestaurant.address, dbRestaurant.user_id),
        phone: dbRestaurant.phone,
        email: dbRestaurant.email,
        is_verified: dbRestaurant.is_verified,
        is_open: true,
        opening_hours: {},
        average_preparation_time: 30,
        minimum_order_amount: 1000,
        delivery_fee: dbRestaurant.delivery_fee || 500,
        delivery_radius_km: 10,
        rating: dbRestaurant.rating || 4.0,
        total_orders: dbRestaurant.total_orders || 0,
        distance: dbRestaurant.distance || 0,
        estimated_delivery_time: dbRestaurant.estimated_delivery_time || 30,
        is_available: dbRestaurant.is_available !== false,
        created_at: dbRestaurant.created_at,
        updated_at: dbRestaurant.updated_at,
      })) || [];

      console.log(`🔍 ${restaurants.length} restaurants found for "${query}"`);
      return restaurants;

    } catch (error) {
      console.error('❌ Error searching restaurants:', error);
      return [];
    }
  }

  /**
   * Récupère les restaurants populaires
   */
  async getPopularRestaurants(userLocation?: { latitude: number; longitude: number }): Promise<Restaurant[]> {
    return this.getRestaurants({
      min_rating: 4.0,
      is_open: true,
      is_verified: true,
      user_location: userLocation,
    });
  }

  /**
   * Récupère les restaurants par type de service
   */
  async getRestaurantsByType(
    businessType: Restaurant['business_type'],
    userLocation?: { latitude: number; longitude: number }
  ): Promise<Restaurant[]> {
    return this.getRestaurants({
      business_type: businessType,
      is_open: true,
      user_location: userLocation,
    });
  }

  /**
   * Récupère un restaurant par ID
   */
  async getRestaurantById(restaurantId: string): Promise<Restaurant | null> {
    try {
      if (!supabase) {
        console.error('❌ Supabase not available');
        return null;
      }

      const { data, error } = await supabase
        .from('merchant_profiles')
        .select('*')
        .eq('user_id', restaurantId)
        .single();

      if (error || !data) {
        console.error('❌ Error fetching restaurant:', error);
        return null;
      }

      return this.transformDatabaseRestaurant(data);
    } catch (error) {
      console.error('❌ Error in getRestaurantById:', error);
      return null;
    }
  }

  /**
   * Transforme les données de la base en format Restaurant
   */
  private transformDatabaseRestaurant(dbRestaurant: any): Restaurant {
    return {
      id: dbRestaurant.user_id,
      user_id: dbRestaurant.user_id,
      business_name: dbRestaurant.business_name,
      business_type: dbRestaurant.business_type,
      description: RestaurantService.extractDescription(dbRestaurant.description),
      logo_url: dbRestaurant.logo_url,
      cover_image_url: dbRestaurant.cover_image_url || dbRestaurant.logo_url,
      address: dbRestaurant.address,
      coordinates: RestaurantService.extractCoordinates(dbRestaurant.coordinates),
      phone: dbRestaurant.phone,
      email: dbRestaurant.email,
      is_verified: dbRestaurant.is_verified,
      is_open: dbRestaurant.is_open,
      opening_hours: dbRestaurant.opening_hours || {},
      average_preparation_time: dbRestaurant.average_preparation_time || 30,
      minimum_order_amount: dbRestaurant.minimum_order_amount || 1000,
      delivery_fee: dbRestaurant.delivery_fee || 1500,
      delivery_radius_km: dbRestaurant.delivery_radius_km || 10,
      rating: dbRestaurant.rating || 4.0,
      total_orders: dbRestaurant.total_orders,
      created_at: dbRestaurant.created_at,
      updated_at: dbRestaurant.updated_at,
    };
  }

  /**
   * Version simplifiée pour extraire les coordonnées
   */
  private extractCoordinatesSimple(coordinates: any): { latitude: number; longitude: number } | undefined {
    if (!coordinates) return undefined;

    try {
      // Si c'est déjà un objet avec lat/lng
      if (coordinates.latitude && coordinates.longitude) {
        return coordinates;
      }

      // Si c'est un format PostGIS POINT
      if (typeof coordinates === 'string') {
        const match = coordinates.match(/POINT\(([^)]+)\)/);
        if (match) {
          const [lng, lat] = match[1].split(' ').map(Number);
          return { latitude: lat, longitude: lng };
        }
      }

      return undefined;
    } catch (error) {
      console.warn('⚠️ Error extracting coordinates:', error);
      return undefined;
    }
  }

  /**
   * Extrait la description depuis un objet ou une chaîne (version robuste)
   */
  private extractDescription(description: any): string | undefined {
    if (!description) return undefined;

    // Si c'est déjà une chaîne, la retourner
    if (typeof description === 'string') {
      return description;
    }

    // Si c'est un objet avec des langues, prendre le français ou l'anglais
    if (typeof description === 'object') {
      try {
        const extracted = description.fr || description.en || description.default || Object.values(description)[0];

        // S'assurer que le résultat est une chaîne
        if (typeof extracted === 'string') {
          return extracted;
        }

        // Si c'est encore un objet, le convertir en JSON
        if (typeof extracted === 'object') {
          console.warn('⚠️ Description nested object, converting to JSON:', extracted);
          return JSON.stringify(extracted);
        }

        // Convertir en chaîne si possible
        return String(extracted);
      } catch (error) {
        console.error('❌ Error extracting description:', error);
        return 'Description non disponible';
      }
    }

    // Fallback : convertir en chaîne
    try {
      return String(description);
    } catch (error) {
      console.error('❌ Error converting description to string:', error);
      return undefined;
    }
  }

  /**
   * Extrait la description depuis un objet ou une chaîne (version statique robuste)
   */
  static extractDescription(description: any): string | undefined {
    if (!description) return undefined;

    // Si c'est déjà une chaîne, la retourner
    if (typeof description === 'string') {
      return description;
    }

    // Si c'est un objet avec des langues, prendre le français ou l'anglais
    if (typeof description === 'object') {
      try {
        const extracted = description.fr || description.en || description.default || Object.values(description)[0];

        // S'assurer que le résultat est une chaîne
        if (typeof extracted === 'string') {
          return extracted;
        }

        // Si c'est encore un objet, le convertir en JSON
        if (typeof extracted === 'object') {
          console.warn('⚠️ Description nested object, converting to JSON:', extracted);
          return JSON.stringify(extracted);
        }

        // Convertir en chaîne si possible
        return String(extracted);
      } catch (error) {
        console.error('❌ Error extracting description:', error);
        return 'Description non disponible';
      }
    }

    // Fallback : convertir en chaîne
    try {
      return String(description);
    } catch (error) {
      console.error('❌ Error converting description to string:', error);
      return undefined;
    }
  }

  /**
   * Génère des coordonnées basées sur l'adresse et l'ID du restaurant
   * Solution temporaire en attendant la géolocalisation des adresses
   */
  private static generateCoordinatesFromAddress(address: string, userId: string): { latitude: number; longitude: number } {
    // Coordonnées de base pour différentes villes africaines
    const cityCoordinates = {
      'Abidjan': { latitude: 5.3600, longitude: -4.0083 },
      'Cotonou': { latitude: 6.3703, longitude: 2.3912 },
      'Dakar': { latitude: 14.6928, longitude: -17.4467 },
      'Bamako': { latitude: 12.6392, longitude: -8.0029 },
      'Ouagadougou': { latitude: 12.3714, longitude: -1.5197 },
      'Lomé': { latitude: 6.1319, longitude: 1.2228 },
      'Accra': { latitude: 5.6037, longitude: -0.1870 },
    };

    // Détecter la ville dans l'adresse
    let baseCoords = { latitude: 5.3600, longitude: -4.0083 }; // Abidjan par défaut

    for (const [city, coords] of Object.entries(cityCoordinates)) {
      if (address?.toLowerCase().includes(city.toLowerCase())) {
        baseCoords = coords;
        break;
      }
    }

    // Générer un offset unique basé sur l'ID du restaurant pour éviter la superposition
    let hash = 0;
    for (let i = 0; i < userId.length; i++) {
      const char = userId.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convertir en 32bit integer
    }
    hash = Math.abs(hash);

    const offsetRange = 0.02; // ~2km de variation
    const latOffset = ((hash % 1000) / 1000 - 0.5) * offsetRange;
    const lngOffset = (((hash * 7) % 1000) / 1000 - 0.5) * offsetRange;

    return {
      latitude: baseCoords.latitude + latOffset,
      longitude: baseCoords.longitude + lngOffset,
    };
  }



  /**
   * Extrait les coordonnées depuis le format PostGIS
   */
  private static extractCoordinates(coordinates: any): { latitude: number; longitude: number } | undefined {
    if (!coordinates) return undefined;

    // Si c'est déjà un objet avec lat/lng
    if (coordinates.latitude && coordinates.longitude) {
      return coordinates;
    }

    // Si c'est un format PostGIS POINT
    if (typeof coordinates === 'string') {
      const match = coordinates.match(/POINT\(([^)]+)\)/);
      if (match) {
        const [lng, lat] = match[1].split(' ').map(Number);
        return { latitude: lat, longitude: lng };
      }
    }

    return undefined;
  }

  /**
   * Calcule la distance entre deux points (formule de Haversine)
   */
  private calculateDistance(
    point1: { latitude: number; longitude: number },
    point2: { latitude: number; longitude: number }
  ): number {
    const R = 6371; // Rayon de la Terre en km
    const dLat = this.toRadians(point2.latitude - point1.latitude);
    const dLon = this.toRadians(point2.longitude - point1.longitude);
    
    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
              Math.cos(this.toRadians(point1.latitude)) * Math.cos(this.toRadians(point2.latitude)) *
              Math.sin(dLon / 2) * Math.sin(dLon / 2);
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }

  /**
   * Convertit les degrés en radians
   */
  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }

  /**
   * Vérifie si un restaurant est disponible
   */
  private isRestaurantAvailable(restaurant: Restaurant): boolean {
    if (!restaurant.is_open) return false;
    
    // Vérifier les heures d'ouverture
    if (restaurant.opening_hours) {
      const now = new Date();
      const currentDay = now.toLocaleDateString('en-US', { weekday: 'short' }).toLowerCase(); // 'mon', 'tue', etc.
      const currentTime = now.getHours() * 60 + now.getMinutes(); // minutes depuis minuit
      
      const daySchedule = restaurant.opening_hours[currentDay];
      if (daySchedule && daySchedule.open && daySchedule.close) {
        const [openHour, openMin] = daySchedule.open.split(':').map(Number);
        const [closeHour, closeMin] = daySchedule.close.split(':').map(Number);
        
        const openTime = openHour * 60 + openMin;
        const closeTime = closeHour * 60 + closeMin;
        
        return currentTime >= openTime && currentTime <= closeTime;
      }
    }
    
    return true; // Par défaut, considérer comme disponible si pas d'horaires
  }

  /**
   * Calcule le temps de livraison estimé
   */
  private calculateEstimatedDeliveryTime(restaurant: Restaurant): number {
    let estimatedTime = restaurant.average_preparation_time || 30;

    // Ajouter le temps de livraison basé sur la distance
    if (restaurant.distance) {
      const deliveryTime = Math.ceil(restaurant.distance * 3); // ~3 min par km
      estimatedTime += deliveryTime;
    } else {
      estimatedTime += 15; // Temps de livraison par défaut
    }

    return estimatedTime;
  }

  /**
   * Generate cache key based on filters
   */
  private generateCacheKey(filters?: RestaurantFilters): string {
    if (!filters) return 'all';

    const keyParts = [];
    if (filters.business_type) keyParts.push(`type:${filters.business_type}`);
    if (filters.is_open !== undefined) keyParts.push(`open:${filters.is_open}`);
    if (filters.is_verified !== undefined) keyParts.push(`verified:${filters.is_verified}`);
    if (filters.min_rating) keyParts.push(`rating:${filters.min_rating}`);
    if (filters.search_query) keyParts.push(`search:${filters.search_query}`);

    return keyParts.length > 0 ? keyParts.join('|') : 'all';
  }

  /**
   * Get data from cache if not expired
   */
  private getFromCache(key: string): Restaurant[] | null {
    const cached = this.cache.get(key);
    if (!cached) return null;

    const isExpired = Date.now() - cached.timestamp > this.CACHE_TTL;
    if (isExpired) {
      this.cache.delete(key);
      return null;
    }

    return cached.data;
  }

  /**
   * Set data in cache
   */
  private setCache(key: string, data: Restaurant[]): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
    });

    // Clean up old cache entries (keep only last 10)
    if (this.cache.size > 10) {
      const oldestKey = this.cache.keys().next().value;
      this.cache.delete(oldestKey);
    }
  }

  /**
   * Clear all cache
   */
  clearCache(): void {
    this.cache.clear();
    console.log('🧹 Restaurant cache cleared');
  }
}

export const restaurantService = new RestaurantService();
