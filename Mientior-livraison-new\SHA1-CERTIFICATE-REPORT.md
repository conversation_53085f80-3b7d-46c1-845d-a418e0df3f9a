# 🔐 RAPPORT D'EMPREINTE SHA-1 - CERTIFICAT DE DÉBOGAGE ANDROID
## Application Mientior Livraison

---

## 📋 INFORMATIONS DU CERTIFICAT

### Configuration du Keystore
- **Emplacement** : `android/app/debug.keystore`
- **Alias** : `androiddebugkey`
- **Store Password** : `android`
- **Key Password** : `android`
- **Type** : Certificat de débogage Android standard

### Informations de l'Application
- **Nom de package** : `com.livraisonafrique.mobile`
- **Namespace** : `com.livraisonafrique.mobile`
- **Version** : 1.0.0 (versionCode: 1)

---

## 🔑 EMPREINTES DU CERTIFICAT

### ⚠️ IMPORTANT : Génération requise

Les empreintes SHA-1 et SHA-256 doivent être générées en utilisant l'une des méthodes suivantes :

### Méthode 1 : Keytool (Recommandée)
```bash
keytool -list -v -keystore android/app/debug.keystore \
        -alias androiddebugkey -storepass android -keypass android
```

### Méthode 2 : Gradle
```bash
cd android
./gradlew signingReport
```

### Méthode 3 : Android Studio
1. Ouvrir le projet Android dans Android Studio
2. Aller dans **Build** > **Generate Signed Bundle/APK**
3. Sélectionner le keystore `android/app/debug.keystore`
4. Cliquer sur **View Certificate**

---

## 🌐 CONFIGURATION GOOGLE CLOUD CONSOLE

### Étapes de Configuration

#### 1. Accès à Google Cloud Console
- URL : https://console.cloud.google.com/
- Projet : Mientior Livraison

#### 2. Navigation vers les Credentials
- **APIs & Services** > **Credentials**
- Sélectionner la clé API : `AIzaSyCUSlG6L03l-nE5SH9Rm8sHQLZRKuRhD3s`

#### 3. Configuration des Restrictions d'Application
```
Type de restriction : Applications Android
Nom de package : com.livraisonafrique.mobile
Empreinte SHA-1 : [À générer avec keytool]
```

#### 4. APIs à Activer
- ✅ Maps SDK for Android
- ✅ Maps SDK for iOS
- ✅ Places API
- ✅ Directions API
- ✅ Geocoding API
- ✅ Geolocation API

---

## 🔧 COMMANDES DE GÉNÉRATION

### Windows (PowerShell)
```powershell
# Avec Java JDK installé
& "$env:JAVA_HOME\bin\keytool.exe" -list -v -keystore android\app\debug.keystore -alias androiddebugkey -storepass android -keypass android

# Avec Android Studio
& "C:\Program Files\Android\Android Studio\jre\bin\keytool.exe" -list -v -keystore android\app\debug.keystore -alias androiddebugkey -storepass android -keypass android
```

### Linux/macOS
```bash
# Avec Java JDK installé
keytool -list -v -keystore android/app/debug.keystore -alias androiddebugkey -storepass android -keypass android

# Avec Android SDK
$ANDROID_HOME/build-tools/*/keytool -list -v -keystore android/app/debug.keystore -alias androiddebugkey -storepass android -keypass android
```

### Gradle (Toutes plateformes)
```bash
cd android
./gradlew signingReport
```

---

## 📊 FORMAT ATTENDU DE L'EMPREINTE

### SHA-1 (Format attendu)
```
SHA1: XX:XX:XX:XX:XX:XX:XX:XX:XX:XX:XX:XX:XX:XX:XX:XX:XX:XX:XX:XX
```

### SHA-256 (Format attendu)
```
SHA256: XX:XX:XX:XX:XX:XX:XX:XX:XX:XX:XX:XX:XX:XX:XX:XX:XX:XX:XX:XX:XX:XX:XX:XX:XX:XX:XX:XX:XX:XX:XX:XX
```

---

## ✅ CHECKLIST DE VÉRIFICATION

### Avant Configuration Google Cloud
- [ ] Keystore `android/app/debug.keystore` existe
- [ ] Nom de package `com.livraisonafrique.mobile` confirmé
- [ ] Empreinte SHA-1 générée avec succès
- [ ] Empreinte SHA-256 générée (optionnel)

### Configuration Google Cloud Console
- [ ] Clé API `AIzaSyCUSlG6L03l-nE5SH9Rm8sHQLZRKuRhD3s` sélectionnée
- [ ] Restrictions d'application configurées
- [ ] Nom de package ajouté : `com.livraisonafrique.mobile`
- [ ] Empreinte SHA-1 ajoutée
- [ ] Toutes les APIs Maps activées

### Test de Fonctionnement
- [ ] Application compilée sans erreur
- [ ] Carte Google Maps s'affiche correctement
- [ ] Aucune erreur d'authentification dans les logs
- [ ] Marqueurs de restaurants visibles sur la carte

---

## 🚨 DÉPANNAGE

### Erreurs Communes

#### "API key not valid"
- Vérifier que la clé API est correcte
- Confirmer que les APIs Maps sont activées
- Vérifier les restrictions d'application

#### "This app is not authorized"
- Vérifier le nom de package dans les restrictions
- Confirmer l'empreinte SHA-1 dans Google Cloud Console
- Vérifier que le certificat utilisé correspond

#### Carte vierge/blanche
- Vérifier la connectivité Internet
- Confirmer les permissions de localisation
- Vérifier les logs pour les erreurs d'API

---

## 📞 SUPPORT

### Ressources Utiles
- [Documentation Google Maps Android](https://developers.google.com/maps/documentation/android-sdk)
- [Configuration des clés API](https://developers.google.com/maps/documentation/android-sdk/get-api-key)
- [Dépannage React Native Maps](https://github.com/react-native-maps/react-native-maps/blob/master/docs/troubleshooting.md)

### Commandes de Diagnostic
```bash
# Vérifier la configuration Gradle
cd android && ./gradlew :app:dependencies

# Vérifier les permissions dans le manifest
grep -n "permission" android/app/src/main/AndroidManifest.xml

# Vérifier la configuration de la clé API
grep -r "AIzaSyCUSlG6L03l-nE5SH9Rm8sHQLZRKuRhD3s" .
```

---

**Date de génération** : 2025-06-20  
**Version** : 1.0  
**Statut** : En attente de génération SHA-1
