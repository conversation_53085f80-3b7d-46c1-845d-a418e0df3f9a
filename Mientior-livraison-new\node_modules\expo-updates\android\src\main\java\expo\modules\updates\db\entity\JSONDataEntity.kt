package expo.modules.updates.db.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.Index
import androidx.room.PrimaryKey
import java.util.*

/**
 * Data class representing a (potential) row of arbitrary JSON data to be stored in the `json_data`
 * table in SQLite. The table schema is autogenerated from this file.
 *
 * Used to store per-scope data that should be persisted and overridden via key, such as items in
 * ManifestMetadata.
 *
 * The `scopeKey` field is only relevant in environments such as Expo Go in which updates from
 * multiple scopes can be launched.
 */
@Entity(tableName = "json_data", indices = [Index(value = ["scope_key"])])
class JSONDataEntity(
  var key: String,
  var value: String,
  @field:ColumnInfo(name = "last_updated") var lastUpdated: Date,
  @field:ColumnInfo(name = "scope_key") var scopeKey: String
) {
  @PrimaryKey(autoGenerate = true) // 0 is treated as unset while inserting the entity into the db
  var id: Long = 0
}
