// 🔍 Monitoring Service - Mientior Livraison
// Comprehensive monitoring, error tracking, and performance monitoring

import AsyncStorage from '@react-native-async-storage/async-storage';
import { supabase } from './supabase';
import { Analytics } from './analytics';

// Types for monitoring
export interface ErrorReport {
  id: string;
  error_type: 'crash' | 'api_error' | 'network_error' | 'validation_error' | 'payment_error';
  message: string;
  stack?: string;
  context: string;
  user_id?: string;
  session_id: string;
  timestamp: string;
  app_version: string;
  device_info: DeviceInfo;
  additional_data?: Record<string, any>;
}

export interface PerformanceReport {
  id: string;
  metric_type: 'app_load' | 'screen_load' | 'api_response' | 'payment_process' | 'search_query';
  metric_name: string;
  value: number;
  unit: 'ms' | 'seconds' | 'bytes' | 'count';
  context: string;
  timestamp: string;
  user_id?: string;
  session_id: string;
  additional_data?: Record<string, any>;
}

export interface DeviceInfo {
  platform: string;
  os_version: string;
  app_version: string;
  device_model?: string;
  screen_resolution?: string;
  network_type?: string;
  battery_level?: number;
  memory_usage?: number;
}

export interface BusinessAlert {
  id: string;
  alert_type: 'high_error_rate' | 'payment_failure' | 'delivery_delay' | 'low_performance';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  threshold_value: number;
  current_value: number;
  timestamp: string;
  resolved: boolean;
}

// Monitoring Service Class
class MonitoringService {
  private isInitialized: boolean = false;
  private sessionId: string = '';
  private userId: string | null = null;
  private deviceInfo: DeviceInfo | null = null;
  private errorQueue: ErrorReport[] = [];
  private performanceQueue: PerformanceReport[] = [];
  private isOnline: boolean = true;
  private hasLoggedTableWarning: boolean = false;

  // Initialize monitoring service
  async initialize(userId?: string): Promise<void> {
    try {
      this.sessionId = await this.getSessionId();
      this.userId = userId || null;
      this.deviceInfo = await this.collectDeviceInfo();
      this.isInitialized = true;

      // Set up global error handler
      this.setupGlobalErrorHandler();

      // Process any queued reports
      await this.processQueues();

      console.log('🔍 Monitoring service initialized');
    } catch (error) {
      console.error('❌ Failed to initialize monitoring:', error);
    }
  }

  // Get session ID from analytics or generate new one
  private async getSessionId(): Promise<string> {
    try {
      const sessionId = await AsyncStorage.getItem('analytics_session_id');
      return sessionId || `monitor_${Date.now()}_${Math.random().toString(36).substring(2)}`;
    } catch {
      return `monitor_${Date.now()}_${Math.random().toString(36).substring(2)}`;
    }
  }

  // Collect device information
  private async collectDeviceInfo(): Promise<DeviceInfo> {
    try {
      // In a real implementation, you would use expo-device, expo-constants, etc.
      return {
        platform: 'mobile', // Platform.OS
        os_version: 'unknown', // Device.osVersion
        app_version: '1.0.0', // Constants.expoConfig?.version
        device_model: 'unknown', // Device.modelName
        screen_resolution: 'unknown', // Dimensions.get('screen')
        network_type: 'unknown', // NetInfo.fetch()
      };
    } catch (error) {
      console.error('❌ Failed to collect device info:', error);
      return {
        platform: 'unknown',
        os_version: 'unknown',
        app_version: '1.0.0',
      };
    }
  }

  // Set up global error handler
  private setupGlobalErrorHandler(): void {
    // React Native global error handler
    const globalWithErrorUtils = global as any;
    const originalHandler = globalWithErrorUtils.ErrorUtils?.getGlobalHandler();

    if (globalWithErrorUtils.ErrorUtils) {
      globalWithErrorUtils.ErrorUtils.setGlobalHandler((error: Error, isFatal: boolean) => {
        this.reportError(error, 'global_error_handler', {
          is_fatal: isFatal,
        });

        // Call original handler
        if (originalHandler) {
          originalHandler(error, isFatal);
        }
      });
    }

    // Promise rejection handler
    const globalWithRejection = global as any;
    const originalRejectionHandler = globalWithRejection.onunhandledrejection;
    globalWithRejection.onunhandledrejection = (event: any) => {
      this.reportError(
        new Error(event.reason || 'Unhandled promise rejection'),
        'unhandled_promise_rejection',
        { reason: event.reason }
      );

      if (originalRejectionHandler) {
        originalRejectionHandler.call(globalWithRejection, event);
      }
    };
  }

  // Report errors
  async reportError(
    error: Error,
    context: string,
    additionalData: Record<string, any> = {},
    errorType: ErrorReport['error_type'] = 'crash'
  ): Promise<void> {
    // Completely disable error reporting in development mode to reduce noise
    if (__DEV__) {
      return;
    }

    try {
      const errorReport: ErrorReport = {
        id: `error_${Date.now()}_${Math.random().toString(36).substring(2)}`,
        error_type: errorType,
        message: error.message,
        stack: error.stack,
        context,
        user_id: this.userId || undefined,
        session_id: this.sessionId,
        timestamp: new Date().toISOString(),
        app_version: this.deviceInfo?.app_version || '1.0.0',
        device_info: this.deviceInfo!,
        additional_data: additionalData,
      };

      if (this.isOnline) {
        await this.sendErrorReport(errorReport);
      } else {
        this.errorQueue.push(errorReport);
        await this.saveErrorQueue();
      }

      // Also track in analytics (but don't await to avoid cascading failures)
      Analytics.error(error, context, additionalData).catch(() => {
        // Silently ignore analytics failures
      });
    } catch (reportError) {
      // Silently fail for now - monitoring may not be fully set up
      if (__DEV__) {
        console.warn('⚠️ Error reporting failed:', reportError);
      }
    }
  }

  // Send error report to Supabase
  private async sendErrorReport(report: ErrorReport): Promise<void> {
    try {
      if (!supabase) {
        throw new Error('Supabase client not available');
      }

      const { error } = await supabase
        .from('error_reports')
        .insert(report);

      if (error) throw error;
    } catch (error) {
      // Silently fail for now - monitoring tables may not exist yet
      if (__DEV__) {
        console.warn('⚠️ Error report failed (table may not exist):', report.context);
      }
      // Don't re-queue to avoid infinite loops
    }
  }

  // Report performance metrics
  async reportPerformance(
    metricType: PerformanceReport['metric_type'],
    metricName: string,
    value: number,
    unit: PerformanceReport['unit'] = 'ms',
    context: string = '',
    additionalData: Record<string, any> = {}
  ): Promise<void> {
    // Disable performance monitoring in development to reduce noise
    // Tables need to be created manually in Supabase Dashboard
    if (__DEV__) {
      // Only log in development for debugging, but don't send to database
      console.log(`📊 Performance: ${metricName} = ${value}${unit} (${context})`);
      return;
    }

    try {
      const performanceReport: PerformanceReport = {
        id: `perf_${Date.now()}_${Math.random().toString(36).substring(2)}`,
        metric_type: metricType,
        metric_name: metricName,
        value,
        unit,
        context,
        timestamp: new Date().toISOString(),
        user_id: this.userId || undefined,
        session_id: this.sessionId,
        additional_data: additionalData,
      };

      if (this.isOnline) {
        await this.sendPerformanceReport(performanceReport);
      } else {
        this.performanceQueue.push(performanceReport);
        await this.savePerformanceQueue();
      }

      // Also track in analytics (but don't await to avoid cascading failures)
      Analytics.performance(metricName, value, { unit, context, ...additionalData }).catch(() => {
        // Silently ignore analytics failures
      });
    } catch (error) {
      // Silently fail for now - monitoring may not be fully set up
      if (__DEV__) {
        console.warn('⚠️ Performance reporting failed:', error);
      }
    }
  }

  // Send performance report to Supabase
  private async sendPerformanceReport(report: PerformanceReport): Promise<void> {
    try {
      if (!supabase) {
        throw new Error('Supabase client not available');
      }

      const { error } = await supabase
        .from('performance_reports')
        .insert(report);

      if (error) throw error;
    } catch (error) {
      // Silently fail for now - monitoring tables may not exist yet
      if (__DEV__) {
        // Only log once per session to avoid spam
        if (!this.hasLoggedTableWarning) {
          console.warn('⚠️ Performance monitoring disabled - database tables not set up yet');
          console.log('💡 To enable: Create tables in Supabase Dashboard > SQL Editor');
          this.hasLoggedTableWarning = true;
        }
      }
      // Don't re-queue to avoid infinite loops
    }
  }

  // Save error queue to storage
  private async saveErrorQueue(): Promise<void> {
    try {
      await AsyncStorage.setItem('monitoring_error_queue', JSON.stringify(this.errorQueue));
    } catch (error) {
      console.error('❌ Failed to save error queue:', error);
    }
  }

  // Save performance queue to storage
  private async savePerformanceQueue(): Promise<void> {
    try {
      await AsyncStorage.setItem('monitoring_performance_queue', JSON.stringify(this.performanceQueue));
    } catch (error) {
      console.error('❌ Failed to save performance queue:', error);
    }
  }

  // Process queued reports
  private async processQueues(): Promise<void> {
    try {
      // Process error queue
      const savedErrorQueue = await AsyncStorage.getItem('monitoring_error_queue');
      if (savedErrorQueue) {
        const errors: ErrorReport[] = JSON.parse(savedErrorQueue);
        this.errorQueue = [...this.errorQueue, ...errors];
      }

      for (const errorReport of this.errorQueue) {
        await this.sendErrorReport(errorReport);
      }
      this.errorQueue = [];
      await AsyncStorage.removeItem('monitoring_error_queue');

      // Process performance queue
      const savedPerfQueue = await AsyncStorage.getItem('monitoring_performance_queue');
      if (savedPerfQueue) {
        const reports: PerformanceReport[] = JSON.parse(savedPerfQueue);
        this.performanceQueue = [...this.performanceQueue, ...reports];
      }

      for (const perfReport of this.performanceQueue) {
        await this.sendPerformanceReport(perfReport);
      }
      this.performanceQueue = [];
      await AsyncStorage.removeItem('monitoring_performance_queue');
    } catch (error) {
      // Silently fail for now - monitoring may not be fully set up
      if (__DEV__) {
        console.warn('⚠️ Monitoring queue processing failed:', error);
      }
    }
  }

  // Monitor API calls
  async monitorApiCall<T>(
    apiCall: () => Promise<T>,
    endpoint: string,
    context: string = ''
  ): Promise<T> {
    const startTime = Date.now();
    
    try {
      const result = await apiCall();
      const duration = Date.now() - startTime;
      
      // Report successful API call performance
      await this.reportPerformance(
        'api_response',
        `api_${endpoint}`,
        duration,
        'ms',
        context,
        { endpoint, success: true }
      );
      
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      
      // Report API error
      await this.reportError(
        error as Error,
        `api_call_${endpoint}`,
        { endpoint, duration, context },
        'api_error'
      );
      
      throw error;
    }
  }

  // Monitor screen load time
  async monitorScreenLoad(screenName: string, loadFunction: () => Promise<void>): Promise<void> {
    const startTime = Date.now();
    
    try {
      await loadFunction();
      const loadTime = Date.now() - startTime;
      
      await this.reportPerformance(
        'screen_load',
        `screen_${screenName}`,
        loadTime,
        'ms',
        screenName,
        { screen_name: screenName }
      );
    } catch (error) {
      await this.reportError(
        error as Error,
        `screen_load_${screenName}`,
        { screen_name: screenName },
        'crash'
      );
      throw error;
    }
  }

  // Check for business alerts
  async checkBusinessAlerts(): Promise<BusinessAlert[]> {
    try {
      if (!supabase) {
        console.warn('⚠️ Supabase client not available for business alerts');
        return [];
      }

      const { data, error } = await supabase
        .from('business_alerts')
        .select('*')
        .eq('resolved', false)
        .order('timestamp', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('❌ Failed to check business alerts:', error);
      return [];
    }
  }

  // Set network status
  setNetworkStatus(isOnline: boolean): void {
    this.isOnline = isOnline;
    
    if (isOnline && (this.errorQueue.length > 0 || this.performanceQueue.length > 0)) {
      this.processQueues();
    }
  }

  // Get monitoring summary
  async getMonitoringSummary(timeframe: '1h' | '24h' | '7d' = '24h'): Promise<any> {
    try {
      if (!supabase) {
        console.warn('⚠️ Supabase client not available for monitoring summary');
        return null;
      }

      const { data, error } = await supabase
        .rpc('get_monitoring_summary', {
          user_id: this.userId,
          timeframe,
        });

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('❌ Failed to get monitoring summary:', error);
      return null;
    }
  }

  // Reset monitoring (for logout)
  async reset(): Promise<void> {
    this.userId = null;
    this.errorQueue = [];
    this.performanceQueue = [];
    await AsyncStorage.removeItem('monitoring_error_queue');
    await AsyncStorage.removeItem('monitoring_performance_queue');
  }
}

// Export singleton instance
export const monitoringService = new MonitoringService();

// Convenience functions
export const Monitoring = {
  // Initialize
  init: (userId?: string) => monitoringService.initialize(userId),
  
  // Error reporting
  error: (error: Error, context: string, additionalData?: Record<string, any>, errorType?: ErrorReport['error_type']) =>
    monitoringService.reportError(error, context, additionalData, errorType),
  
  // Performance monitoring
  performance: (
    metricType: PerformanceReport['metric_type'],
    metricName: string,
    value: number,
    unit?: PerformanceReport['unit'],
    context?: string,
    additionalData?: Record<string, any>
  ) => monitoringService.reportPerformance(metricType, metricName, value, unit, context, additionalData),
  
  // API monitoring
  api: <T>(apiCall: () => Promise<T>, endpoint: string, context?: string) =>
    monitoringService.monitorApiCall(apiCall, endpoint, context),
  
  // Screen monitoring
  screen: (screenName: string, loadFunction: () => Promise<void>) =>
    monitoringService.monitorScreenLoad(screenName, loadFunction),
  
  // Business alerts
  alerts: () => monitoringService.checkBusinessAlerts(),
  
  // Network status
  setOnline: (isOnline: boolean) => monitoringService.setNetworkStatus(isOnline),
  
  // Utilities
  getSummary: (timeframe?: '1h' | '24h' | '7d') => monitoringService.getMonitoringSummary(timeframe),
  reset: () => monitoringService.reset(),
};

export default Monitoring;
