{"version": 3, "file": "UseUpdatesUtils.js", "sourceRoot": "", "sources": ["../src/UseUpdatesUtils.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,OAAO,MAAM,WAAW,CAAC;AAMrC,OAAO,EACL,cAAc,GAIf,MAAM,oBAAoB,CAAC;AAE5B,iEAAiE;AACjE,MAAM,CAAC,MAAM,gBAAgB,GAAyB;IACpD,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,SAAS;IACvC,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,SAAS;IACrC,SAAS,EAAE,OAAO,CAAC,SAAS,IAAI,SAAS;IACzC,cAAc,EAAE,OAAO,CAAC,cAAc,IAAI,SAAS;IACnD,gBAAgB,EAAE,OAAO,CAAC,gBAAgB;IAC1C,iBAAiB,EAAE,OAAO,CAAC,iBAAiB;IAC5C,qBAAqB,EAAE,OAAO,CAAC,qBAAqB;IACpD,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,SAAS;IACvC,cAAc,EAAE,OAAO,CAAC,cAAc,IAAI,SAAS;CACpD,CAAC;AAEF,2CAA2C;AAC3C,MAAM,CAAC,MAAM,kBAAkB,GAAoD,CAAC,QAAQ,EAAE,EAAE;IAC9F,OAAO;QACL,IAAI,EAAE,cAAc,CAAC,GAAG;QACxB,QAAQ,EAAE,QAAQ,CAAC,EAAE,IAAI,EAAE;QAC3B,SAAS,EACP,QAAQ,IAAI,WAAW,IAAI,QAAQ,IAAI,QAAQ,CAAC,SAAS;YACvD,CAAC,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC;YAC9B,CAAC,CAAC,6EAA6E;gBAC7E,wDAAwD;gBACxD,IAAI,IAAI,CAAC,CAAC,CAAC;QACjB,QAAQ;KACT,CAAC;AACJ,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,kBAAkB,GAAyD,CACtF,QAAQ,EACR,EAAE,CAAC,CAAC;IACJ,IAAI,EAAE,cAAc,CAAC,QAAQ;IAC7B,SAAS,EAAE,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC;IACxC,QAAQ,EAAE,SAAS;IACnB,QAAQ,EAAE,SAAS;CACpB,CAAC,CAAC;AAEH,yEAAyE;AACzE,MAAM,CAAC,MAAM,uBAAuB,GAEkB,CAAC,OAAO,EAAE,EAAE;IAChE,MAAM,eAAe,GAAG,OAAO,EAAE,cAAc;QAC7C,CAAC,CAAC,kBAAkB,CAAC,OAAO,EAAE,cAAc,CAAC;QAC7C,CAAC,CAAC,OAAO,CAAC,QAAQ;YAChB,CAAC,CAAC,kBAAkB,CAAC,OAAO,CAAC,QAAQ,CAAC;YACtC,CAAC,CAAC,SAAS,CAAC;IAChB,MAAM,gBAAgB,GAAG,OAAO,EAAE,kBAAkB;QAClD,CAAC,CAAC,kBAAkB,CAAC,OAAO,EAAE,kBAAkB,CAAC;QACjD,CAAC,CAAC,OAAO,CAAC,QAAQ;YAChB,CAAC,CAAC,kBAAkB,CAAC,OAAO,CAAC,QAAQ,CAAC;YACtC,CAAC,CAAC,SAAS,CAAC;IAChB,OAAO;QACL,yBAAyB,EAAE,OAAO,CAAC,yBAAyB;QAC5D,iBAAiB,EAAE,OAAO,CAAC,iBAAiB;QAC5C,eAAe,EAAE,OAAO,CAAC,eAAe;QACxC,UAAU,EAAE,OAAO,CAAC,UAAU;QAC9B,aAAa,EAAE,OAAO,CAAC,aAAa;QACpC,YAAY,EAAE,OAAO,CAAC,YAAY;QAClC,YAAY,EAAE,OAAO,CAAC,YAAY;QAClC,eAAe;QACf,gBAAgB;QAChB,UAAU,EAAE,OAAO,CAAC,UAAU;QAC9B,aAAa,EAAE,OAAO,CAAC,aAAa;QACpC,kCAAkC,EAAE,OAAO,CAAC,sBAAsB;KACnE,CAAC;AACJ,CAAC,CAAC", "sourcesContent": ["import * as Updates from './Updates';\nimport type {\n  Manifest,\n  UpdatesNativeStateMachineContext,\n  UpdatesNativeStateRollback,\n} from './Updates.types';\nimport {\n  UpdateInfoType,\n  type CurrentlyRunningInfo,\n  type UpdateInfo,\n  type UseUpdatesReturnType,\n} from './UseUpdates.types';\n\n// The currently running info, constructed from Updates constants\nexport const currentlyRunning: CurrentlyRunningInfo = {\n  updateId: Updates.updateId ?? undefined,\n  channel: Updates.channel ?? undefined,\n  createdAt: Updates.createdAt ?? undefined,\n  launchDuration: Updates.launchDuration ?? undefined,\n  isEmbeddedLaunch: Updates.isEmbeddedLaunch,\n  isEmergencyLaunch: Updates.isEmergencyLaunch,\n  emergencyLaunchReason: Updates.emergencyLaunchReason,\n  manifest: Updates.manifest ?? undefined,\n  runtimeVersion: Updates.runtimeVersion ?? undefined,\n};\n\n// Constructs an UpdateInfo from a manifest\nexport const updateFromManifest: (manifest: NonNullable<Manifest>) => UpdateInfo = (manifest) => {\n  return {\n    type: UpdateInfoType.NEW,\n    updateId: manifest.id ?? '',\n    createdAt:\n      manifest && 'createdAt' in manifest && manifest.createdAt\n        ? new Date(manifest.createdAt)\n        : // We should never reach this if the manifest is valid and has a commit time,\n          // but leave this in so that createdAt is always defined\n          new Date(0),\n    manifest,\n  };\n};\n\nexport const updateFromRollback: (rollback: UpdatesNativeStateRollback) => UpdateInfo = (\n  rollback\n) => ({\n  type: UpdateInfoType.ROLLBACK,\n  createdAt: new Date(rollback.commitTime),\n  manifest: undefined,\n  updateId: undefined,\n});\n\n// Transform the useUpdates() state based on native state machine context\nexport const updatesStateFromContext: (\n  context: UpdatesNativeStateMachineContext\n) => Omit<UseUpdatesReturnType, 'currentlyRunning'> = (context) => {\n  const availableUpdate = context?.latestManifest\n    ? updateFromManifest(context?.latestManifest)\n    : context.rollback\n      ? updateFromRollback(context.rollback)\n      : undefined;\n  const downloadedUpdate = context?.downloadedManifest\n    ? updateFromManifest(context?.downloadedManifest)\n    : context.rollback\n      ? updateFromRollback(context.rollback)\n      : undefined;\n  return {\n    isStartupProcedureRunning: context.isStartupProcedureRunning,\n    isUpdateAvailable: context.isUpdateAvailable,\n    isUpdatePending: context.isUpdatePending,\n    isChecking: context.isChecking,\n    isDownloading: context.isDownloading,\n    isRestarting: context.isRestarting,\n    restartCount: context.restartCount,\n    availableUpdate,\n    downloadedUpdate,\n    checkError: context.checkError,\n    downloadError: context.downloadError,\n    lastCheckForUpdateTimeSinceRestart: context.lastCheckForUpdateTime,\n  };\n};\n"]}