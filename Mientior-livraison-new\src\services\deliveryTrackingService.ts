import { supabase } from './supabase';
import * as Location from 'expo-location';
import { AppState } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
// Import conditionnel pour expo-battery
let Battery: any = null;
try {
  Battery = require('expo-battery');
} catch (error) {
  console.warn('expo-battery not available, battery level tracking disabled');
}

export interface DeliveryTrackingPoint {
  id: string;
  delivery_id: string;
  order_id: string;
  driver_id: string;
  current_location: {
    latitude: number;
    longitude: number;
  };
  heading?: number;
  speed?: number;
  accuracy?: number;
  battery_level?: number;
  is_online: boolean;
  estimated_arrival?: string;
  distance_to_destination?: number;
  created_at: string;
}

export interface TrackingConfig {
  updateInterval: number; // en millisecondes
  highAccuracy: boolean;
  distanceFilter: number; // en mètres
  enableBackgroundUpdates: boolean;
}

const DEFAULT_CONFIG: TrackingConfig = {
  updateInterval: 15000, // 15 secondes
  highAccuracy: true,
  distanceFilter: 10, // 10 mètres
  enableBackgroundUpdates: true,
};

const TRACKING_CACHE_KEY = 'delivery_tracking_cache';

class DeliveryTrackingService {
  private trackingInterval: NodeJS.Timeout | null = null;
  private currentDeliveryId: string | null = null;
  private currentOrderId: string | null = null;
  private isTracking = false;
  private config: TrackingConfig = DEFAULT_CONFIG;
  private lastKnownLocation: Location.LocationObject | null = null;

  /**
   * Démarre le tracking pour une livraison
   */
  async startTracking(
    deliveryId: string,
    orderId: string,
    driverId: string,
    config?: Partial<TrackingConfig>
  ): Promise<boolean> {
    try {
      console.log('🚚 Starting delivery tracking for:', deliveryId);

      // Vérifier les permissions
      const hasPermission = await this.requestLocationPermissions();
      if (!hasPermission) {
        throw new Error('Permission de localisation refusée');
      }

      // Configurer le tracking
      this.config = { ...DEFAULT_CONFIG, ...config };
      this.currentDeliveryId = deliveryId;
      this.currentOrderId = orderId;
      this.isTracking = true;

      // Obtenir la position initiale
      const initialLocation = await this.getCurrentLocation();
      if (initialLocation) {
        await this.saveTrackingPoint(driverId, initialLocation);
      }

      // Démarrer le tracking périodique
      this.startPeriodicTracking(driverId);

      // Écouter les changements d'état de l'app
      AppState.addEventListener('change', this.handleAppStateChange.bind(this));

      console.log('✅ Delivery tracking started successfully');
      return true;

    } catch (error) {
      console.error('❌ Error starting tracking:', error);
      return false;
    }
  }

  /**
   * Arrête le tracking
   */
  async stopTracking(): Promise<void> {
    try {
      console.log('🚚 Stopping delivery tracking');

      this.isTracking = false;
      this.currentDeliveryId = null;
      this.currentOrderId = null;

      if (this.trackingInterval) {
        clearInterval(this.trackingInterval);
        this.trackingInterval = null;
      }

      // Arrêter le tracking en arrière-plan
      await Location.stopLocationUpdatesAsync('deliveryTracking');

      console.log('✅ Delivery tracking stopped');

    } catch (error) {
      console.error('❌ Error stopping tracking:', error);
    }
  }

  /**
   * Met à jour la position manuellement
   */
  async updateLocation(driverId: string): Promise<boolean> {
    try {
      if (!this.isTracking || !this.currentDeliveryId) {
        return false;
      }

      const location = await this.getCurrentLocation();
      if (location) {
        await this.saveTrackingPoint(driverId, location);
        return true;
      }

      return false;
    } catch (error) {
      console.error('❌ Error updating location:', error);
      return false;
    }
  }

  /**
   * Récupère l'historique de tracking d'une livraison
   */
  async getTrackingHistory(deliveryId: string): Promise<DeliveryTrackingPoint[]> {
    try {
      console.log('🚚 Fetching tracking history for:', deliveryId);

      if (!supabase) {
        return this.getCachedTrackingHistory(deliveryId);
      }

      const { data, error } = await supabase
        .from('delivery_tracking_with_distance')
        .select('*')
        .eq('delivery_id', deliveryId)
        .order('created_at', { ascending: true });

      if (error) {
        console.error('❌ Error fetching tracking history:', error);
        return this.getCachedTrackingHistory(deliveryId);
      }

      const trackingPoints = data?.map(this.transformDatabaseTrackingPoint) || [];
      
      // Cache l'historique
      await this.cacheTrackingHistory(deliveryId, trackingPoints);
      
      console.log(`✅ ${trackingPoints.length} tracking points fetched`);
      return trackingPoints;

    } catch (error) {
      console.error('❌ Error in getTrackingHistory:', error);
      return this.getCachedTrackingHistory(deliveryId);
    }
  }

  /**
   * Récupère la dernière position d'une livraison
   */
  async getLatestLocation(deliveryId: string): Promise<DeliveryTrackingPoint | null> {
    try {
      const history = await this.getTrackingHistory(deliveryId);
      return history.length > 0 ? history[history.length - 1] : null;
    } catch (error) {
      console.error('❌ Error getting latest location:', error);
      return null;
    }
  }

  /**
   * Calcule la distance et l'ETA vers une destination
   */
  async calculateDistanceAndETA(
    currentLocation: { latitude: number; longitude: number },
    destination: { latitude: number; longitude: number }
  ): Promise<{ distance: number; eta: number }> {
    try {
      // Calcul de distance simple (à vol d'oiseau)
      const distance = this.calculateHaversineDistance(currentLocation, destination);
      
      // Estimation ETA basée sur une vitesse moyenne de 30 km/h en ville
      const averageSpeed = 30; // km/h
      const eta = (distance / averageSpeed) * 60; // en minutes

      return { distance, eta };
    } catch (error) {
      console.error('❌ Error calculating distance and ETA:', error);
      return { distance: 0, eta: 0 };
    }
  }

  /**
   * Demande les permissions de localisation
   */
  private async requestLocationPermissions(): Promise<boolean> {
    try {
      const { status: foregroundStatus } = await Location.requestForegroundPermissionsAsync();
      
      if (foregroundStatus !== 'granted') {
        console.log('Permission de localisation en premier plan refusée');
        return false;
      }

      if (this.config.enableBackgroundUpdates) {
        const { status: backgroundStatus } = await Location.requestBackgroundPermissionsAsync();
        
        if (backgroundStatus !== 'granted') {
          console.log('Permission de localisation en arrière-plan refusée');
          // Continuer sans le tracking en arrière-plan
        }
      }

      return true;
    } catch (error) {
      console.error('❌ Error requesting location permissions:', error);
      return false;
    }
  }

  /**
   * Obtient la position actuelle
   */
  private async getCurrentLocation(): Promise<Location.LocationObject | null> {
    try {
      const location = await Location.getCurrentPositionAsync({
        accuracy: this.config.highAccuracy ? Location.Accuracy.High : Location.Accuracy.Balanced,
      });

      this.lastKnownLocation = location;
      return location;
    } catch (error) {
      console.error('❌ Error getting current location:', error);
      return this.lastKnownLocation;
    }
  }

  /**
   * Sauvegarde un point de tracking
   */
  private async saveTrackingPoint(driverId: string, location: Location.LocationObject): Promise<void> {
    try {
      if (!supabase || !this.currentDeliveryId || !this.currentOrderId) {
        return;
      }

      // Obtenir le niveau de batterie
      const batteryLevel = await this.getBatteryLevel();

      // Calculer la distance vers la destination si possible
      // (nécessiterait l'adresse de destination)

      const trackingData = {
        delivery_id: this.currentDeliveryId,
        order_id: this.currentOrderId,
        driver_id: driverId,
        current_location: `POINT(${location.coords.longitude} ${location.coords.latitude})`,
        heading: location.coords.heading,
        speed: location.coords.speed,
        accuracy: location.coords.accuracy,
        battery_level: batteryLevel,
        is_online: true,
      };

      const { error } = await supabase
        .from('delivery_tracking_realtime')
        .insert([trackingData]);

      if (error) {
        console.error('❌ Error saving tracking point:', error);
      } else {
        console.log('✅ Tracking point saved successfully');
      }

    } catch (error) {
      console.error('❌ Error in saveTrackingPoint:', error);
    }
  }

  /**
   * Démarre le tracking périodique
   */
  private startPeriodicTracking(driverId: string): void {
    if (this.trackingInterval) {
      clearInterval(this.trackingInterval);
    }

    this.trackingInterval = setInterval(async () => {
      if (this.isTracking) {
        await this.updateLocation(driverId);
      }
    }, this.config.updateInterval);
  }

  /**
   * Gère les changements d'état de l'app
   */
  private handleAppStateChange(nextAppState: string): void {
    if (nextAppState === 'background' && this.config.enableBackgroundUpdates) {
      // Configurer le tracking en arrière-plan
      this.setupBackgroundTracking();
    } else if (nextAppState === 'active' && this.isTracking) {
      // Reprendre le tracking en premier plan
      this.resumeForegroundTracking();
    }
  }

  /**
   * Configure le tracking en arrière-plan
   */
  private async setupBackgroundTracking(): Promise<void> {
    try {
      await Location.startLocationUpdatesAsync('deliveryTracking', {
        accuracy: Location.Accuracy.Balanced,
        timeInterval: this.config.updateInterval,
        distanceInterval: this.config.distanceFilter,
        foregroundService: {
          notificationTitle: 'Livraison en cours',
          notificationBody: 'Suivi de votre position pour la livraison',
        },
      });
    } catch (error) {
      console.error('❌ Error setting up background tracking:', error);
    }
  }

  /**
   * Reprend le tracking en premier plan
   */
  private resumeForegroundTracking(): void {
    // Le tracking périodique continue automatiquement
    console.log('🚚 Resuming foreground tracking');
  }

  /**
   * Obtient le niveau de batterie
   */
  private async getBatteryLevel(): Promise<number> {
    try {
      if (!Battery) {
        return 100; // Valeur par défaut si expo-battery n'est pas disponible
      }

      const batteryLevel = await Battery.getBatteryLevelAsync();
      return Math.round(batteryLevel * 100);
    } catch (error) {
      console.warn('⚠️ Could not get battery level:', error);
      return 100; // Valeur par défaut
    }
  }

  /**
   * Calcule la distance entre deux points (formule de Haversine)
   */
  private calculateHaversineDistance(
    point1: { latitude: number; longitude: number },
    point2: { latitude: number; longitude: number }
  ): number {
    const R = 6371; // Rayon de la Terre en km
    const dLat = this.toRadians(point2.latitude - point1.latitude);
    const dLon = this.toRadians(point2.longitude - point1.longitude);
    
    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
              Math.cos(this.toRadians(point1.latitude)) * Math.cos(this.toRadians(point2.latitude)) *
              Math.sin(dLon / 2) * Math.sin(dLon / 2);
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }

  /**
   * Convertit les degrés en radians
   */
  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }

  /**
   * Transforme les données de la base en format DeliveryTrackingPoint
   */
  private transformDatabaseTrackingPoint(dbPoint: any): DeliveryTrackingPoint {
    return {
      id: dbPoint.id,
      delivery_id: dbPoint.delivery_id,
      order_id: dbPoint.order_id,
      driver_id: dbPoint.driver_id,
      current_location: {
        latitude: dbPoint.latitude || 0,
        longitude: dbPoint.longitude || 0,
      },
      heading: dbPoint.heading,
      speed: dbPoint.speed,
      accuracy: dbPoint.accuracy,
      battery_level: dbPoint.battery_level,
      is_online: dbPoint.is_online,
      estimated_arrival: dbPoint.estimated_arrival,
      distance_to_destination: dbPoint.distance_to_destination_calculated,
      created_at: dbPoint.created_at,
    };
  }

  /**
   * Cache l'historique de tracking
   */
  private async cacheTrackingHistory(deliveryId: string, history: DeliveryTrackingPoint[]): Promise<void> {
    try {
      const cacheData = {
        history,
        timestamp: Date.now(),
        deliveryId,
      };
      await AsyncStorage.setItem(`${TRACKING_CACHE_KEY}_${deliveryId}`, JSON.stringify(cacheData));
    } catch (error) {
      console.warn('⚠️ Failed to cache tracking history:', error);
    }
  }

  /**
   * Récupère l'historique depuis le cache
   */
  private async getCachedTrackingHistory(deliveryId: string): Promise<DeliveryTrackingPoint[]> {
    try {
      const cached = await AsyncStorage.getItem(`${TRACKING_CACHE_KEY}_${deliveryId}`);
      if (!cached) return [];

      const cacheData = JSON.parse(cached);
      const isExpired = Date.now() - cacheData.timestamp > 5 * 60 * 1000; // 5 minutes
      
      if (isExpired || cacheData.deliveryId !== deliveryId) {
        await AsyncStorage.removeItem(`${TRACKING_CACHE_KEY}_${deliveryId}`);
        return [];
      }

      console.log(`🚚 Using cached tracking history for delivery ${deliveryId}`);
      return cacheData.history || [];
    } catch (error) {
      console.warn('⚠️ Failed to get cached tracking history:', error);
      return [];
    }
  }
}

export const deliveryTrackingService = new DeliveryTrackingService();
