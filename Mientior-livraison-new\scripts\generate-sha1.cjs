#!/usr/bin/env node

/**
 * Script pour générer l'empreinte SHA-1 pour Google Maps
 * Usage: node scripts/generate-sha1.cjs
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔐 Génération de l\'empreinte SHA-1 pour Google Maps\n');

// Chemins possibles pour le keystore de débogage
const keystorePaths = [
  path.join(__dirname, '../android/app/debug.keystore'),
  path.join(process.env.USERPROFILE || process.env.HOME, '.android', 'debug.keystore'),
  path.join(process.env.ANDROID_HOME || '', 'debug.keystore')
];

function findKeystore() {
  for (const keystorePath of keystorePaths) {
    if (fs.existsSync(keystorePath)) {
      console.log(`✅ Keystore trouvé: ${keystorePath}`);
      return keystorePath;
    }
  }
  return null;
}

function generateSHA1(keystorePath) {
  try {
    console.log('🔍 Génération de l\'empreinte SHA-1...\n');
    
    const command = `keytool -list -v -keystore "${keystorePath}" -alias androiddebugkey -storepass android -keypass android`;
    
    const output = execSync(command, { encoding: 'utf8' });
    
    // Extraire l'empreinte SHA-1
    const sha1Match = output.match(/SHA1:\s*([A-F0-9:]+)/i);
    
    if (sha1Match) {
      const sha1 = sha1Match[1];
      console.log('🎉 EMPREINTE SHA-1 GÉNÉRÉE AVEC SUCCÈS !');
      console.log('=' .repeat(60));
      console.log(`SHA-1: ${sha1}`);
      console.log('=' .repeat(60));
      
      console.log('\n📋 INSTRUCTIONS POUR GOOGLE CLOUD CONSOLE:');
      console.log('1. Aller sur https://console.cloud.google.com');
      console.log('2. Sélectionner votre projet');
      console.log('3. Aller dans "APIs & Services" > "Credentials"');
      console.log('4. Cliquer sur votre API Key Google Maps');
      console.log('5. Dans "Application restrictions", sélectionner "Android apps"');
      console.log('6. Cliquer "Add an item"');
      console.log('7. Entrer:');
      console.log(`   - Package name: com.livraison.afrique`);
      console.log(`   - SHA-1 certificate fingerprint: ${sha1}`);
      console.log('8. Cliquer "Save"');
      
      console.log('\n🔧 APIS À ACTIVER:');
      console.log('- Maps SDK for Android');
      console.log('- Maps SDK for iOS');
      console.log('- Places API');
      console.log('- Directions API');
      console.log('- Geocoding API');
      
      return sha1;
    } else {
      console.log('❌ Impossible d\'extraire l\'empreinte SHA-1');
      return null;
    }
  } catch (error) {
    console.error('❌ Erreur lors de la génération:', error.message);
    return null;
  }
}

function checkPackageName() {
  console.log('\n📱 Vérification du nom du package...');
  
  const buildGradlePath = path.join(__dirname, '../android/app/build.gradle');
  
  if (fs.existsSync(buildGradlePath)) {
    const buildGradleContent = fs.readFileSync(buildGradlePath, 'utf8');
    const packageMatch = buildGradleContent.match(/applicationId\s*["']([^"']+)["']/);
    
    if (packageMatch) {
      const packageName = packageMatch[1];
      console.log(`✅ Package name trouvé: ${packageName}`);
      
      if (packageName === 'com.livraison.afrique') {
        console.log('✅ Package name correct pour Google Maps');
      } else {
        console.log('⚠️  Package name différent de celui attendu (com.livraison.afrique)');
      }
      
      return packageName;
    } else {
      console.log('❌ Package name non trouvé dans build.gradle');
      return null;
    }
  } else {
    console.log('❌ Fichier build.gradle non trouvé');
    return null;
  }
}

function generateConfigFile(sha1, packageName) {
  const config = {
    packageName: packageName || 'com.livraison.afrique',
    sha1: sha1,
    apiKey: 'AIzaSyCUSlG6L03l-nE5SH9Rm8sHQLZRKuRhD3s',
    googleCloudConsoleUrl: 'https://console.cloud.google.com',
    instructions: [
      'Aller sur Google Cloud Console',
      'Sélectionner le projet',
      'APIs & Services > Credentials',
      'Cliquer sur l\'API Key',
      'Application restrictions > Android apps',
      'Ajouter package name et SHA-1',
      'Activer les APIs nécessaires'
    ]
  };
  
  const configPath = path.join(__dirname, '../google-maps-config.json');
  fs.writeFileSync(configPath, JSON.stringify(config, null, 2));
  
  console.log(`\n💾 Configuration sauvegardée dans: ${configPath}`);
}

// Fonction principale
function main() {
  const keystorePath = findKeystore();
  
  if (!keystorePath) {
    console.log('❌ Aucun keystore de débogage trouvé');
    console.log('\n💡 SOLUTIONS:');
    console.log('1. Générer un keystore de débogage:');
    console.log('   keytool -genkey -v -keystore debug.keystore -storepass android -alias androiddebugkey -keypass android -keyalg RSA -keysize 2048 -validity 10000');
    console.log('2. Ou utiliser le keystore par défaut d\'Android Studio');
    return;
  }
  
  const packageName = checkPackageName();
  const sha1 = generateSHA1(keystorePath);
  
  if (sha1) {
    generateConfigFile(sha1, packageName);
    
    console.log('\n🎯 PROCHAINES ÉTAPES:');
    console.log('1. Configurer Google Cloud Console avec les informations ci-dessus');
    console.log('2. Attendre 5-10 minutes pour la propagation');
    console.log('3. Tester l\'application');
    console.log('4. Vérifier que la carte s\'affiche correctement');
  }
}

// Exécution
main();
