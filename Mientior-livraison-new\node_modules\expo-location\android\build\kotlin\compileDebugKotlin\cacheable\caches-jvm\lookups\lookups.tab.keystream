  Manifest android  ACCESS_BACKGROUND_LOCATION android.Manifest.permission  ACCESS_COARSE_LOCATION android.Manifest.permission  ACCESS_FINE_LOCATION android.Manifest.permission  FOREGROUND_SERVICE android.Manifest.permission  FOREGROUND_SERVICE_LOCATION android.Manifest.permission  	TargetApi android.annotation  Activity android.app  Notification android.app  NotificationChannel android.app  NotificationManager android.app  
PendingIntent android.app  Service android.app  RESULT_CANCELED android.app.Activity  	RESULT_OK android.app.Activity  Builder android.app.Notification  CATEGORY_SERVICE android.app.Notification  build  android.app.Notification.Builder  setCategory  android.app.Notification.Builder  setColor  android.app.Notification.Builder  setColorized  android.app.Notification.Builder  setContentIntent  android.app.Notification.Builder  setContentText  android.app.Notification.Builder  setContentTitle  android.app.Notification.Builder  setSmallIcon  android.app.Notification.Builder  description android.app.NotificationChannel  IMPORTANCE_LOW android.app.NotificationManager  createNotificationChannel android.app.NotificationManager  getNotificationChannel android.app.NotificationManager  FLAG_MUTABLE android.app.PendingIntent  FLAG_UPDATE_CURRENT android.app.PendingIntent  cancel android.app.PendingIntent  getActivity android.app.PendingIntent  let android.app.PendingIntent  Build android.app.Service  Color android.app.Service  	Exception android.app.Service  Intent android.app.Service  NOTIFICATION_SERVICE android.app.Service  Notification android.app.Service  NotificationChannel android.app.Service  NotificationManager android.app.Service  
PendingIntent android.app.Service  START_REDELIVER_INTENT android.app.Service  let android.app.Service  
onTaskRemoved android.app.Service  plus android.app.Service  run android.app.Service  
sServiceId android.app.Service  startForeground android.app.Service  stopForeground android.app.Service  stopSelf android.app.Service  
JobParameters android.app.job  
JobService android.app.job  jobFinished android.app.job.JobService  
ComponentName android.content  Context android.content  Intent android.content  ServiceConnection android.content  BIND_AUTO_CREATE android.content.Context  Build android.content.Context  Color android.content.Context  	Exception android.content.Context  Intent android.content.Context  LOCATION_SERVICE android.content.Context  NOTIFICATION_SERVICE android.content.Context  Notification android.content.Context  NotificationChannel android.content.Context  NotificationManager android.content.Context  
PendingIntent android.content.Context  SENSOR_SERVICE android.content.Context  START_REDELIVER_INTENT android.content.Context  applicationContext android.content.Context  bindService android.content.Context  getSystemService android.content.Context  let android.content.Context  packageManager android.content.Context  packageName android.content.Context  plus android.content.Context  run android.content.Context  
sServiceId android.content.Context  startForegroundService android.content.Context  Build android.content.ContextWrapper  Color android.content.ContextWrapper  	Exception android.content.ContextWrapper  Intent android.content.ContextWrapper  NOTIFICATION_SERVICE android.content.ContextWrapper  Notification android.content.ContextWrapper  NotificationChannel android.content.ContextWrapper  NotificationManager android.content.ContextWrapper  
PendingIntent android.content.ContextWrapper  START_REDELIVER_INTENT android.content.ContextWrapper  applicationInfo android.content.ContextWrapper  getSystemService android.content.ContextWrapper  let android.content.ContextWrapper  packageManager android.content.ContextWrapper  plus android.content.ContextWrapper  run android.content.ContextWrapper  
sServiceId android.content.ContextWrapper  FLAG_ACTIVITY_SINGLE_TOP android.content.Intent  extras android.content.Intent  flags android.content.Intent  let android.content.Intent  	putExtras android.content.Intent  PackageManager android.content.pm  icon "android.content.pm.PackageItemInfo  	loadLabel "android.content.pm.PackageItemInfo  PERMISSION_GRANTED !android.content.pm.PackageManager  getLaunchIntentForPackage !android.content.pm.PackageManager  Color android.graphics  
parseColor android.graphics.Color  GeomagneticField android.hardware  Sensor android.hardware  SensorEvent android.hardware  SensorEventListener android.hardware  
SensorManager android.hardware  declination !android.hardware.GeomagneticField  takeIf !android.hardware.GeomagneticField  TYPE_ACCELEROMETER android.hardware.Sensor  TYPE_MAGNETIC_FIELD android.hardware.Sensor  type android.hardware.Sensor  sensor android.hardware.SensorEvent  values android.hardware.SensorEvent  SENSOR_DELAY_NORMAL android.hardware.SensorManager  getDefaultSensor android.hardware.SensorManager  getOrientation android.hardware.SensorManager  getRotationMatrix android.hardware.SensorManager  registerListener android.hardware.SensorManager  unregisterListener android.hardware.SensorManager  Address android.location  Geocoder android.location  Location android.location  LocationManager android.location  	adminArea android.location.Address  countryCode android.location.Address  countryName android.location.Address  featureName android.location.Address  getAddressLine android.location.Address  latitude android.location.Address  let android.location.Address  locality android.location.Address  	longitude android.location.Address  maxAddressLineIndex android.location.Address  
postalCode android.location.Address  subAdminArea android.location.Address  subLocality android.location.Address  subThoroughfare android.location.Address  thoroughfare android.location.Address  getFromLocation android.location.Geocoder  getFromLocationName android.location.Geocoder  	isPresent android.location.Geocoder  accuracy android.location.Location  altitude android.location.Location  apply android.location.Location  bearing android.location.Location  
distanceTo android.location.Location  isFromMockProvider android.location.Location  latitude android.location.Location  let android.location.Location  	longitude android.location.Location  speed android.location.Location  time android.location.Location  verticalAccuracyMeters android.location.Location  GPS_PROVIDER  android.location.LocationManager  NETWORK_PROVIDER  android.location.LocationManager  PASSIVE_PROVIDER  android.location.LocationManager  getLastKnownLocation  android.location.LocationManager  isProviderEnabled  android.location.LocationManager  
BaseBundle 
android.os  Binder 
android.os  Build 
android.os  Bundle 
android.os  IBinder 
android.os  Looper 
android.os  PersistableBundle 
android.os  
getBoolean android.os.BaseBundle  getInt android.os.BaseBundle  	getString android.os.BaseBundle  putAll android.os.BaseBundle  
putBoolean android.os.BaseBundle  	putDouble android.os.BaseBundle  putInt android.os.BaseBundle  	putString android.os.BaseBundle  SDK_INT android.os.Build.VERSION  O android.os.Build.VERSION_CODES  P android.os.Build.VERSION_CODES  Q android.os.Build.VERSION_CODES  S android.os.Build.VERSION_CODES  UPSIDE_DOWN_CAKE android.os.Build.VERSION_CODES  accuracy android.os.Bundle  apply android.os.Bundle  
getBoolean android.os.Bundle  	getBundle android.os.Bundle  	getString android.os.Bundle  heading android.os.Bundle  let android.os.Bundle  
magHeading android.os.Bundle  putAll android.os.Bundle  
putBoolean android.os.Bundle  	putBundle android.os.Bundle  putFloat android.os.Bundle  putInt android.os.Bundle  putParcelableArrayList android.os.Bundle  	putString android.os.Bundle  trueHeading android.os.Bundle  watchId android.os.Bundle  
getMainLooper android.os.Looper  myLooper android.os.Looper  GeofencingRegionState android.os.PersistableBundle  apply android.os.PersistableBundle  getParamAsDouble android.os.PersistableBundle  getPersistableBundle android.os.PersistableBundle  let android.os.PersistableBundle  	putDouble android.os.PersistableBundle  putInt android.os.PersistableBundle  putPersistableBundle android.os.PersistableBundle  	putString android.os.PersistableBundle  Log android.util  e android.util.Log  w android.util.Log  ChecksSdkIntAtLeast androidx.annotation  ActivityCompat androidx.core.app  checkSelfPermission  androidx.core.app.ActivityCompat  checkSelfPermission #androidx.core.content.ContextCompat  LocationManagerCompat androidx.core.location  isLocationEnabled ,androidx.core.location.LocationManagerCompat  bundleOf androidx.core.os  ApiException !com.google.android.gms.common.api  CommonStatusCodes !com.google.android.gms.common.api  ResolvableApiException !com.google.android.gms.common.api  
statusCode .com.google.android.gms.common.api.ApiException  RESOLUTION_REQUIRED 3com.google.android.gms.common.api.CommonStatusCodes  startResolutionForResult 8com.google.android.gms.common.api.ResolvableApiException  CurrentLocationRequest com.google.android.gms.location  FusedLocationProviderClient com.google.android.gms.location  Geofence com.google.android.gms.location  GeofenceStatusCodes com.google.android.gms.location  GeofencingClient com.google.android.gms.location  GeofencingEvent com.google.android.gms.location  GeofencingRequest com.google.android.gms.location  Granularity com.google.android.gms.location  LocationAvailability com.google.android.gms.location  LocationCallback com.google.android.gms.location  LocationRequest com.google.android.gms.location  LocationResult com.google.android.gms.location  LocationServices com.google.android.gms.location  LocationSettingsRequest com.google.android.gms.location  Priority com.google.android.gms.location  SettingsClient com.google.android.gms.location  Builder 6com.google.android.gms.location.CurrentLocationRequest  Granularity >com.google.android.gms.location.CurrentLocationRequest.Builder  apply >com.google.android.gms.location.CurrentLocationRequest.Builder  build >com.google.android.gms.location.CurrentLocationRequest.Builder  mapAccuracyToPriority >com.google.android.gms.location.CurrentLocationRequest.Builder  setGranularity >com.google.android.gms.location.CurrentLocationRequest.Builder  setMaxUpdateAgeMillis >com.google.android.gms.location.CurrentLocationRequest.Builder  setPriority >com.google.android.gms.location.CurrentLocationRequest.Builder  getCurrentLocation ;com.google.android.gms.location.FusedLocationProviderClient  lastLocation ;com.google.android.gms.location.FusedLocationProviderClient  removeLocationUpdates ;com.google.android.gms.location.FusedLocationProviderClient  requestLocationUpdates ;com.google.android.gms.location.FusedLocationProviderClient  Builder (com.google.android.gms.location.Geofence  GEOFENCE_TRANSITION_DWELL (com.google.android.gms.location.Geofence  GEOFENCE_TRANSITION_ENTER (com.google.android.gms.location.Geofence  GEOFENCE_TRANSITION_EXIT (com.google.android.gms.location.Geofence  NEVER_EXPIRE (com.google.android.gms.location.Geofence  	requestId (com.google.android.gms.location.Geofence  build 0com.google.android.gms.location.Geofence.Builder  setCircularRegion 0com.google.android.gms.location.Geofence.Builder  setExpirationDuration 0com.google.android.gms.location.Geofence.Builder  setRequestId 0com.google.android.gms.location.Geofence.Builder  setTransitionTypes 0com.google.android.gms.location.Geofence.Builder  GEOFENCE_NOT_AVAILABLE 3com.google.android.gms.location.GeofenceStatusCodes  GEOFENCE_TOO_MANY_GEOFENCES 3com.google.android.gms.location.GeofenceStatusCodes  !GEOFENCE_TOO_MANY_PENDING_INTENTS 3com.google.android.gms.location.GeofenceStatusCodes  addGeofences 0com.google.android.gms.location.GeofencingClient  removeGeofences 0com.google.android.gms.location.GeofencingClient  	errorCode /com.google.android.gms.location.GeofencingEvent  
fromIntent /com.google.android.gms.location.GeofencingEvent  geofenceTransition /com.google.android.gms.location.GeofencingEvent  hasError /com.google.android.gms.location.GeofencingEvent  triggeringGeofences /com.google.android.gms.location.GeofencingEvent  Builder 1com.google.android.gms.location.GeofencingRequest  INITIAL_TRIGGER_ENTER 1com.google.android.gms.location.GeofencingRequest  INITIAL_TRIGGER_EXIT 1com.google.android.gms.location.GeofencingRequest  let 1com.google.android.gms.location.GeofencingRequest  addGeofences 9com.google.android.gms.location.GeofencingRequest.Builder  build 9com.google.android.gms.location.GeofencingRequest.Builder  setInitialTrigger 9com.google.android.gms.location.GeofencingRequest.Builder  GRANULARITY_PERMISSION_LEVEL +com.google.android.gms.location.Granularity  isLocationAvailable 4com.google.android.gms.location.LocationAvailability  GeomagneticField 0com.google.android.gms.location.LocationCallback  LocationUnavailableException 0com.google.android.gms.location.LocationCallback  LocationUnknownException 0com.google.android.gms.location.LocationCallback  System 0com.google.android.gms.location.LocationCallback  let 0com.google.android.gms.location.LocationCallback  	mGeofield 0com.google.android.gms.location.LocationCallback  Builder /com.google.android.gms.location.LocationRequest  PRIORITY_HIGH_ACCURACY /com.google.android.gms.location.LocationRequest  build 7com.google.android.gms.location.LocationRequest.Builder  setMaxUpdateDelayMillis 7com.google.android.gms.location.LocationRequest.Builder  
setMaxUpdates 7com.google.android.gms.location.LocationRequest.Builder  setMinUpdateDistanceMeters 7com.google.android.gms.location.LocationRequest.Builder  setMinUpdateIntervalMillis 7com.google.android.gms.location.LocationRequest.Builder  setPriority 7com.google.android.gms.location.LocationRequest.Builder  
extractResult .com.google.android.gms.location.LocationResult  lastLocation .com.google.android.gms.location.LocationResult  	locations .com.google.android.gms.location.LocationResult  getFusedLocationProviderClient 0com.google.android.gms.location.LocationServices  getGeofencingClient 0com.google.android.gms.location.LocationServices  getSettingsClient 0com.google.android.gms.location.LocationServices  Builder 7com.google.android.gms.location.LocationSettingsRequest  addLocationRequest ?com.google.android.gms.location.LocationSettingsRequest.Builder  build ?com.google.android.gms.location.LocationSettingsRequest.Builder   PRIORITY_BALANCED_POWER_ACCURACY (com.google.android.gms.location.Priority  PRIORITY_HIGH_ACCURACY (com.google.android.gms.location.Priority  PRIORITY_LOW_POWER (com.google.android.gms.location.Priority  checkLocationSettings .com.google.android.gms.location.SettingsClient  OnCanceledListener com.google.android.gms.tasks  OnCompleteListener com.google.android.gms.tasks  OnFailureListener com.google.android.gms.tasks  OnSuccessListener com.google.android.gms.tasks  Task com.google.android.gms.tasks  <SAM-CONSTRUCTOR> /com.google.android.gms.tasks.OnCanceledListener  <SAM-CONSTRUCTOR> /com.google.android.gms.tasks.OnCompleteListener  <SAM-CONSTRUCTOR> .com.google.android.gms.tasks.OnFailureListener  <SAM-CONSTRUCTOR> .com.google.android.gms.tasks.OnSuccessListener  addOnCanceledListener !com.google.android.gms.tasks.Task  addOnCompleteListener !com.google.android.gms.tasks.Task  addOnFailureListener !com.google.android.gms.tasks.Task  addOnSuccessListener !com.google.android.gms.tasks.Task  result !com.google.android.gms.tasks.Task  	MapHelper expo.modules.core  MapArguments expo.modules.core.arguments  ReadableArguments expo.modules.core.arguments  getArguments -expo.modules.core.arguments.ReadableArguments  toBundle -expo.modules.core.arguments.ReadableArguments  ActivityEventListener expo.modules.core.interfaces  	Arguments expo.modules.core.interfaces  LifecycleEventListener expo.modules.core.interfaces  	getDouble &expo.modules.core.interfaces.Arguments  getLong &expo.modules.core.interfaces.Arguments  	UIManager %expo.modules.core.interfaces.services  registerActivityEventListener /expo.modules.core.interfaces.services.UIManager  unregisterActivityEventListener /expo.modules.core.interfaces.services.UIManager  Permissions #expo.modules.interfaces.permissions  'askForPermissionsWithPermissionsManager /expo.modules.interfaces.permissions.Permissions  $getPermissionsWithPermissionsManager /expo.modules.interfaces.permissions.Permissions  hasGrantedPermissions /expo.modules.interfaces.permissions.Permissions  isPermissionPresentInManifest /expo.modules.interfaces.permissions.Permissions  let /expo.modules.interfaces.permissions.Permissions  TaskConsumer #expo.modules.interfaces.taskManager  TaskConsumerInterface #expo.modules.interfaces.taskManager  TaskExecutionCallback #expo.modules.interfaces.taskManager  
TaskInterface #expo.modules.interfaces.taskManager  TaskManagerInterface #expo.modules.interfaces.taskManager  TaskManagerUtilsInterface #expo.modules.interfaces.taskManager  Any 0expo.modules.interfaces.taskManager.TaskConsumer  AppForegroundedSingleton 0expo.modules.interfaces.taskManager.TaskConsumer  	Arguments 0expo.modules.interfaces.taskManager.TaskConsumer  	ArrayList 0expo.modules.interfaces.taskManager.TaskConsumer  Boolean 0expo.modules.interfaces.taskManager.TaskConsumer  Build 0expo.modules.interfaces.taskManager.TaskConsumer  Bundle 0expo.modules.interfaces.taskManager.TaskConsumer  
ComponentName 0expo.modules.interfaces.taskManager.TaskConsumer  Context 0expo.modules.interfaces.taskManager.TaskConsumer  Double 0expo.modules.interfaces.taskManager.TaskConsumer  Error 0expo.modules.interfaces.taskManager.TaskConsumer  FOREGROUND_SERVICE_KEY 0expo.modules.interfaces.taskManager.TaskConsumer  Float 0expo.modules.interfaces.taskManager.TaskConsumer  Geofence 0expo.modules.interfaces.taskManager.TaskConsumer  GeofenceStatusCodes 0expo.modules.interfaces.taskManager.TaskConsumer  GeofencingEvent 0expo.modules.interfaces.taskManager.TaskConsumer  GeofencingException 0expo.modules.interfaces.taskManager.TaskConsumer  GeofencingRegionState 0expo.modules.interfaces.taskManager.TaskConsumer  GeofencingRequest 0expo.modules.interfaces.taskManager.TaskConsumer  HashMap 0expo.modules.interfaces.taskManager.TaskConsumer  IBinder 0expo.modules.interfaces.taskManager.TaskConsumer  Int 0expo.modules.interfaces.taskManager.TaskConsumer  Intent 0expo.modules.interfaces.taskManager.TaskConsumer  List 0expo.modules.interfaces.taskManager.TaskConsumer  LocationHelpers 0expo.modules.interfaces.taskManager.TaskConsumer  LocationModule 0expo.modules.interfaces.taskManager.TaskConsumer  LocationOptions 0expo.modules.interfaces.taskManager.TaskConsumer  LocationResponse 0expo.modules.interfaces.taskManager.TaskConsumer  LocationResult 0expo.modules.interfaces.taskManager.TaskConsumer  LocationServices 0expo.modules.interfaces.taskManager.TaskConsumer  LocationTaskService 0expo.modules.interfaces.taskManager.TaskConsumer  Log 0expo.modules.interfaces.taskManager.TaskConsumer  Long 0expo.modules.interfaces.taskManager.TaskConsumer  MapArguments 0expo.modules.interfaces.taskManager.TaskConsumer  	MapHelper 0expo.modules.interfaces.taskManager.TaskConsumer  MutableList 0expo.modules.interfaces.taskManager.TaskConsumer  PersistableBundle 0expo.modules.interfaces.taskManager.TaskConsumer  ReadableArguments 0expo.modules.interfaces.taskManager.TaskConsumer  SecurityException 0expo.modules.interfaces.taskManager.TaskConsumer  
ServiceBinder 0expo.modules.interfaces.taskManager.TaskConsumer  ServiceConnection 0expo.modules.interfaces.taskManager.TaskConsumer  String 0expo.modules.interfaces.taskManager.TaskConsumer  TAG 0expo.modules.interfaces.taskManager.TaskConsumer  UUID 0expo.modules.interfaces.taskManager.TaskConsumer  abs 0expo.modules.interfaces.taskManager.TaskConsumer  apply 0expo.modules.interfaces.taskManager.TaskConsumer  context 0expo.modules.interfaces.taskManager.TaskConsumer  filterIsInstance 0expo.modules.interfaces.taskManager.TaskConsumer  
getContext 0expo.modules.interfaces.taskManager.TaskConsumer  getErrorString 0expo.modules.interfaces.taskManager.TaskConsumer  getParamAsDouble 0expo.modules.interfaces.taskManager.TaskConsumer  getValue 0expo.modules.interfaces.taskManager.TaskConsumer  isAnyProviderAvailable 0expo.modules.interfaces.taskManager.TaskConsumer  java 0expo.modules.interfaces.taskManager.TaskConsumer  lazy 0expo.modules.interfaces.taskManager.TaskConsumer  let 0expo.modules.interfaces.taskManager.TaskConsumer  listOf 0expo.modules.interfaces.taskManager.TaskConsumer  mService 0expo.modules.interfaces.taskManager.TaskConsumer  
plusAssign 0expo.modules.interfaces.taskManager.TaskConsumer  prepareLocationRequest 0expo.modules.interfaces.taskManager.TaskConsumer  provideDelegate 0expo.modules.interfaces.taskManager.TaskConsumer  run 0expo.modules.interfaces.taskManager.TaskConsumer  sLastTimestamp 0expo.modules.interfaces.taskManager.TaskConsumer  set 0expo.modules.interfaces.taskManager.TaskConsumer  
setOptions 0expo.modules.interfaces.taskManager.TaskConsumer  shouldUseForegroundService 0expo.modules.interfaces.taskManager.TaskConsumer  taskManagerUtils 0expo.modules.interfaces.taskManager.TaskConsumer  toDoubleOrNull 0expo.modules.interfaces.taskManager.TaskConsumer  <SAM-CONSTRUCTOR> 9expo.modules.interfaces.taskManager.TaskExecutionCallback  
onFinished 9expo.modules.interfaces.taskManager.TaskExecutionCallback  appScopeKey 1expo.modules.interfaces.taskManager.TaskInterface  execute 1expo.modules.interfaces.taskManager.TaskInterface  name 1expo.modules.interfaces.taskManager.TaskInterface  options 1expo.modules.interfaces.taskManager.TaskInterface  registerTask 8expo.modules.interfaces.taskManager.TaskManagerInterface  taskHasConsumerOfClass 8expo.modules.interfaces.taskManager.TaskManagerInterface  unregisterTask 8expo.modules.interfaces.taskManager.TaskManagerInterface  createTaskIntent =expo.modules.interfaces.taskManager.TaskManagerUtilsInterface  extractDataFromJobParams =expo.modules.interfaces.taskManager.TaskManagerUtilsInterface  scheduleJob =expo.modules.interfaces.taskManager.TaskManagerUtilsInterface  
AppContext expo.modules.kotlin  Promise expo.modules.kotlin  legacyModule expo.modules.kotlin.AppContext  permissions expo.modules.kotlin.AppContext  reactContext expo.modules.kotlin.AppContext  throwingActivity expo.modules.kotlin.AppContext  reject expo.modules.kotlin.Promise  resolve expo.modules.kotlin.Promise  CodedException expo.modules.kotlin.exception  
Exceptions expo.modules.kotlin.exception  Class ,expo.modules.kotlin.exception.CodedException  	Companion ,expo.modules.kotlin.exception.CodedException  	Exception ,expo.modules.kotlin.exception.CodedException  String ,expo.modules.kotlin.exception.CodedException  	Throwable ,expo.modules.kotlin.exception.CodedException  java ,expo.modules.kotlin.exception.CodedException  java 6expo.modules.kotlin.exception.CodedException.Companion  AppContextLost (expo.modules.kotlin.exception.Exceptions  ReactContextLost (expo.modules.kotlin.exception.Exceptions  AsyncFunctionBuilder expo.modules.kotlin.functions  AsyncFunctionComponent expo.modules.kotlin.functions  BaseAsyncFunctionComponent expo.modules.kotlin.functions  	Coroutine expo.modules.kotlin.functions  SuspendFunctionComponent expo.modules.kotlin.functions  	Coroutine 2expo.modules.kotlin.functions.AsyncFunctionBuilder  Module expo.modules.kotlin.modules  ModuleDefinition expo.modules.kotlin.modules  ModuleDefinitionBuilder expo.modules.kotlin.modules  ModuleDefinitionData expo.modules.kotlin.modules  Name ;expo.modules.kotlin.modules.InternalModuleDefinitionBuilder  OnActivityEntersBackground ;expo.modules.kotlin.modules.InternalModuleDefinitionBuilder  OnActivityEntersForeground ;expo.modules.kotlin.modules.InternalModuleDefinitionBuilder  OnCreate ;expo.modules.kotlin.modules.InternalModuleDefinitionBuilder  
appContext "expo.modules.kotlin.modules.Module  	sendEvent "expo.modules.kotlin.modules.Module  Activity 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  AppForegroundedSingleton 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  
AsyncFunction 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  Build 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  Context 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  	Coroutine 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  Events 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  
Exceptions 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  %ForegroundServicePermissionsException 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  )ForegroundServiceStartNotAllowedException 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  GeofencingTaskConsumer 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  HEADING_EVENT_NAME 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  LOCATION_EVENT_NAME 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  'LocationBackgroundUnauthorizedException 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  LocationHelpers 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  LocationOptions 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  LocationServices 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  $LocationSettingsUnsatisfiedException 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  LocationTaskConsumer 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  LocationUnauthorizedException 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  Manifest 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  MissingUIManagerException 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  Name 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  NoPermissionsModuleException 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  OnActivityEntersBackground 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  OnActivityEntersForeground 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  OnCreate 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  SensorManagerUnavailable 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  addPendingLocationRequest 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  
appContext 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  'askForPermissionsWithPermissionsManager 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  destroyHeadingWatch 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  geocode 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  getBackgroundPermissionsAsync 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  getCurrentPositionAsync 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  getForegroundPermissionsAsync 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  getLastKnownPositionAsync 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  $getPermissionsWithPermissionsManager 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  getProviderStatus 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  hasForegroundServicePermissions 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  hasNetworkProviderEnabled 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  isAnyProviderAvailable 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  isMissingBackgroundPermissions 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  isMissingForegroundPermissions 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  java 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  mContext 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  
mHeadingId 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  mLocationProvider 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  mSensorManager 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  mTaskManager 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  
mUIManager 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  prepareLocationRequest 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  removeLocationUpdatesForRequest 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  !requestBackgroundPermissionsAsync 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  requestContinuousUpdates 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  resume 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  resumeWithException 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  reverseGeocode 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  startHeadingUpdate 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  suspendCoroutine 3expo.modules.kotlin.modules.ModuleDefinitionBuilder  
AsyncFunction 3expo.modules.kotlin.objects.ObjectDefinitionBuilder  AsyncFunctionWithPromise 3expo.modules.kotlin.objects.ObjectDefinitionBuilder  Events 3expo.modules.kotlin.objects.ObjectDefinitionBuilder  Field expo.modules.kotlin.records  Record expo.modules.kotlin.records  
Enumerable expo.modules.kotlin.types  Activity expo.modules.location  ActivityCompat expo.modules.location  ActivityEventListener expo.modules.location  Any expo.modules.location  ApiException expo.modules.location  AppForegroundedSingleton expo.modules.location  	ArrayList expo.modules.location  Boolean expo.modules.location  Build expo.modules.location  Bundle expo.modules.location  CHECK_SETTINGS_REQUEST_CODE expo.modules.location  ChecksSdkIntAtLeast expo.modules.location  Class expo.modules.location  CodedException expo.modules.location  CommonStatusCodes expo.modules.location  Context expo.modules.location  ConversionException expo.modules.location  	Coroutine expo.modules.location  %CurrentLocationIsUnavailableException expo.modules.location  CurrentLocationRequest expo.modules.location  DEGREE_DELTA expo.modules.location  Double expo.modules.location  	Exception expo.modules.location  
Exceptions expo.modules.location  Float expo.modules.location  
FloatArray expo.modules.location  %ForegroundServicePermissionsException expo.modules.location  )ForegroundServiceStartNotAllowedException expo.modules.location  FusedLocationProviderClient expo.modules.location  GeocodeException expo.modules.location  GeocodeResponse expo.modules.location  Geocoder expo.modules.location  GeofencingException expo.modules.location  GeofencingOptions expo.modules.location  GeofencingTaskConsumer expo.modules.location  GeomagneticField expo.modules.location  Granularity expo.modules.location  HEADING_EVENT_NAME expo.modules.location  HashMap expo.modules.location  Heading expo.modules.location  HeadingEventResponse expo.modules.location  Int expo.modules.location  Intent expo.modules.location  LOCATION_EVENT_NAME expo.modules.location  LifecycleEventListener expo.modules.location  List expo.modules.location  Locale expo.modules.location  Location expo.modules.location  LocationAccuracy expo.modules.location  LocationActivityResultListener expo.modules.location  LocationAvailability expo.modules.location  'LocationBackgroundUnauthorizedException expo.modules.location  LocationCallback expo.modules.location  LocationHelpers expo.modules.location  LocationLastKnownOptions expo.modules.location  LocationManager expo.modules.location  LocationManagerCompat expo.modules.location  LocationModule expo.modules.location  LocationOptions expo.modules.location  LocationParams expo.modules.location  LocationProviderStatus expo.modules.location  LocationRequest expo.modules.location  LocationRequestCallbacks expo.modules.location  !LocationRequestCancelledException expo.modules.location   LocationRequestRejectedException expo.modules.location  LocationResponse expo.modules.location  LocationResult expo.modules.location  LocationServices expo.modules.location  LocationSettingsRequest expo.modules.location  $LocationSettingsUnsatisfiedException expo.modules.location  LocationTaskConsumer expo.modules.location  LocationTaskOptions expo.modules.location  LocationUnauthorizedException expo.modules.location  LocationUnavailableException expo.modules.location  LocationUnknownException expo.modules.location  Log expo.modules.location  Long expo.modules.location  Looper expo.modules.location  Manifest expo.modules.location  Math expo.modules.location  MissingUIManagerException expo.modules.location  Module expo.modules.location  NoGeocodeException expo.modules.location  NoPermissionInManifestException expo.modules.location  NoPermissionsModuleException expo.modules.location  PackageManager expo.modules.location   PermissionDetailsLocationAndroid expo.modules.location  PermissionRequestResponse expo.modules.location  Permissions expo.modules.location  Priority expo.modules.location  Promise expo.modules.location  ResolvableApiException expo.modules.location  ReverseGeocodeLocation expo.modules.location  ReverseGeocodeResponse expo.modules.location  SecurityException expo.modules.location  Sensor expo.modules.location  SensorEvent expo.modules.location  SensorEventListener expo.modules.location  
SensorManager expo.modules.location  SensorManagerUnavailable expo.modules.location  String expo.modules.location  System expo.modules.location  TAG expo.modules.location  
TIME_DELTA expo.modules.location  TaskManagerInterface expo.modules.location  TaskManagerNotFoundException expo.modules.location  	Throwable expo.modules.location  	UIManager expo.modules.location  abs expo.modules.location  addPendingLocationRequest expo.modules.location  
appContext expo.modules.location  apply expo.modules.location  'askForPermissionsWithPermissionsManager expo.modules.location  bundleOf expo.modules.location  destroyHeadingWatch expo.modules.location  	emptyList expo.modules.location  from expo.modules.location  geocode expo.modules.location  getBackgroundPermissionsAsync expo.modules.location  getCurrentPositionAsync expo.modules.location  getForegroundPermissionsAsync expo.modules.location  getLastKnownPositionAsync expo.modules.location  $getPermissionsWithPermissionsManager expo.modules.location  getProviderStatus expo.modules.location  getValue expo.modules.location  hasForegroundServicePermissions expo.modules.location  hasNetworkProviderEnabled expo.modules.location  isAnyProviderAvailable expo.modules.location  isLocationValid expo.modules.location  isMissingBackgroundPermissions expo.modules.location  isMissingForegroundPermissions expo.modules.location  java expo.modules.location  lazy expo.modules.location  let expo.modules.location  mContext expo.modules.location  	mGeofield expo.modules.location  
mHeadingId expo.modules.location  mLocationProvider expo.modules.location  mSensorManager expo.modules.location  mTaskManager expo.modules.location  
mUIManager expo.modules.location  mapAccuracyToPriority expo.modules.location  
mapNotNull expo.modules.location  prepareCurrentLocationRequest expo.modules.location  prepareLocationRequest expo.modules.location  provideDelegate expo.modules.location  removeLocationUpdatesForRequest expo.modules.location  !requestBackgroundPermissionsAsync expo.modules.location  requestContinuousUpdates expo.modules.location  requestSingleLocation expo.modules.location  resume expo.modules.location  resumeWithException expo.modules.location  reverseGeocode expo.modules.location  set expo.modules.location  startHeadingUpdate expo.modules.location  suspendCoroutine expo.modules.location  takeIf expo.modules.location  isForegrounded .expo.modules.location.AppForegroundedSingleton  HIGH &expo.modules.location.LocationAccuracy  LOW &expo.modules.location.LocationAccuracy  LOWEST &expo.modules.location.LocationAccuracy  MEDIUM &expo.modules.location.LocationAccuracy  onResult 4expo.modules.location.LocationActivityResultListener  Any %expo.modules.location.LocationHelpers  Boolean %expo.modules.location.LocationHelpers  Bundle %expo.modules.location.LocationHelpers  CodedException %expo.modules.location.LocationHelpers  	Companion %expo.modules.location.LocationHelpers  Context %expo.modules.location.LocationHelpers  ConversionException %expo.modules.location.LocationHelpers  %CurrentLocationIsUnavailableException %expo.modules.location.LocationHelpers  CurrentLocationRequest %expo.modules.location.LocationHelpers  Double %expo.modules.location.LocationHelpers  FusedLocationProviderClient %expo.modules.location.LocationHelpers  Granularity %expo.modules.location.LocationHelpers  Int %expo.modules.location.LocationHelpers  Location %expo.modules.location.LocationHelpers  LocationAccuracy %expo.modules.location.LocationHelpers  LocationLastKnownOptions %expo.modules.location.LocationHelpers  LocationManager %expo.modules.location.LocationHelpers  LocationModule %expo.modules.location.LocationHelpers  LocationOptions %expo.modules.location.LocationHelpers  LocationParams %expo.modules.location.LocationHelpers  LocationRequest %expo.modules.location.LocationHelpers  LocationRequestCallbacks %expo.modules.location.LocationHelpers  !LocationRequestCancelledException %expo.modules.location.LocationHelpers   LocationRequestRejectedException %expo.modules.location.LocationHelpers  LocationResponse %expo.modules.location.LocationHelpers  PermissionRequestResponse %expo.modules.location.LocationHelpers  Permissions %expo.modules.location.LocationHelpers  Priority %expo.modules.location.LocationHelpers  Promise %expo.modules.location.LocationHelpers  SecurityException %expo.modules.location.LocationHelpers  String %expo.modules.location.LocationHelpers  System %expo.modules.location.LocationHelpers  	Throwable %expo.modules.location.LocationHelpers  apply %expo.modules.location.LocationHelpers  'askForPermissionsWithPermissionsManager %expo.modules.location.LocationHelpers  buildLocationParamsForAccuracy %expo.modules.location.LocationHelpers  $getPermissionsWithPermissionsManager %expo.modules.location.LocationHelpers  hasNetworkProviderEnabled %expo.modules.location.LocationHelpers  isAnyProviderAvailable %expo.modules.location.LocationHelpers  isLocationValid %expo.modules.location.LocationHelpers  java %expo.modules.location.LocationHelpers  let %expo.modules.location.LocationHelpers  mapAccuracyToPriority %expo.modules.location.LocationHelpers  mapOptionsToLocationParams %expo.modules.location.LocationHelpers  prepareCurrentLocationRequest %expo.modules.location.LocationHelpers  prepareLocationRequest %expo.modules.location.LocationHelpers  requestContinuousUpdates %expo.modules.location.LocationHelpers  requestSingleLocation %expo.modules.location.LocationHelpers  resume %expo.modules.location.LocationHelpers  resumeWithException %expo.modules.location.LocationHelpers  suspendCoroutine %expo.modules.location.LocationHelpers  Any /expo.modules.location.LocationHelpers.Companion  Bundle /expo.modules.location.LocationHelpers.Companion  CodedException /expo.modules.location.LocationHelpers.Companion  Context /expo.modules.location.LocationHelpers.Companion  ConversionException /expo.modules.location.LocationHelpers.Companion  %CurrentLocationIsUnavailableException /expo.modules.location.LocationHelpers.Companion  CurrentLocationRequest /expo.modules.location.LocationHelpers.Companion  Double /expo.modules.location.LocationHelpers.Companion  Granularity /expo.modules.location.LocationHelpers.Companion  LocationAccuracy /expo.modules.location.LocationHelpers.Companion  LocationManager /expo.modules.location.LocationHelpers.Companion  LocationModule /expo.modules.location.LocationHelpers.Companion  LocationParams /expo.modules.location.LocationHelpers.Companion  LocationRequest /expo.modules.location.LocationHelpers.Companion  !LocationRequestCancelledException /expo.modules.location.LocationHelpers.Companion   LocationRequestRejectedException /expo.modules.location.LocationHelpers.Companion  LocationResponse /expo.modules.location.LocationHelpers.Companion  PermissionRequestResponse /expo.modules.location.LocationHelpers.Companion  Permissions /expo.modules.location.LocationHelpers.Companion  Priority /expo.modules.location.LocationHelpers.Companion  System /expo.modules.location.LocationHelpers.Companion  apply /expo.modules.location.LocationHelpers.Companion  'askForPermissionsWithPermissionsManager /expo.modules.location.LocationHelpers.Companion  buildLocationParamsForAccuracy /expo.modules.location.LocationHelpers.Companion  $getPermissionsWithPermissionsManager /expo.modules.location.LocationHelpers.Companion  hasNetworkProviderEnabled /expo.modules.location.LocationHelpers.Companion  isAnyProviderAvailable /expo.modules.location.LocationHelpers.Companion  isLocationValid /expo.modules.location.LocationHelpers.Companion  java /expo.modules.location.LocationHelpers.Companion  let /expo.modules.location.LocationHelpers.Companion  mapAccuracyToPriority /expo.modules.location.LocationHelpers.Companion  mapOptionsToLocationParams /expo.modules.location.LocationHelpers.Companion  prepareCurrentLocationRequest /expo.modules.location.LocationHelpers.Companion  prepareLocationRequest /expo.modules.location.LocationHelpers.Companion  requestContinuousUpdates /expo.modules.location.LocationHelpers.Companion  requestSingleLocation /expo.modules.location.LocationHelpers.Companion  resume /expo.modules.location.LocationHelpers.Companion  resumeWithException /expo.modules.location.LocationHelpers.Companion  suspendCoroutine /expo.modules.location.LocationHelpers.Companion  ACCURACY_BALANCED $expo.modules.location.LocationModule  ACCURACY_BEST_FOR_NAVIGATION $expo.modules.location.LocationModule  
ACCURACY_HIGH $expo.modules.location.LocationModule  ACCURACY_HIGHEST $expo.modules.location.LocationModule  ACCURACY_LOW $expo.modules.location.LocationModule  ACCURACY_LOWEST $expo.modules.location.LocationModule  Activity $expo.modules.location.LocationModule  ActivityCompat $expo.modules.location.LocationModule  ApiException $expo.modules.location.LocationModule  AppForegroundedSingleton $expo.modules.location.LocationModule  	ArrayList $expo.modules.location.LocationModule  Boolean $expo.modules.location.LocationModule  Build $expo.modules.location.LocationModule  Bundle $expo.modules.location.LocationModule  CHECK_SETTINGS_REQUEST_CODE $expo.modules.location.LocationModule  ChecksSdkIntAtLeast $expo.modules.location.LocationModule  CommonStatusCodes $expo.modules.location.LocationModule  	Companion $expo.modules.location.LocationModule  Context $expo.modules.location.LocationModule  	Coroutine $expo.modules.location.LocationModule  DEGREE_DELTA $expo.modules.location.LocationModule  	Exception $expo.modules.location.LocationModule  
Exceptions $expo.modules.location.LocationModule  Float $expo.modules.location.LocationModule  
FloatArray $expo.modules.location.LocationModule  %ForegroundServicePermissionsException $expo.modules.location.LocationModule  )ForegroundServiceStartNotAllowedException $expo.modules.location.LocationModule  FusedLocationProviderClient $expo.modules.location.LocationModule  GEOFENCING_EVENT_ENTER $expo.modules.location.LocationModule  GEOFENCING_EVENT_EXIT $expo.modules.location.LocationModule  GeocodeException $expo.modules.location.LocationModule  GeocodeResponse $expo.modules.location.LocationModule  Geocoder $expo.modules.location.LocationModule  GeofencingOptions $expo.modules.location.LocationModule  GeofencingTaskConsumer $expo.modules.location.LocationModule  GeomagneticField $expo.modules.location.LocationModule  HEADING_EVENT_NAME $expo.modules.location.LocationModule  HashMap $expo.modules.location.LocationModule  Heading $expo.modules.location.LocationModule  HeadingEventResponse $expo.modules.location.LocationModule  Int $expo.modules.location.LocationModule  Intent $expo.modules.location.LocationModule  LOCATION_EVENT_NAME $expo.modules.location.LocationModule  List $expo.modules.location.LocationModule  Locale $expo.modules.location.LocationModule  Location $expo.modules.location.LocationModule  LocationActivityResultListener $expo.modules.location.LocationModule  LocationAvailability $expo.modules.location.LocationModule  'LocationBackgroundUnauthorizedException $expo.modules.location.LocationModule  LocationCallback $expo.modules.location.LocationModule  LocationHelpers $expo.modules.location.LocationModule  LocationLastKnownOptions $expo.modules.location.LocationModule  LocationManager $expo.modules.location.LocationModule  LocationManagerCompat $expo.modules.location.LocationModule  LocationModule $expo.modules.location.LocationModule  LocationOptions $expo.modules.location.LocationModule  LocationProviderStatus $expo.modules.location.LocationModule  LocationRequest $expo.modules.location.LocationModule  LocationRequestCallbacks $expo.modules.location.LocationModule   LocationRequestRejectedException $expo.modules.location.LocationModule  LocationResponse $expo.modules.location.LocationModule  LocationResult $expo.modules.location.LocationModule  LocationServices $expo.modules.location.LocationModule  LocationSettingsRequest $expo.modules.location.LocationModule  $LocationSettingsUnsatisfiedException $expo.modules.location.LocationModule  LocationTaskConsumer $expo.modules.location.LocationModule  LocationTaskOptions $expo.modules.location.LocationModule  LocationUnauthorizedException $expo.modules.location.LocationModule  LocationUnavailableException $expo.modules.location.LocationModule  LocationUnknownException $expo.modules.location.LocationModule  Log $expo.modules.location.LocationModule  Long $expo.modules.location.LocationModule  Looper $expo.modules.location.LocationModule  Manifest $expo.modules.location.LocationModule  Math $expo.modules.location.LocationModule  MissingUIManagerException $expo.modules.location.LocationModule  ModuleDefinition $expo.modules.location.LocationModule  NoGeocodeException $expo.modules.location.LocationModule  NoPermissionInManifestException $expo.modules.location.LocationModule  NoPermissionsModuleException $expo.modules.location.LocationModule  PackageManager $expo.modules.location.LocationModule   PermissionDetailsLocationAndroid $expo.modules.location.LocationModule  PermissionRequestResponse $expo.modules.location.LocationModule  Promise $expo.modules.location.LocationModule  ResolvableApiException $expo.modules.location.LocationModule  ReverseGeocodeLocation $expo.modules.location.LocationModule  ReverseGeocodeResponse $expo.modules.location.LocationModule  SecurityException $expo.modules.location.LocationModule  Sensor $expo.modules.location.LocationModule  SensorEvent $expo.modules.location.LocationModule  
SensorManager $expo.modules.location.LocationModule  SensorManagerUnavailable $expo.modules.location.LocationModule  String $expo.modules.location.LocationModule  System $expo.modules.location.LocationModule  TAG $expo.modules.location.LocationModule  
TIME_DELTA $expo.modules.location.LocationModule  TaskManagerInterface $expo.modules.location.LocationModule  TaskManagerNotFoundException $expo.modules.location.LocationModule  	Throwable $expo.modules.location.LocationModule  	UIManager $expo.modules.location.LocationModule  abs $expo.modules.location.LocationModule  addPendingLocationRequest $expo.modules.location.LocationModule  
appContext $expo.modules.location.LocationModule  apply $expo.modules.location.LocationModule  'askForPermissionsWithPermissionsManager $expo.modules.location.LocationModule  bundleOf $expo.modules.location.LocationModule  calcMagNorth $expo.modules.location.LocationModule  
calcTrueNorth $expo.modules.location.LocationModule  destroyHeadingWatch $expo.modules.location.LocationModule  	emptyList $expo.modules.location.LocationModule  executePendingRequests $expo.modules.location.LocationModule  from $expo.modules.location.LocationModule  geocode $expo.modules.location.LocationModule  getBackgroundPermissionsAsync $expo.modules.location.LocationModule  getCurrentPositionAsync $expo.modules.location.LocationModule  getForegroundPermissionsAsync $expo.modules.location.LocationModule  getLastKnownLocation $expo.modules.location.LocationModule  getLastKnownPositionAsync $expo.modules.location.LocationModule  $getPermissionsWithPermissionsManager $expo.modules.location.LocationModule  getProviderStatus $expo.modules.location.LocationModule  getValue $expo.modules.location.LocationModule  hasForegroundServicePermissions $expo.modules.location.LocationModule  hasNetworkProviderEnabled $expo.modules.location.LocationModule  isAnyProviderAvailable $expo.modules.location.LocationModule   isBackgroundPermissionInManifest $expo.modules.location.LocationModule  isLocationValid $expo.modules.location.LocationModule  isMissingBackgroundPermissions $expo.modules.location.LocationModule  isMissingForegroundPermissions $expo.modules.location.LocationModule  java $expo.modules.location.LocationModule  lazy $expo.modules.location.LocationModule  let $expo.modules.location.LocationModule  	mAccuracy $expo.modules.location.LocationModule  mContext $expo.modules.location.LocationModule  mGeocoderPaused $expo.modules.location.LocationModule  	mGeofield $expo.modules.location.LocationModule  mGeomagnetic $expo.modules.location.LocationModule  mGravity $expo.modules.location.LocationModule  
mHeadingId $expo.modules.location.LocationModule  mLastAzimuth $expo.modules.location.LocationModule  mLastUpdate $expo.modules.location.LocationModule  mLocationCallbacks $expo.modules.location.LocationModule  mLocationProvider $expo.modules.location.LocationModule  mLocationRequests $expo.modules.location.LocationModule  mPendingLocationRequests $expo.modules.location.LocationModule  mSensorManager $expo.modules.location.LocationModule  mTaskManager $expo.modules.location.LocationModule  
mUIManager $expo.modules.location.LocationModule  
mapNotNull $expo.modules.location.LocationModule  pauseLocationUpdatesForRequest $expo.modules.location.LocationModule  prepareCurrentLocationRequest $expo.modules.location.LocationModule  prepareLocationRequest $expo.modules.location.LocationModule  provideDelegate $expo.modules.location.LocationModule  removeLocationUpdatesForRequest $expo.modules.location.LocationModule  !requestBackgroundPermissionsAsync $expo.modules.location.LocationModule  requestContinuousUpdates $expo.modules.location.LocationModule  requestLocationUpdates $expo.modules.location.LocationModule  requestSingleLocation $expo.modules.location.LocationModule  resolveUserSettingsForRequest $expo.modules.location.LocationModule  resume $expo.modules.location.LocationModule  resumeLocationUpdates $expo.modules.location.LocationModule  resumeWithException $expo.modules.location.LocationModule  reverseGeocode $expo.modules.location.LocationModule  	sendEvent $expo.modules.location.LocationModule  sendLocationResponse $expo.modules.location.LocationModule  
sendUpdate $expo.modules.location.LocationModule  set $expo.modules.location.LocationModule  shouldAskBackgroundPermissions $expo.modules.location.LocationModule  startHeadingUpdate $expo.modules.location.LocationModule  
startWatching $expo.modules.location.LocationModule  stopHeadingWatch $expo.modules.location.LocationModule  stopWatching $expo.modules.location.LocationModule  suspendCoroutine $expo.modules.location.LocationModule  takeIf $expo.modules.location.LocationModule  ACCURACY_BALANCED .expo.modules.location.LocationModule.Companion  ACCURACY_BEST_FOR_NAVIGATION .expo.modules.location.LocationModule.Companion  
ACCURACY_HIGH .expo.modules.location.LocationModule.Companion  ACCURACY_HIGHEST .expo.modules.location.LocationModule.Companion  ACCURACY_LOW .expo.modules.location.LocationModule.Companion  ACCURACY_LOWEST .expo.modules.location.LocationModule.Companion  Activity .expo.modules.location.LocationModule.Companion  ActivityCompat .expo.modules.location.LocationModule.Companion  AppForegroundedSingleton .expo.modules.location.LocationModule.Companion  	ArrayList .expo.modules.location.LocationModule.Companion  Build .expo.modules.location.LocationModule.Companion  Bundle .expo.modules.location.LocationModule.Companion  CHECK_SETTINGS_REQUEST_CODE .expo.modules.location.LocationModule.Companion  CommonStatusCodes .expo.modules.location.LocationModule.Companion  Context .expo.modules.location.LocationModule.Companion  	Coroutine .expo.modules.location.LocationModule.Companion  DEGREE_DELTA .expo.modules.location.LocationModule.Companion  
Exceptions .expo.modules.location.LocationModule.Companion  
FloatArray .expo.modules.location.LocationModule.Companion  %ForegroundServicePermissionsException .expo.modules.location.LocationModule.Companion  )ForegroundServiceStartNotAllowedException .expo.modules.location.LocationModule.Companion  GEOFENCING_EVENT_ENTER .expo.modules.location.LocationModule.Companion  GEOFENCING_EVENT_EXIT .expo.modules.location.LocationModule.Companion  GeocodeException .expo.modules.location.LocationModule.Companion  GeocodeResponse .expo.modules.location.LocationModule.Companion  Geocoder .expo.modules.location.LocationModule.Companion  GeofencingTaskConsumer .expo.modules.location.LocationModule.Companion  GeomagneticField .expo.modules.location.LocationModule.Companion  HEADING_EVENT_NAME .expo.modules.location.LocationModule.Companion  HashMap .expo.modules.location.LocationModule.Companion  Heading .expo.modules.location.LocationModule.Companion  HeadingEventResponse .expo.modules.location.LocationModule.Companion  LOCATION_EVENT_NAME .expo.modules.location.LocationModule.Companion  Locale .expo.modules.location.LocationModule.Companion  Location .expo.modules.location.LocationModule.Companion  'LocationBackgroundUnauthorizedException .expo.modules.location.LocationModule.Companion  LocationHelpers .expo.modules.location.LocationModule.Companion  LocationManager .expo.modules.location.LocationModule.Companion  LocationManagerCompat .expo.modules.location.LocationModule.Companion  LocationModule .expo.modules.location.LocationModule.Companion  LocationOptions .expo.modules.location.LocationModule.Companion  LocationProviderStatus .expo.modules.location.LocationModule.Companion  LocationRequest .expo.modules.location.LocationModule.Companion   LocationRequestRejectedException .expo.modules.location.LocationModule.Companion  LocationResponse .expo.modules.location.LocationModule.Companion  LocationServices .expo.modules.location.LocationModule.Companion  LocationSettingsRequest .expo.modules.location.LocationModule.Companion  $LocationSettingsUnsatisfiedException .expo.modules.location.LocationModule.Companion  LocationTaskConsumer .expo.modules.location.LocationModule.Companion  LocationUnauthorizedException .expo.modules.location.LocationModule.Companion  LocationUnavailableException .expo.modules.location.LocationModule.Companion  LocationUnknownException .expo.modules.location.LocationModule.Companion  Log .expo.modules.location.LocationModule.Companion  Looper .expo.modules.location.LocationModule.Companion  Manifest .expo.modules.location.LocationModule.Companion  Math .expo.modules.location.LocationModule.Companion  MissingUIManagerException .expo.modules.location.LocationModule.Companion  ModuleDefinition .expo.modules.location.LocationModule.Companion  NoGeocodeException .expo.modules.location.LocationModule.Companion  NoPermissionInManifestException .expo.modules.location.LocationModule.Companion  NoPermissionsModuleException .expo.modules.location.LocationModule.Companion  PackageManager .expo.modules.location.LocationModule.Companion   PermissionDetailsLocationAndroid .expo.modules.location.LocationModule.Companion  PermissionRequestResponse .expo.modules.location.LocationModule.Companion  ReverseGeocodeResponse .expo.modules.location.LocationModule.Companion  Sensor .expo.modules.location.LocationModule.Companion  
SensorManager .expo.modules.location.LocationModule.Companion  SensorManagerUnavailable .expo.modules.location.LocationModule.Companion  System .expo.modules.location.LocationModule.Companion  TAG .expo.modules.location.LocationModule.Companion  
TIME_DELTA .expo.modules.location.LocationModule.Companion  TaskManagerNotFoundException .expo.modules.location.LocationModule.Companion  abs .expo.modules.location.LocationModule.Companion  addPendingLocationRequest .expo.modules.location.LocationModule.Companion  
appContext .expo.modules.location.LocationModule.Companion  apply .expo.modules.location.LocationModule.Companion  'askForPermissionsWithPermissionsManager .expo.modules.location.LocationModule.Companion  bundleOf .expo.modules.location.LocationModule.Companion  destroyHeadingWatch .expo.modules.location.LocationModule.Companion  	emptyList .expo.modules.location.LocationModule.Companion  from .expo.modules.location.LocationModule.Companion  geocode .expo.modules.location.LocationModule.Companion  getBackgroundPermissionsAsync .expo.modules.location.LocationModule.Companion  getCurrentPositionAsync .expo.modules.location.LocationModule.Companion  getForegroundPermissionsAsync .expo.modules.location.LocationModule.Companion  getLastKnownPositionAsync .expo.modules.location.LocationModule.Companion  $getPermissionsWithPermissionsManager .expo.modules.location.LocationModule.Companion  getProviderStatus .expo.modules.location.LocationModule.Companion  getValue .expo.modules.location.LocationModule.Companion  hasForegroundServicePermissions .expo.modules.location.LocationModule.Companion  hasNetworkProviderEnabled .expo.modules.location.LocationModule.Companion  isAnyProviderAvailable .expo.modules.location.LocationModule.Companion  isLocationValid .expo.modules.location.LocationModule.Companion  isMissingBackgroundPermissions .expo.modules.location.LocationModule.Companion  isMissingForegroundPermissions .expo.modules.location.LocationModule.Companion  java .expo.modules.location.LocationModule.Companion  lazy .expo.modules.location.LocationModule.Companion  let .expo.modules.location.LocationModule.Companion  mContext .expo.modules.location.LocationModule.Companion  	mGeofield .expo.modules.location.LocationModule.Companion  
mHeadingId .expo.modules.location.LocationModule.Companion  mLocationProvider .expo.modules.location.LocationModule.Companion  mSensorManager .expo.modules.location.LocationModule.Companion  mTaskManager .expo.modules.location.LocationModule.Companion  
mUIManager .expo.modules.location.LocationModule.Companion  
mapNotNull .expo.modules.location.LocationModule.Companion  prepareCurrentLocationRequest .expo.modules.location.LocationModule.Companion  prepareLocationRequest .expo.modules.location.LocationModule.Companion  provideDelegate .expo.modules.location.LocationModule.Companion  removeLocationUpdatesForRequest .expo.modules.location.LocationModule.Companion  !requestBackgroundPermissionsAsync .expo.modules.location.LocationModule.Companion  requestContinuousUpdates .expo.modules.location.LocationModule.Companion  requestSingleLocation .expo.modules.location.LocationModule.Companion  resume .expo.modules.location.LocationModule.Companion  resumeWithException .expo.modules.location.LocationModule.Companion  reverseGeocode .expo.modules.location.LocationModule.Companion  set .expo.modules.location.LocationModule.Companion  startHeadingUpdate .expo.modules.location.LocationModule.Companion  suspendCoroutine .expo.modules.location.LocationModule.Companion  takeIf .expo.modules.location.LocationModule.Companion  distance $expo.modules.location.LocationParams  interval $expo.modules.location.LocationParams  onLocationChanged .expo.modules.location.LocationRequestCallbacks  onLocationError .expo.modules.location.LocationRequestCallbacks  onRequestFailed .expo.modules.location.LocationRequestCallbacks  onRequestSuccess .expo.modules.location.LocationRequestCallbacks  ACCURACY_BALANCED expo.modules.location.records  Address expo.modules.location.records  Any expo.modules.location.records  
BaseBundle expo.modules.location.records  Boolean expo.modules.location.records  Build expo.modules.location.records  Bundle expo.modules.location.records  
BundleType expo.modules.location.records  Class expo.modules.location.records  ConversionException expo.modules.location.records  Double expo.modules.location.records  
Enumerable expo.modules.location.records  	Exception expo.modules.location.records  Field expo.modules.location.records  Float expo.modules.location.records  GeocodeResponse expo.modules.location.records  GeofencingOptions expo.modules.location.records  GeofencingRegionState expo.modules.location.records  Heading expo.modules.location.records  HeadingEventResponse expo.modules.location.records  IllegalAccessException expo.modules.location.records  InstantiationException expo.modules.location.records  Int expo.modules.location.records  List expo.modules.location.records  Location expo.modules.location.records  LocationLastKnownOptions expo.modules.location.records  LocationModule expo.modules.location.records  LocationObjectCoords expo.modules.location.records  LocationOptions expo.modules.location.records  LocationProviderStatus expo.modules.location.records  LocationResponse expo.modules.location.records  LocationTaskOptions expo.modules.location.records  LocationTaskServiceOptions expo.modules.location.records  Log expo.modules.location.records  Long expo.modules.location.records  Map expo.modules.location.records  
MutableMap expo.modules.location.records   PermissionDetailsLocationAndroid expo.modules.location.records  PermissionRequestResponse expo.modules.location.records  PersistableBundle expo.modules.location.records  Record expo.modules.location.records  Region expo.modules.location.records  ReverseGeocodeLocation expo.modules.location.records  ReverseGeocodeResponse expo.modules.location.records  Serializable expo.modules.location.records  String expo.modules.location.records  
StringBuilder expo.modules.location.records  accuracy expo.modules.location.records  altitude expo.modules.location.records  altitudeAccuracy expo.modules.location.records  apply expo.modules.location.records  constructFormattedAddress expo.modules.location.records  coords expo.modules.location.records  foregroundService expo.modules.location.records  heading expo.modules.location.records  java expo.modules.location.records  latitude expo.modules.location.records  let expo.modules.location.records  	longitude expo.modules.location.records  
magHeading expo.modules.location.records  map expo.modules.location.records  mapOf expo.modules.location.records  mocked expo.modules.location.records  mutableMapOf expo.modules.location.records  set expo.modules.location.records  speed expo.modules.location.records  	timestamp expo.modules.location.records  to expo.modules.location.records  trueHeading expo.modules.location.records  watchId expo.modules.location.records  	Companion -expo.modules.location.records.GeocodeResponse  Double -expo.modules.location.records.GeocodeResponse  	Exception -expo.modules.location.records.GeocodeResponse  Field -expo.modules.location.records.GeocodeResponse  Float -expo.modules.location.records.GeocodeResponse  GeocodeResponse -expo.modules.location.records.GeocodeResponse  IllegalAccessException -expo.modules.location.records.GeocodeResponse  InstantiationException -expo.modules.location.records.GeocodeResponse  Location -expo.modules.location.records.GeocodeResponse  LocationModule -expo.modules.location.records.GeocodeResponse  Log -expo.modules.location.records.GeocodeResponse  from -expo.modules.location.records.GeocodeResponse  GeocodeResponse 7expo.modules.location.records.GeocodeResponse.Companion  LocationModule 7expo.modules.location.records.GeocodeResponse.Companion  Log 7expo.modules.location.records.GeocodeResponse.Companion  from 7expo.modules.location.records.GeocodeResponse.Companion  map /expo.modules.location.records.GeofencingOptions  mapOf /expo.modules.location.records.GeofencingOptions  regions /expo.modules.location.records.GeofencingOptions  to /expo.modules.location.records.GeofencingOptions  toMap /expo.modules.location.records.GeofencingOptions  INSIDE 3expo.modules.location.records.GeofencingRegionState  OUTSIDE 3expo.modules.location.records.GeofencingRegionState  UNKNOWN 3expo.modules.location.records.GeofencingRegionState  ordinal 3expo.modules.location.records.GeofencingRegionState  Bundle %expo.modules.location.records.Heading  accuracy %expo.modules.location.records.Heading  apply %expo.modules.location.records.Heading  let %expo.modules.location.records.Heading  
magHeading %expo.modules.location.records.Heading  toBundle %expo.modules.location.records.Heading  trueHeading %expo.modules.location.records.Heading  Bundle 2expo.modules.location.records.HeadingEventResponse  apply 2expo.modules.location.records.HeadingEventResponse  heading 2expo.modules.location.records.HeadingEventResponse  let 2expo.modules.location.records.HeadingEventResponse  toBundle 2expo.modules.location.records.HeadingEventResponse  watchId 2expo.modules.location.records.HeadingEventResponse  maxAge 6expo.modules.location.records.LocationLastKnownOptions  requiredAccuracy 6expo.modules.location.records.LocationLastKnownOptions  Build 2expo.modules.location.records.LocationObjectCoords  Bundle 2expo.modules.location.records.LocationObjectCoords  ConversionException 2expo.modules.location.records.LocationObjectCoords  LocationObjectCoords 2expo.modules.location.records.LocationObjectCoords  PersistableBundle 2expo.modules.location.records.LocationObjectCoords  accuracy 2expo.modules.location.records.LocationObjectCoords  altitude 2expo.modules.location.records.LocationObjectCoords  altitudeAccuracy 2expo.modules.location.records.LocationObjectCoords  apply 2expo.modules.location.records.LocationObjectCoords  heading 2expo.modules.location.records.LocationObjectCoords  java 2expo.modules.location.records.LocationObjectCoords  latitude 2expo.modules.location.records.LocationObjectCoords  let 2expo.modules.location.records.LocationObjectCoords  	longitude 2expo.modules.location.records.LocationObjectCoords  speed 2expo.modules.location.records.LocationObjectCoords  toBundle 2expo.modules.location.records.LocationObjectCoords  ACCURACY_BALANCED -expo.modules.location.records.LocationOptions  accuracy -expo.modules.location.records.LocationOptions  distanceInterval -expo.modules.location.records.LocationOptions  mayShowUserSettingsDialog -expo.modules.location.records.LocationOptions  timeInterval -expo.modules.location.records.LocationOptions  apply 4expo.modules.location.records.LocationProviderStatus  backgroundModeEnabled 4expo.modules.location.records.LocationProviderStatus  gpsAvailable 4expo.modules.location.records.LocationProviderStatus  locationServicesEnabled 4expo.modules.location.records.LocationProviderStatus  networkAvailable 4expo.modules.location.records.LocationProviderStatus  passiveAvailable 4expo.modules.location.records.LocationProviderStatus  Bundle .expo.modules.location.records.LocationResponse  ConversionException .expo.modules.location.records.LocationResponse  LocationObjectCoords .expo.modules.location.records.LocationResponse  LocationResponse .expo.modules.location.records.LocationResponse  PersistableBundle .expo.modules.location.records.LocationResponse  apply .expo.modules.location.records.LocationResponse  coords .expo.modules.location.records.LocationResponse  java .expo.modules.location.records.LocationResponse  let .expo.modules.location.records.LocationResponse  mocked .expo.modules.location.records.LocationResponse  	timestamp .expo.modules.location.records.LocationResponse  toBundle .expo.modules.location.records.LocationResponse  accuracy 1expo.modules.location.records.LocationTaskOptions  apply 1expo.modules.location.records.LocationTaskOptions  deferredUpdatesDistance 1expo.modules.location.records.LocationTaskOptions  deferredUpdatesInterval 1expo.modules.location.records.LocationTaskOptions  deferredUpdatesTimeout 1expo.modules.location.records.LocationTaskOptions  distanceInterval 1expo.modules.location.records.LocationTaskOptions  foregroundService 1expo.modules.location.records.LocationTaskOptions  let 1expo.modules.location.records.LocationTaskOptions  mayShowUserSettingsDialog 1expo.modules.location.records.LocationTaskOptions  mutableMapOf 1expo.modules.location.records.LocationTaskOptions  set 1expo.modules.location.records.LocationTaskOptions  timeInterval 1expo.modules.location.records.LocationTaskOptions  to 1expo.modules.location.records.LocationTaskOptions  toMutableMap 1expo.modules.location.records.LocationTaskOptions  killServiceOnDestroy 8expo.modules.location.records.LocationTaskServiceOptions  let 8expo.modules.location.records.LocationTaskServiceOptions  mutableMapOf 8expo.modules.location.records.LocationTaskServiceOptions  notificationBody 8expo.modules.location.records.LocationTaskServiceOptions  notificationColor 8expo.modules.location.records.LocationTaskServiceOptions  notificationTitle 8expo.modules.location.records.LocationTaskServiceOptions  to 8expo.modules.location.records.LocationTaskServiceOptions  toMutableMap 8expo.modules.location.records.LocationTaskServiceOptions  Bundle 7expo.modules.location.records.PermissionRequestResponse  ConversionException 7expo.modules.location.records.PermissionRequestResponse   PermissionDetailsLocationAndroid 7expo.modules.location.records.PermissionRequestResponse  PermissionRequestResponse 7expo.modules.location.records.PermissionRequestResponse  android 7expo.modules.location.records.PermissionRequestResponse  granted 7expo.modules.location.records.PermissionRequestResponse  java 7expo.modules.location.records.PermissionRequestResponse  let 7expo.modules.location.records.PermissionRequestResponse  
identifier $expo.modules.location.records.Region  latitude $expo.modules.location.records.Region  	longitude $expo.modules.location.records.Region  mapOf $expo.modules.location.records.Region  
notifyOnEnter $expo.modules.location.records.Region  notifyOnExit $expo.modules.location.records.Region  radius $expo.modules.location.records.Region  state $expo.modules.location.records.Region  to $expo.modules.location.records.Region  toMap $expo.modules.location.records.Region  latitude 4expo.modules.location.records.ReverseGeocodeLocation  	longitude 4expo.modules.location.records.ReverseGeocodeLocation  Address 4expo.modules.location.records.ReverseGeocodeResponse  Field 4expo.modules.location.records.ReverseGeocodeResponse  String 4expo.modules.location.records.ReverseGeocodeResponse  
StringBuilder 4expo.modules.location.records.ReverseGeocodeResponse  constructFormattedAddress 4expo.modules.location.records.ReverseGeocodeResponse  
StringBuilder >expo.modules.location.records.ReverseGeocodeResponse.Companion  constructFormattedAddress >expo.modules.location.records.ReverseGeocodeResponse.Companion  Binder expo.modules.location.services  Build expo.modules.location.services  Bundle expo.modules.location.services  Color expo.modules.location.services  Context expo.modules.location.services  	Exception expo.modules.location.services  IBinder expo.modules.location.services  Int expo.modules.location.services  Intent expo.modules.location.services  LocationTaskService expo.modules.location.services  NOTIFICATION_SERVICE expo.modules.location.services  Notification expo.modules.location.services  NotificationChannel expo.modules.location.services  NotificationManager expo.modules.location.services  
PendingIntent expo.modules.location.services  START_REDELIVER_INTENT expo.modules.location.services  Service expo.modules.location.services  String expo.modules.location.services  	TargetApi expo.modules.location.services  let expo.modules.location.services  plus expo.modules.location.services  run expo.modules.location.services  
sServiceId expo.modules.location.services  Binder 2expo.modules.location.services.LocationTaskService  Build 2expo.modules.location.services.LocationTaskService  Bundle 2expo.modules.location.services.LocationTaskService  Color 2expo.modules.location.services.LocationTaskService  	Companion 2expo.modules.location.services.LocationTaskService  Context 2expo.modules.location.services.LocationTaskService  	Exception 2expo.modules.location.services.LocationTaskService  IBinder 2expo.modules.location.services.LocationTaskService  Int 2expo.modules.location.services.LocationTaskService  Intent 2expo.modules.location.services.LocationTaskService  LocationTaskService 2expo.modules.location.services.LocationTaskService  NOTIFICATION_SERVICE 2expo.modules.location.services.LocationTaskService  Notification 2expo.modules.location.services.LocationTaskService  NotificationChannel 2expo.modules.location.services.LocationTaskService  NotificationManager 2expo.modules.location.services.LocationTaskService  
PendingIntent 2expo.modules.location.services.LocationTaskService  START_REDELIVER_INTENT 2expo.modules.location.services.LocationTaskService  
ServiceBinder 2expo.modules.location.services.LocationTaskService  String 2expo.modules.location.services.LocationTaskService  	TargetApi 2expo.modules.location.services.LocationTaskService  applicationInfo 2expo.modules.location.services.LocationTaskService  buildServiceNotification 2expo.modules.location.services.LocationTaskService  colorStringToInteger 2expo.modules.location.services.LocationTaskService  getSystemService 2expo.modules.location.services.LocationTaskService  let 2expo.modules.location.services.LocationTaskService  mBinder 2expo.modules.location.services.LocationTaskService  
mChannelId 2expo.modules.location.services.LocationTaskService  mKillService 2expo.modules.location.services.LocationTaskService  mParentContext 2expo.modules.location.services.LocationTaskService  
mServiceId 2expo.modules.location.services.LocationTaskService  packageManager 2expo.modules.location.services.LocationTaskService  plus 2expo.modules.location.services.LocationTaskService  prepareChannel 2expo.modules.location.services.LocationTaskService  run 2expo.modules.location.services.LocationTaskService  
sServiceId 2expo.modules.location.services.LocationTaskService  setParentContext 2expo.modules.location.services.LocationTaskService  startForeground 2expo.modules.location.services.LocationTaskService  stop 2expo.modules.location.services.LocationTaskService  stopForeground 2expo.modules.location.services.LocationTaskService  stopSelf 2expo.modules.location.services.LocationTaskService  Build <expo.modules.location.services.LocationTaskService.Companion  Color <expo.modules.location.services.LocationTaskService.Companion  Intent <expo.modules.location.services.LocationTaskService.Companion  NOTIFICATION_SERVICE <expo.modules.location.services.LocationTaskService.Companion  Notification <expo.modules.location.services.LocationTaskService.Companion  NotificationChannel <expo.modules.location.services.LocationTaskService.Companion  NotificationManager <expo.modules.location.services.LocationTaskService.Companion  
PendingIntent <expo.modules.location.services.LocationTaskService.Companion  START_REDELIVER_INTENT <expo.modules.location.services.LocationTaskService.Companion  let <expo.modules.location.services.LocationTaskService.Companion  plus <expo.modules.location.services.LocationTaskService.Companion  run <expo.modules.location.services.LocationTaskService.Companion  
sServiceId <expo.modules.location.services.LocationTaskService.Companion  service @expo.modules.location.services.LocationTaskService.ServiceBinder  Any #expo.modules.location.taskConsumers  AppForegroundedSingleton #expo.modules.location.taskConsumers  	Arguments #expo.modules.location.taskConsumers  	ArrayList #expo.modules.location.taskConsumers  Boolean #expo.modules.location.taskConsumers  Build #expo.modules.location.taskConsumers  Bundle #expo.modules.location.taskConsumers  
ComponentName #expo.modules.location.taskConsumers  Context #expo.modules.location.taskConsumers  Double #expo.modules.location.taskConsumers  Error #expo.modules.location.taskConsumers  FOREGROUND_SERVICE_KEY #expo.modules.location.taskConsumers  Float #expo.modules.location.taskConsumers  FusedLocationProviderClient #expo.modules.location.taskConsumers  Geofence #expo.modules.location.taskConsumers  GeofenceStatusCodes #expo.modules.location.taskConsumers  GeofencingClient #expo.modules.location.taskConsumers  GeofencingEvent #expo.modules.location.taskConsumers  GeofencingException #expo.modules.location.taskConsumers  GeofencingRegionState #expo.modules.location.taskConsumers  GeofencingRequest #expo.modules.location.taskConsumers  GeofencingTaskConsumer #expo.modules.location.taskConsumers  HashMap #expo.modules.location.taskConsumers  IBinder #expo.modules.location.taskConsumers  Int #expo.modules.location.taskConsumers  Intent #expo.modules.location.taskConsumers  
JobParameters #expo.modules.location.taskConsumers  
JobService #expo.modules.location.taskConsumers  LifecycleEventListener #expo.modules.location.taskConsumers  List #expo.modules.location.taskConsumers  Location #expo.modules.location.taskConsumers  LocationHelpers #expo.modules.location.taskConsumers  LocationModule #expo.modules.location.taskConsumers  LocationOptions #expo.modules.location.taskConsumers  LocationRequest #expo.modules.location.taskConsumers  LocationResponse #expo.modules.location.taskConsumers  LocationResult #expo.modules.location.taskConsumers  LocationServices #expo.modules.location.taskConsumers  LocationTaskConsumer #expo.modules.location.taskConsumers  LocationTaskService #expo.modules.location.taskConsumers  Log #expo.modules.location.taskConsumers  Long #expo.modules.location.taskConsumers  Map #expo.modules.location.taskConsumers  MapArguments #expo.modules.location.taskConsumers  	MapHelper #expo.modules.location.taskConsumers  MutableList #expo.modules.location.taskConsumers  
MutableMap #expo.modules.location.taskConsumers  
PendingIntent #expo.modules.location.taskConsumers  PersistableBundle #expo.modules.location.taskConsumers  ReadableArguments #expo.modules.location.taskConsumers  SecurityException #expo.modules.location.taskConsumers  
ServiceBinder #expo.modules.location.taskConsumers  ServiceConnection #expo.modules.location.taskConsumers  String #expo.modules.location.taskConsumers  TAG #expo.modules.location.taskConsumers  TaskConsumer #expo.modules.location.taskConsumers  TaskConsumerInterface #expo.modules.location.taskConsumers  TaskExecutionCallback #expo.modules.location.taskConsumers  
TaskInterface #expo.modules.location.taskConsumers  TaskManagerUtilsInterface #expo.modules.location.taskConsumers  UUID #expo.modules.location.taskConsumers  abs #expo.modules.location.taskConsumers  apply #expo.modules.location.taskConsumers  context #expo.modules.location.taskConsumers  filterIsInstance #expo.modules.location.taskConsumers  getErrorString #expo.modules.location.taskConsumers  getParamAsDouble #expo.modules.location.taskConsumers  getValue #expo.modules.location.taskConsumers  isAnyProviderAvailable #expo.modules.location.taskConsumers  java #expo.modules.location.taskConsumers  lazy #expo.modules.location.taskConsumers  let #expo.modules.location.taskConsumers  listOf #expo.modules.location.taskConsumers  mService #expo.modules.location.taskConsumers  
plusAssign #expo.modules.location.taskConsumers  prepareLocationRequest #expo.modules.location.taskConsumers  provideDelegate #expo.modules.location.taskConsumers  run #expo.modules.location.taskConsumers  sLastTimestamp #expo.modules.location.taskConsumers  set #expo.modules.location.taskConsumers  shouldUseForegroundService #expo.modules.location.taskConsumers  toDoubleOrNull #expo.modules.location.taskConsumers  Any :expo.modules.location.taskConsumers.GeofencingTaskConsumer  	ArrayList :expo.modules.location.taskConsumers.GeofencingTaskConsumer  Boolean :expo.modules.location.taskConsumers.GeofencingTaskConsumer  Bundle :expo.modules.location.taskConsumers.GeofencingTaskConsumer  	Companion :expo.modules.location.taskConsumers.GeofencingTaskConsumer  Context :expo.modules.location.taskConsumers.GeofencingTaskConsumer  Double :expo.modules.location.taskConsumers.GeofencingTaskConsumer  Error :expo.modules.location.taskConsumers.GeofencingTaskConsumer  Float :expo.modules.location.taskConsumers.GeofencingTaskConsumer  Geofence :expo.modules.location.taskConsumers.GeofencingTaskConsumer  GeofenceStatusCodes :expo.modules.location.taskConsumers.GeofencingTaskConsumer  GeofencingClient :expo.modules.location.taskConsumers.GeofencingTaskConsumer  GeofencingEvent :expo.modules.location.taskConsumers.GeofencingTaskConsumer  GeofencingException :expo.modules.location.taskConsumers.GeofencingTaskConsumer  GeofencingRegionState :expo.modules.location.taskConsumers.GeofencingTaskConsumer  GeofencingRequest :expo.modules.location.taskConsumers.GeofencingTaskConsumer  HashMap :expo.modules.location.taskConsumers.GeofencingTaskConsumer  Int :expo.modules.location.taskConsumers.GeofencingTaskConsumer  Intent :expo.modules.location.taskConsumers.GeofencingTaskConsumer  
JobParameters :expo.modules.location.taskConsumers.GeofencingTaskConsumer  
JobService :expo.modules.location.taskConsumers.GeofencingTaskConsumer  List :expo.modules.location.taskConsumers.GeofencingTaskConsumer  LocationHelpers :expo.modules.location.taskConsumers.GeofencingTaskConsumer  LocationModule :expo.modules.location.taskConsumers.GeofencingTaskConsumer  LocationServices :expo.modules.location.taskConsumers.GeofencingTaskConsumer  Log :expo.modules.location.taskConsumers.GeofencingTaskConsumer  Long :expo.modules.location.taskConsumers.GeofencingTaskConsumer  Map :expo.modules.location.taskConsumers.GeofencingTaskConsumer  MutableList :expo.modules.location.taskConsumers.GeofencingTaskConsumer  
MutableMap :expo.modules.location.taskConsumers.GeofencingTaskConsumer  
PendingIntent :expo.modules.location.taskConsumers.GeofencingTaskConsumer  PersistableBundle :expo.modules.location.taskConsumers.GeofencingTaskConsumer  SecurityException :expo.modules.location.taskConsumers.GeofencingTaskConsumer  String :expo.modules.location.taskConsumers.GeofencingTaskConsumer  TAG :expo.modules.location.taskConsumers.GeofencingTaskConsumer  
TaskInterface :expo.modules.location.taskConsumers.GeofencingTaskConsumer  TaskManagerUtilsInterface :expo.modules.location.taskConsumers.GeofencingTaskConsumer  UUID :expo.modules.location.taskConsumers.GeofencingTaskConsumer  apply :expo.modules.location.taskConsumers.GeofencingTaskConsumer  bundleFromRegion :expo.modules.location.taskConsumers.GeofencingTaskConsumer  context :expo.modules.location.taskConsumers.GeofencingTaskConsumer  eventTypeFromTransitionType :expo.modules.location.taskConsumers.GeofencingTaskConsumer  filterIsInstance :expo.modules.location.taskConsumers.GeofencingTaskConsumer  geofenceFromRegion :expo.modules.location.taskConsumers.GeofencingTaskConsumer  
getContext :expo.modules.location.taskConsumers.GeofencingTaskConsumer  getErrorString :expo.modules.location.taskConsumers.GeofencingTaskConsumer  getParamAsDouble :expo.modules.location.taskConsumers.GeofencingTaskConsumer  isAnyProviderAvailable :expo.modules.location.taskConsumers.GeofencingTaskConsumer  let :expo.modules.location.taskConsumers.GeofencingTaskConsumer  listOf :expo.modules.location.taskConsumers.GeofencingTaskConsumer  mGeofencingClient :expo.modules.location.taskConsumers.GeofencingTaskConsumer  mGeofencingList :expo.modules.location.taskConsumers.GeofencingTaskConsumer  mGeofencingRequest :expo.modules.location.taskConsumers.GeofencingTaskConsumer  mPendingIntent :expo.modules.location.taskConsumers.GeofencingTaskConsumer  mRegions :expo.modules.location.taskConsumers.GeofencingTaskConsumer  mTask :expo.modules.location.taskConsumers.GeofencingTaskConsumer  prepareGeofencingRequest :expo.modules.location.taskConsumers.GeofencingTaskConsumer  preparePendingIntent :expo.modules.location.taskConsumers.GeofencingTaskConsumer  regionStateForTransitionType :expo.modules.location.taskConsumers.GeofencingTaskConsumer  run :expo.modules.location.taskConsumers.GeofencingTaskConsumer  set :expo.modules.location.taskConsumers.GeofencingTaskConsumer  startGeofencing :expo.modules.location.taskConsumers.GeofencingTaskConsumer  stopGeofencing :expo.modules.location.taskConsumers.GeofencingTaskConsumer  taskManagerUtils :expo.modules.location.taskConsumers.GeofencingTaskConsumer  toDoubleOrNull :expo.modules.location.taskConsumers.GeofencingTaskConsumer  	ArrayList Dexpo.modules.location.taskConsumers.GeofencingTaskConsumer.Companion  Bundle Dexpo.modules.location.taskConsumers.GeofencingTaskConsumer.Companion  Error Dexpo.modules.location.taskConsumers.GeofencingTaskConsumer.Companion  Geofence Dexpo.modules.location.taskConsumers.GeofencingTaskConsumer.Companion  GeofenceStatusCodes Dexpo.modules.location.taskConsumers.GeofencingTaskConsumer.Companion  GeofencingEvent Dexpo.modules.location.taskConsumers.GeofencingTaskConsumer.Companion  GeofencingException Dexpo.modules.location.taskConsumers.GeofencingTaskConsumer.Companion  GeofencingRegionState Dexpo.modules.location.taskConsumers.GeofencingTaskConsumer.Companion  GeofencingRequest Dexpo.modules.location.taskConsumers.GeofencingTaskConsumer.Companion  HashMap Dexpo.modules.location.taskConsumers.GeofencingTaskConsumer.Companion  LocationHelpers Dexpo.modules.location.taskConsumers.GeofencingTaskConsumer.Companion  LocationModule Dexpo.modules.location.taskConsumers.GeofencingTaskConsumer.Companion  LocationServices Dexpo.modules.location.taskConsumers.GeofencingTaskConsumer.Companion  Log Dexpo.modules.location.taskConsumers.GeofencingTaskConsumer.Companion  PersistableBundle Dexpo.modules.location.taskConsumers.GeofencingTaskConsumer.Companion  TAG Dexpo.modules.location.taskConsumers.GeofencingTaskConsumer.Companion  UUID Dexpo.modules.location.taskConsumers.GeofencingTaskConsumer.Companion  apply Dexpo.modules.location.taskConsumers.GeofencingTaskConsumer.Companion  filterIsInstance Dexpo.modules.location.taskConsumers.GeofencingTaskConsumer.Companion  getErrorString Dexpo.modules.location.taskConsumers.GeofencingTaskConsumer.Companion  getParamAsDouble Dexpo.modules.location.taskConsumers.GeofencingTaskConsumer.Companion  isAnyProviderAvailable Dexpo.modules.location.taskConsumers.GeofencingTaskConsumer.Companion  let Dexpo.modules.location.taskConsumers.GeofencingTaskConsumer.Companion  listOf Dexpo.modules.location.taskConsumers.GeofencingTaskConsumer.Companion  run Dexpo.modules.location.taskConsumers.GeofencingTaskConsumer.Companion  set Dexpo.modules.location.taskConsumers.GeofencingTaskConsumer.Companion  toDoubleOrNull Dexpo.modules.location.taskConsumers.GeofencingTaskConsumer.Companion  Any 8expo.modules.location.taskConsumers.LocationTaskConsumer  AppForegroundedSingleton 8expo.modules.location.taskConsumers.LocationTaskConsumer  	Arguments 8expo.modules.location.taskConsumers.LocationTaskConsumer  	ArrayList 8expo.modules.location.taskConsumers.LocationTaskConsumer  Boolean 8expo.modules.location.taskConsumers.LocationTaskConsumer  Build 8expo.modules.location.taskConsumers.LocationTaskConsumer  Bundle 8expo.modules.location.taskConsumers.LocationTaskConsumer  	Companion 8expo.modules.location.taskConsumers.LocationTaskConsumer  
ComponentName 8expo.modules.location.taskConsumers.LocationTaskConsumer  Context 8expo.modules.location.taskConsumers.LocationTaskConsumer  FOREGROUND_SERVICE_KEY 8expo.modules.location.taskConsumers.LocationTaskConsumer  FusedLocationProviderClient 8expo.modules.location.taskConsumers.LocationTaskConsumer  IBinder 8expo.modules.location.taskConsumers.LocationTaskConsumer  Intent 8expo.modules.location.taskConsumers.LocationTaskConsumer  
JobParameters 8expo.modules.location.taskConsumers.LocationTaskConsumer  
JobService 8expo.modules.location.taskConsumers.LocationTaskConsumer  List 8expo.modules.location.taskConsumers.LocationTaskConsumer  Location 8expo.modules.location.taskConsumers.LocationTaskConsumer  LocationHelpers 8expo.modules.location.taskConsumers.LocationTaskConsumer  LocationOptions 8expo.modules.location.taskConsumers.LocationTaskConsumer  LocationRequest 8expo.modules.location.taskConsumers.LocationTaskConsumer  LocationResponse 8expo.modules.location.taskConsumers.LocationTaskConsumer  LocationResult 8expo.modules.location.taskConsumers.LocationTaskConsumer  LocationServices 8expo.modules.location.taskConsumers.LocationTaskConsumer  LocationTaskService 8expo.modules.location.taskConsumers.LocationTaskConsumer  Log 8expo.modules.location.taskConsumers.LocationTaskConsumer  Long 8expo.modules.location.taskConsumers.LocationTaskConsumer  Map 8expo.modules.location.taskConsumers.LocationTaskConsumer  MapArguments 8expo.modules.location.taskConsumers.LocationTaskConsumer  	MapHelper 8expo.modules.location.taskConsumers.LocationTaskConsumer  MutableList 8expo.modules.location.taskConsumers.LocationTaskConsumer  
PendingIntent 8expo.modules.location.taskConsumers.LocationTaskConsumer  PersistableBundle 8expo.modules.location.taskConsumers.LocationTaskConsumer  ReadableArguments 8expo.modules.location.taskConsumers.LocationTaskConsumer  SecurityException 8expo.modules.location.taskConsumers.LocationTaskConsumer  
ServiceBinder 8expo.modules.location.taskConsumers.LocationTaskConsumer  ServiceConnection 8expo.modules.location.taskConsumers.LocationTaskConsumer  String 8expo.modules.location.taskConsumers.LocationTaskConsumer  TAG 8expo.modules.location.taskConsumers.LocationTaskConsumer  TaskExecutionCallback 8expo.modules.location.taskConsumers.LocationTaskConsumer  
TaskInterface 8expo.modules.location.taskConsumers.LocationTaskConsumer  TaskManagerUtilsInterface 8expo.modules.location.taskConsumers.LocationTaskConsumer  abs 8expo.modules.location.taskConsumers.LocationTaskConsumer  context 8expo.modules.location.taskConsumers.LocationTaskConsumer  deferLocations 8expo.modules.location.taskConsumers.LocationTaskConsumer  executeTaskWithLocationBundles 8expo.modules.location.taskConsumers.LocationTaskConsumer  getValue 8expo.modules.location.taskConsumers.LocationTaskConsumer  isAnyProviderAvailable 8expo.modules.location.taskConsumers.LocationTaskConsumer  java 8expo.modules.location.taskConsumers.LocationTaskConsumer  lazy 8expo.modules.location.taskConsumers.LocationTaskConsumer  let 8expo.modules.location.taskConsumers.LocationTaskConsumer  listOf 8expo.modules.location.taskConsumers.LocationTaskConsumer  mDeferredDistance 8expo.modules.location.taskConsumers.LocationTaskConsumer  mDeferredLocations 8expo.modules.location.taskConsumers.LocationTaskConsumer  
mIsHostPaused 8expo.modules.location.taskConsumers.LocationTaskConsumer  mLastReportedLocation 8expo.modules.location.taskConsumers.LocationTaskConsumer  mLocationClient 8expo.modules.location.taskConsumers.LocationTaskConsumer  mLocationRequest 8expo.modules.location.taskConsumers.LocationTaskConsumer  mPendingIntent 8expo.modules.location.taskConsumers.LocationTaskConsumer  mService 8expo.modules.location.taskConsumers.LocationTaskConsumer  mTask 8expo.modules.location.taskConsumers.LocationTaskConsumer  maybeReportDeferredLocations 8expo.modules.location.taskConsumers.LocationTaskConsumer  maybeStartForegroundService 8expo.modules.location.taskConsumers.LocationTaskConsumer  
plusAssign 8expo.modules.location.taskConsumers.LocationTaskConsumer  prepareLocationRequest 8expo.modules.location.taskConsumers.LocationTaskConsumer  preparePendingIntent 8expo.modules.location.taskConsumers.LocationTaskConsumer  provideDelegate 8expo.modules.location.taskConsumers.LocationTaskConsumer  run 8expo.modules.location.taskConsumers.LocationTaskConsumer  sLastTimestamp 8expo.modules.location.taskConsumers.LocationTaskConsumer  shouldReportDeferredLocations 8expo.modules.location.taskConsumers.LocationTaskConsumer  shouldUseForegroundService 8expo.modules.location.taskConsumers.LocationTaskConsumer  startLocationUpdates 8expo.modules.location.taskConsumers.LocationTaskConsumer  stopForegroundService 8expo.modules.location.taskConsumers.LocationTaskConsumer  stopLocationUpdates 8expo.modules.location.taskConsumers.LocationTaskConsumer  taskManagerUtils 8expo.modules.location.taskConsumers.LocationTaskConsumer  AppForegroundedSingleton Bexpo.modules.location.taskConsumers.LocationTaskConsumer.Companion  	ArrayList Bexpo.modules.location.taskConsumers.LocationTaskConsumer.Companion  Build Bexpo.modules.location.taskConsumers.LocationTaskConsumer.Companion  Bundle Bexpo.modules.location.taskConsumers.LocationTaskConsumer.Companion  Context Bexpo.modules.location.taskConsumers.LocationTaskConsumer.Companion  FOREGROUND_SERVICE_KEY Bexpo.modules.location.taskConsumers.LocationTaskConsumer.Companion  Intent Bexpo.modules.location.taskConsumers.LocationTaskConsumer.Companion  LocationHelpers Bexpo.modules.location.taskConsumers.LocationTaskConsumer.Companion  LocationOptions Bexpo.modules.location.taskConsumers.LocationTaskConsumer.Companion  LocationResponse Bexpo.modules.location.taskConsumers.LocationTaskConsumer.Companion  LocationResult Bexpo.modules.location.taskConsumers.LocationTaskConsumer.Companion  LocationServices Bexpo.modules.location.taskConsumers.LocationTaskConsumer.Companion  LocationTaskService Bexpo.modules.location.taskConsumers.LocationTaskConsumer.Companion  Log Bexpo.modules.location.taskConsumers.LocationTaskConsumer.Companion  MapArguments Bexpo.modules.location.taskConsumers.LocationTaskConsumer.Companion  	MapHelper Bexpo.modules.location.taskConsumers.LocationTaskConsumer.Companion  PersistableBundle Bexpo.modules.location.taskConsumers.LocationTaskConsumer.Companion  TAG Bexpo.modules.location.taskConsumers.LocationTaskConsumer.Companion  abs Bexpo.modules.location.taskConsumers.LocationTaskConsumer.Companion  context Bexpo.modules.location.taskConsumers.LocationTaskConsumer.Companion  getValue Bexpo.modules.location.taskConsumers.LocationTaskConsumer.Companion  isAnyProviderAvailable Bexpo.modules.location.taskConsumers.LocationTaskConsumer.Companion  java Bexpo.modules.location.taskConsumers.LocationTaskConsumer.Companion  lazy Bexpo.modules.location.taskConsumers.LocationTaskConsumer.Companion  let Bexpo.modules.location.taskConsumers.LocationTaskConsumer.Companion  listOf Bexpo.modules.location.taskConsumers.LocationTaskConsumer.Companion  mService Bexpo.modules.location.taskConsumers.LocationTaskConsumer.Companion  
plusAssign Bexpo.modules.location.taskConsumers.LocationTaskConsumer.Companion  prepareLocationRequest Bexpo.modules.location.taskConsumers.LocationTaskConsumer.Companion  provideDelegate Bexpo.modules.location.taskConsumers.LocationTaskConsumer.Companion  run Bexpo.modules.location.taskConsumers.LocationTaskConsumer.Companion  sLastTimestamp Bexpo.modules.location.taskConsumers.LocationTaskConsumer.Companion  shouldUseForegroundService Bexpo.modules.location.taskConsumers.LocationTaskConsumer.Companion  Serializable java.io  Class 	java.lang  Error 	java.lang  	Exception 	java.lang  IllegalAccessException 	java.lang  InstantiationException 	java.lang  SecurityException 	java.lang  
StringBuilder 	java.lang  
simpleName java.lang.Class  java java.lang.Exception  message java.lang.Exception  	toDegrees java.lang.Math  message java.lang.SecurityException  append java.lang.StringBuilder  toString java.lang.StringBuilder  currentTimeMillis java.lang.System  
BigDecimal 	java.math  
BigInteger 	java.math  	ArrayList 	java.util  HashMap 	java.util  Locale 	java.util  UUID 	java.util  add java.util.ArrayList  clear java.util.ArrayList  filterIsInstance java.util.ArrayList  iterator java.util.ArrayList  size java.util.ArrayList  get java.util.HashMap  keys java.util.HashMap  remove java.util.HashMap  set java.util.HashMap  
getDefault java.util.Locale  
randomUUID java.util.UUID  toString java.util.UUID  Array kotlin  BooleanArray kotlin  	ByteArray kotlin  	CharArray kotlin  CharSequence kotlin  Double kotlin  DoubleArray kotlin  
FloatArray kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  	Function3 kotlin  IntArray kotlin  Lazy kotlin  	LongArray kotlin  Nothing kotlin  Pair kotlin  Result kotlin  
ShortArray kotlin  	Throwable kotlin  
UByteArray kotlin  	UIntArray kotlin  
ULongArray kotlin  UShortArray kotlin  apply kotlin  getValue kotlin  lazy kotlin  let kotlin  map kotlin  plus kotlin  run kotlin  takeIf kotlin  to kotlin  toString 
kotlin.Any  let kotlin.Boolean  not kotlin.Boolean  toString kotlin.CharSequence  	Companion 
kotlin.Double  	MAX_VALUE 
kotlin.Double  	compareTo 
kotlin.Double  let 
kotlin.Double  plus 
kotlin.Double  
plusAssign 
kotlin.Double  toFloat 
kotlin.Double  	MAX_VALUE kotlin.Double.Companion  	compareTo kotlin.Float  minus kotlin.Float  plus kotlin.Float  rem kotlin.Float  toDouble kotlin.Float  
unaryMinus kotlin.Float  get kotlin.FloatArray  	compareTo 
kotlin.Int  inc 
kotlin.Int  let 
kotlin.Int  minus 
kotlin.Int  or 
kotlin.Int  rangeTo 
kotlin.Int  toDouble 
kotlin.Int  toFloat 
kotlin.Int  getValue kotlin.Lazy  provideDelegate kotlin.Lazy  	compareTo kotlin.Long  let kotlin.Long  minus kotlin.Long  toDouble kotlin.Long  let 
kotlin.String  plus 
kotlin.String  to 
kotlin.String  toDoubleOrNull 
kotlin.String  message kotlin.Throwable  IntIterator kotlin.collections  Iterator kotlin.collections  List kotlin.collections  Map kotlin.collections  MutableIterator kotlin.collections  MutableList kotlin.collections  
MutableMap kotlin.collections  
MutableSet kotlin.collections  Set kotlin.collections  	emptyList kotlin.collections  filterIsInstance kotlin.collections  getValue kotlin.collections  listOf kotlin.collections  map kotlin.collections  
mapNotNull kotlin.collections  mapOf kotlin.collections  mutableMapOf kotlin.collections  plus kotlin.collections  
plusAssign kotlin.collections  set kotlin.collections  hasNext kotlin.collections.IntIterator  next kotlin.collections.IntIterator  hasNext kotlin.collections.Iterator  next kotlin.collections.Iterator  iterator kotlin.collections.List  map kotlin.collections.List  containsKey kotlin.collections.Map  get kotlin.collections.Map  hasNext "kotlin.collections.MutableIterator  next "kotlin.collections.MutableIterator  add kotlin.collections.MutableList  addAll kotlin.collections.MutableList  clear kotlin.collections.MutableList  get kotlin.collections.MutableList  iterator kotlin.collections.MutableList  size kotlin.collections.MutableList  apply kotlin.collections.MutableMap  foregroundService kotlin.collections.MutableMap  get kotlin.collections.MutableMap  let kotlin.collections.MutableMap  set kotlin.collections.MutableMap  iterator kotlin.collections.MutableSet  Continuation kotlin.coroutines  SuspendFunction0 kotlin.coroutines  SuspendFunction1 kotlin.coroutines  resume kotlin.coroutines  resumeWithException kotlin.coroutines  suspendCoroutine kotlin.coroutines  resume kotlin.coroutines.Continuation  resumeWithException kotlin.coroutines.Continuation  java 
kotlin.jvm  abs kotlin.math  IntRange 
kotlin.ranges  	LongRange 
kotlin.ranges  iterator kotlin.ranges.IntProgression  iterator kotlin.ranges.IntRange  
KProperty1 kotlin.reflect  java kotlin.reflect.KClass  
simpleName kotlin.reflect.KClass  Sequence kotlin.sequences  filterIsInstance kotlin.sequences  map kotlin.sequences  
mapNotNull kotlin.sequences  plus kotlin.sequences  map kotlin.text  
mapNotNull kotlin.text  plus kotlin.text  set kotlin.text  toDoubleOrNull kotlin.text                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              