
/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateShadowNodeH.js
 */

#pragma once

#include <react/renderer/components/rnsvg/EventEmitters.h>
#include <react/renderer/components/rnsvg/Props.h>
#include <react/renderer/components/rnsvg/States.h>
#include <react/renderer/components/view/ConcreteViewShadowNode.h>
#include <jsi/jsi.h>

namespace facebook::react {

JSI_EXPORT extern const char RNSVGSvgViewAndroidComponentName[];

/*
 * `ShadowNode` for <RNSVGSvgViewAndroid> component.
 */
using RNSVGSvgViewAndroidShadowNode = ConcreteViewShadowNode<
    RNSVGSvgViewAndroidComponentName,
    RNSVGSvgViewAndroidProps,
    RNSVGSvgViewAndroidEventEmitter,
    RNSVGSvgViewAndroidState>;

JSI_EXPORT extern const char RNSVGSvgViewComponentName[];

/*
 * `ShadowNode` for <RNSVGSvgView> component.
 */
using RNSVGSvgViewShadowNode = ConcreteViewShadowNode<
    RNSVGSvgViewComponentName,
    RNSVGSvgViewProps,
    RNSVGSvgViewEventEmitter,
    RNSVGSvgViewState>;

} // namespace facebook::react
