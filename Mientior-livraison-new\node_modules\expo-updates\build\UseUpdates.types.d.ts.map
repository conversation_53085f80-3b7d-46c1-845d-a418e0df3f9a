{"version": 3, "file": "UseUpdates.types.d.ts", "sourceRoot": "", "sources": ["../src/UseUpdates.types.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,QAAQ,EAAE,MAAM,iBAAiB,CAAC;AAEhD;;;GAGG;AACH,MAAM,MAAM,oBAAoB,GAAG;IACjC;;;;;;OAMG;IACH,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB;;OAEG;IACH,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB;;;;;;OAMG;IACH,SAAS,CAAC,EAAE,IAAI,CAAC;IACjB;;;OAGG;IACH,gBAAgB,EAAE,OAAO,CAAC;IAC1B;;;;;;;;OAQG;IACH,iBAAiB,EAAE,OAAO,CAAC;IAC3B;;;OAGG;IACH,qBAAqB,EAAE,MAAM,GAAG,IAAI,CAAC;IACrC;;OAEG;IACH,cAAc,CAAC,EAAE,MAAM,CAAC;IACxB;;;;;;;OAOG;IACH,QAAQ,CAAC,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC;IAC7B;;OAEG;IACH,cAAc,CAAC,EAAE,MAAM,CAAC;CACzB,CAAC;AAEF;;;;;GAKG;AACH,oBAAY,cAAc;IACxB;;OAEG;IACH,GAAG,QAAQ;IACX;;OAEG;IACH,QAAQ,aAAa;CACtB;AAED;;GAEG;AACH,MAAM,MAAM,aAAa,GAAG;IAC1B;;OAEG;IACH,IAAI,EAAE,cAAc,CAAC,GAAG,CAAC;IACzB;;;;;;OAMG;IACH,QAAQ,EAAE,MAAM,CAAC;IACjB;;;OAGG;IACH,SAAS,EAAE,IAAI,CAAC;IAChB;;;OAGG;IACH,QAAQ,EAAE,QAAQ,CAAC;CACpB,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,kBAAkB,GAAG;IAC/B;;OAEG;IACH,IAAI,EAAE,cAAc,CAAC,QAAQ,CAAC;IAC9B;;OAEG;IACH,QAAQ,EAAE,SAAS,CAAC;IACpB;;;OAGG;IACH,SAAS,EAAE,IAAI,CAAC;IAChB;;OAEG;IACH,QAAQ,EAAE,SAAS,CAAC;CACrB,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,UAAU,GAAG,aAAa,GAAG,kBAAkB,CAAC;AAE5D;;GAEG;AACH,MAAM,MAAM,oBAAoB,GAAG;IACjC;;OAEG;IACH,gBAAgB,EAAE,oBAAoB,CAAC;IACvC;;;OAGG;IACH,yBAAyB,EAAE,OAAO,CAAC;IACnC;;;OAGG;IACH,eAAe,CAAC,EAAE,UAAU,CAAC;IAC7B;;;OAGG;IACH,gBAAgB,CAAC,EAAE,UAAU,CAAC;IAC9B;;OAEG;IACH,iBAAiB,EAAE,OAAO,CAAC;IAC3B;;OAEG;IACH,eAAe,EAAE,OAAO,CAAC;IACzB;;OAEG;IACH,UAAU,EAAE,OAAO,CAAC;IACpB;;OAEG;IACH,aAAa,EAAE,OAAO,CAAC;IACvB;;OAEG;IACH,YAAY,EAAE,OAAO,CAAC;IACtB;;OAEG;IACH,YAAY,EAAE,MAAM,CAAC;IACrB;;;OAGG;IACH,UAAU,CAAC,EAAE,KAAK,CAAC;IACnB;;;OAGG;IACH,aAAa,CAAC,EAAE,KAAK,CAAC;IACtB;;;;OAIG;IACH,kCAAkC,CAAC,EAAE,IAAI,CAAC;CAC3C,CAAC"}