# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.22

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: Reanimated
# Configurations: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.8


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Debug
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = C$:/Users/<USER>/Documents/Mientior$ livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/.cxx/Debug/4212f3t5/armeabi-v7a/

#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D "C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-reanimated\android\.cxx\Debug\4212f3t5\armeabi-v7a" && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D "C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-reanimated\android\.cxx\Debug\4212f3t5\armeabi-v7a" && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -S"C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-reanimated\android" -B"C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-reanimated\android\.cxx\Debug\4212f3t5\armeabi-v7a""
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for SHARED_LIBRARY target worklets


#############################################
# Order-only phony target for worklets

build cmake_object_order_depends_target_worklets: phony || src/main/cpp/worklets/CMakeFiles/worklets.dir

build src/main/cpp/worklets/CMakeFiles/worklets.dir/C_/Users/<USER>/Documents/Mientior_livraison/Mientior-livraison-new/node_modules/react-native-reanimated/Common/cpp/worklets/NativeModules/WorkletsModuleProxy.cpp.o: CXX_COMPILER__worklets_Debug C$:/Users/<USER>/Documents/Mientior$ livraison/Mientior-livraison-new/node_modules/react-native-reanimated/Common/cpp/worklets/NativeModules/WorkletsModuleProxy.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src\main\cpp\worklets\CMakeFiles\worklets.dir\C_\Users\Elisee\Documents\Mientior_livraison\Mientior-livraison-new\node_modules\react-native-reanimated\Common\cpp\worklets\NativeModules\WorkletsModuleProxy.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/callinvoker" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/yoga" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir
  OBJECT_FILE_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir\C_\Users\Elisee\Documents\Mientior_livraison\Mientior-livraison-new\node_modules\react-native-reanimated\Common\cpp\worklets\NativeModules
  TARGET_COMPILE_PDB = src\main\cpp\worklets\CMakeFiles\worklets.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\4212f3t5\obj\armeabi-v7a\libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/C_/Users/<USER>/Documents/Mientior_livraison/Mientior-livraison-new/node_modules/react-native-reanimated/Common/cpp/worklets/NativeModules/WorkletsModuleProxySpec.cpp.o: CXX_COMPILER__worklets_Debug C$:/Users/<USER>/Documents/Mientior$ livraison/Mientior-livraison-new/node_modules/react-native-reanimated/Common/cpp/worklets/NativeModules/WorkletsModuleProxySpec.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src\main\cpp\worklets\CMakeFiles\worklets.dir\C_\Users\Elisee\Documents\Mientior_livraison\Mientior-livraison-new\node_modules\react-native-reanimated\Common\cpp\worklets\NativeModules\WorkletsModuleProxySpec.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/callinvoker" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/yoga" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir
  OBJECT_FILE_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir\C_\Users\Elisee\Documents\Mientior_livraison\Mientior-livraison-new\node_modules\react-native-reanimated\Common\cpp\worklets\NativeModules
  TARGET_COMPILE_PDB = src\main\cpp\worklets\CMakeFiles\worklets.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\4212f3t5\obj\armeabi-v7a\libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/C_/Users/<USER>/Documents/Mientior_livraison/Mientior-livraison-new/node_modules/react-native-reanimated/Common/cpp/worklets/Registries/EventHandlerRegistry.cpp.o: CXX_COMPILER__worklets_Debug C$:/Users/<USER>/Documents/Mientior$ livraison/Mientior-livraison-new/node_modules/react-native-reanimated/Common/cpp/worklets/Registries/EventHandlerRegistry.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src\main\cpp\worklets\CMakeFiles\worklets.dir\C_\Users\Elisee\Documents\Mientior_livraison\Mientior-livraison-new\node_modules\react-native-reanimated\Common\cpp\worklets\Registries\EventHandlerRegistry.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/callinvoker" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/yoga" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir
  OBJECT_FILE_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir\C_\Users\Elisee\Documents\Mientior_livraison\Mientior-livraison-new\node_modules\react-native-reanimated\Common\cpp\worklets\Registries
  TARGET_COMPILE_PDB = src\main\cpp\worklets\CMakeFiles\worklets.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\4212f3t5\obj\armeabi-v7a\libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/C_/Users/<USER>/Documents/Mientior_livraison/Mientior-livraison-new/node_modules/react-native-reanimated/Common/cpp/worklets/Registries/WorkletRuntimeRegistry.cpp.o: CXX_COMPILER__worklets_Debug C$:/Users/<USER>/Documents/Mientior$ livraison/Mientior-livraison-new/node_modules/react-native-reanimated/Common/cpp/worklets/Registries/WorkletRuntimeRegistry.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src\main\cpp\worklets\CMakeFiles\worklets.dir\C_\Users\Elisee\Documents\Mientior_livraison\Mientior-livraison-new\node_modules\react-native-reanimated\Common\cpp\worklets\Registries\WorkletRuntimeRegistry.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/callinvoker" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/yoga" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir
  OBJECT_FILE_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir\C_\Users\Elisee\Documents\Mientior_livraison\Mientior-livraison-new\node_modules\react-native-reanimated\Common\cpp\worklets\Registries
  TARGET_COMPILE_PDB = src\main\cpp\worklets\CMakeFiles\worklets.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\4212f3t5\obj\armeabi-v7a\libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/acc6d5c989e3e398e3d79b196b996379/Shareables.cpp.o: CXX_COMPILER__worklets_Debug C$:/Users/<USER>/Documents/Mientior$ livraison/Mientior-livraison-new/node_modules/react-native-reanimated/Common/cpp/worklets/SharedItems/Shareables.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src\main\cpp\worklets\CMakeFiles\worklets.dir\acc6d5c989e3e398e3d79b196b996379\Shareables.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/callinvoker" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/yoga" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir
  OBJECT_FILE_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir\acc6d5c989e3e398e3d79b196b996379
  TARGET_COMPILE_PDB = src\main\cpp\worklets\CMakeFiles\worklets.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\4212f3t5\obj\armeabi-v7a\libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/f55d245126d8cdd46c0c91a7a4da027d/Tools/AsyncQueue.cpp.o: CXX_COMPILER__worklets_Debug C$:/Users/<USER>/Documents/Mientior$ livraison/Mientior-livraison-new/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/AsyncQueue.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src\main\cpp\worklets\CMakeFiles\worklets.dir\f55d245126d8cdd46c0c91a7a4da027d\Tools\AsyncQueue.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/callinvoker" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/yoga" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir
  OBJECT_FILE_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir\f55d245126d8cdd46c0c91a7a4da027d\Tools
  TARGET_COMPILE_PDB = src\main\cpp\worklets\CMakeFiles\worklets.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\4212f3t5\obj\armeabi-v7a\libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/848e9dab9ab52d33b2228e7855dc069f/JSISerializer.cpp.o: CXX_COMPILER__worklets_Debug C$:/Users/<USER>/Documents/Mientior$ livraison/Mientior-livraison-new/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/JSISerializer.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src\main\cpp\worklets\CMakeFiles\worklets.dir\848e9dab9ab52d33b2228e7855dc069f\JSISerializer.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/callinvoker" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/yoga" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir
  OBJECT_FILE_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir\848e9dab9ab52d33b2228e7855dc069f
  TARGET_COMPILE_PDB = src\main\cpp\worklets\CMakeFiles\worklets.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\4212f3t5\obj\armeabi-v7a\libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/f55d245126d8cdd46c0c91a7a4da027d/Tools/JSLogger.cpp.o: CXX_COMPILER__worklets_Debug C$:/Users/<USER>/Documents/Mientior$ livraison/Mientior-livraison-new/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/JSLogger.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src\main\cpp\worklets\CMakeFiles\worklets.dir\f55d245126d8cdd46c0c91a7a4da027d\Tools\JSLogger.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/callinvoker" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/yoga" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir
  OBJECT_FILE_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir\f55d245126d8cdd46c0c91a7a4da027d\Tools
  TARGET_COMPILE_PDB = src\main\cpp\worklets\CMakeFiles\worklets.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\4212f3t5\obj\armeabi-v7a\libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/f55d245126d8cdd46c0c91a7a4da027d/Tools/JSScheduler.cpp.o: CXX_COMPILER__worklets_Debug C$:/Users/<USER>/Documents/Mientior$ livraison/Mientior-livraison-new/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/JSScheduler.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src\main\cpp\worklets\CMakeFiles\worklets.dir\f55d245126d8cdd46c0c91a7a4da027d\Tools\JSScheduler.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/callinvoker" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/yoga" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir
  OBJECT_FILE_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir\f55d245126d8cdd46c0c91a7a4da027d\Tools
  TARGET_COMPILE_PDB = src\main\cpp\worklets\CMakeFiles\worklets.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\4212f3t5\obj\armeabi-v7a\libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/848e9dab9ab52d33b2228e7855dc069f/ReanimatedJSIUtils.cpp.o: CXX_COMPILER__worklets_Debug C$:/Users/<USER>/Documents/Mientior$ livraison/Mientior-livraison-new/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/ReanimatedJSIUtils.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src\main\cpp\worklets\CMakeFiles\worklets.dir\848e9dab9ab52d33b2228e7855dc069f\ReanimatedJSIUtils.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/callinvoker" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/yoga" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir
  OBJECT_FILE_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir\848e9dab9ab52d33b2228e7855dc069f
  TARGET_COMPILE_PDB = src\main\cpp\worklets\CMakeFiles\worklets.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\4212f3t5\obj\armeabi-v7a\libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/848e9dab9ab52d33b2228e7855dc069f/ReanimatedVersion.cpp.o: CXX_COMPILER__worklets_Debug C$:/Users/<USER>/Documents/Mientior$ livraison/Mientior-livraison-new/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/ReanimatedVersion.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src\main\cpp\worklets\CMakeFiles\worklets.dir\848e9dab9ab52d33b2228e7855dc069f\ReanimatedVersion.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/callinvoker" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/yoga" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir
  OBJECT_FILE_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir\848e9dab9ab52d33b2228e7855dc069f
  TARGET_COMPILE_PDB = src\main\cpp\worklets\CMakeFiles\worklets.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\4212f3t5\obj\armeabi-v7a\libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/f55d245126d8cdd46c0c91a7a4da027d/Tools/UIScheduler.cpp.o: CXX_COMPILER__worklets_Debug C$:/Users/<USER>/Documents/Mientior$ livraison/Mientior-livraison-new/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/UIScheduler.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src\main\cpp\worklets\CMakeFiles\worklets.dir\f55d245126d8cdd46c0c91a7a4da027d\Tools\UIScheduler.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/callinvoker" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/yoga" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir
  OBJECT_FILE_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir\f55d245126d8cdd46c0c91a7a4da027d\Tools
  TARGET_COMPILE_PDB = src\main\cpp\worklets\CMakeFiles\worklets.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\4212f3t5\obj\armeabi-v7a\libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/C_/Users/<USER>/Documents/Mientior_livraison/Mientior-livraison-new/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/WorkletEventHandler.cpp.o: CXX_COMPILER__worklets_Debug C$:/Users/<USER>/Documents/Mientior$ livraison/Mientior-livraison-new/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/WorkletEventHandler.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src\main\cpp\worklets\CMakeFiles\worklets.dir\C_\Users\Elisee\Documents\Mientior_livraison\Mientior-livraison-new\node_modules\react-native-reanimated\Common\cpp\worklets\Tools\WorkletEventHandler.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/callinvoker" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/yoga" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir
  OBJECT_FILE_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir\C_\Users\Elisee\Documents\Mientior_livraison\Mientior-livraison-new\node_modules\react-native-reanimated\Common\cpp\worklets\Tools
  TARGET_COMPILE_PDB = src\main\cpp\worklets\CMakeFiles\worklets.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\4212f3t5\obj\armeabi-v7a\libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/C_/Users/<USER>/Documents/Mientior_livraison/Mientior-livraison-new/node_modules/react-native-reanimated/Common/cpp/worklets/WorkletRuntime/RNRuntimeWorkletDecorator.cpp.o: CXX_COMPILER__worklets_Debug C$:/Users/<USER>/Documents/Mientior$ livraison/Mientior-livraison-new/node_modules/react-native-reanimated/Common/cpp/worklets/WorkletRuntime/RNRuntimeWorkletDecorator.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src\main\cpp\worklets\CMakeFiles\worklets.dir\C_\Users\Elisee\Documents\Mientior_livraison\Mientior-livraison-new\node_modules\react-native-reanimated\Common\cpp\worklets\WorkletRuntime\RNRuntimeWorkletDecorator.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/callinvoker" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/yoga" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir
  OBJECT_FILE_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir\C_\Users\Elisee\Documents\Mientior_livraison\Mientior-livraison-new\node_modules\react-native-reanimated\Common\cpp\worklets\WorkletRuntime
  TARGET_COMPILE_PDB = src\main\cpp\worklets\CMakeFiles\worklets.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\4212f3t5\obj\armeabi-v7a\libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/C_/Users/<USER>/Documents/Mientior_livraison/Mientior-livraison-new/node_modules/react-native-reanimated/Common/cpp/worklets/WorkletRuntime/ReanimatedHermesRuntime.cpp.o: CXX_COMPILER__worklets_Debug C$:/Users/<USER>/Documents/Mientior$ livraison/Mientior-livraison-new/node_modules/react-native-reanimated/Common/cpp/worklets/WorkletRuntime/ReanimatedHermesRuntime.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src\main\cpp\worklets\CMakeFiles\worklets.dir\C_\Users\Elisee\Documents\Mientior_livraison\Mientior-livraison-new\node_modules\react-native-reanimated\Common\cpp\worklets\WorkletRuntime\ReanimatedHermesRuntime.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/callinvoker" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/yoga" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir
  OBJECT_FILE_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir\C_\Users\Elisee\Documents\Mientior_livraison\Mientior-livraison-new\node_modules\react-native-reanimated\Common\cpp\worklets\WorkletRuntime
  TARGET_COMPILE_PDB = src\main\cpp\worklets\CMakeFiles\worklets.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\4212f3t5\obj\armeabi-v7a\libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/11ac37f919a2eba3c8fcb77b57fd582f/ReanimatedRuntime.cpp.o: CXX_COMPILER__worklets_Debug C$:/Users/<USER>/Documents/Mientior$ livraison/Mientior-livraison-new/node_modules/react-native-reanimated/Common/cpp/worklets/WorkletRuntime/ReanimatedRuntime.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src\main\cpp\worklets\CMakeFiles\worklets.dir\11ac37f919a2eba3c8fcb77b57fd582f\ReanimatedRuntime.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/callinvoker" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/yoga" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir
  OBJECT_FILE_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir\11ac37f919a2eba3c8fcb77b57fd582f
  TARGET_COMPILE_PDB = src\main\cpp\worklets\CMakeFiles\worklets.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\4212f3t5\obj\armeabi-v7a\libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/11ac37f919a2eba3c8fcb77b57fd582f/WorkletRuntime.cpp.o: CXX_COMPILER__worklets_Debug C$:/Users/<USER>/Documents/Mientior$ livraison/Mientior-livraison-new/node_modules/react-native-reanimated/Common/cpp/worklets/WorkletRuntime/WorkletRuntime.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src\main\cpp\worklets\CMakeFiles\worklets.dir\11ac37f919a2eba3c8fcb77b57fd582f\WorkletRuntime.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/callinvoker" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/yoga" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir
  OBJECT_FILE_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir\11ac37f919a2eba3c8fcb77b57fd582f
  TARGET_COMPILE_PDB = src\main\cpp\worklets\CMakeFiles\worklets.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\4212f3t5\obj\armeabi-v7a\libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/C_/Users/<USER>/Documents/Mientior_livraison/Mientior-livraison-new/node_modules/react-native-reanimated/Common/cpp/worklets/WorkletRuntime/WorkletRuntimeDecorator.cpp.o: CXX_COMPILER__worklets_Debug C$:/Users/<USER>/Documents/Mientior$ livraison/Mientior-livraison-new/node_modules/react-native-reanimated/Common/cpp/worklets/WorkletRuntime/WorkletRuntimeDecorator.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src\main\cpp\worklets\CMakeFiles\worklets.dir\C_\Users\Elisee\Documents\Mientior_livraison\Mientior-livraison-new\node_modules\react-native-reanimated\Common\cpp\worklets\WorkletRuntime\WorkletRuntimeDecorator.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/callinvoker" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/yoga" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir
  OBJECT_FILE_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir\C_\Users\Elisee\Documents\Mientior_livraison\Mientior-livraison-new\node_modules\react-native-reanimated\Common\cpp\worklets\WorkletRuntime
  TARGET_COMPILE_PDB = src\main\cpp\worklets\CMakeFiles\worklets.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\4212f3t5\obj\armeabi-v7a\libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/android/AndroidUIScheduler.cpp.o: CXX_COMPILER__worklets_Debug C$:/Users/<USER>/Documents/Mientior$ livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp/worklets/android/AndroidUIScheduler.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src\main\cpp\worklets\CMakeFiles\worklets.dir\android\AndroidUIScheduler.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/callinvoker" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/yoga" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir
  OBJECT_FILE_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir\android
  TARGET_COMPILE_PDB = src\main\cpp\worklets\CMakeFiles\worklets.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\4212f3t5\obj\armeabi-v7a\libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/android/PlatformLogger.cpp.o: CXX_COMPILER__worklets_Debug C$:/Users/<USER>/Documents/Mientior$ livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp/worklets/android/PlatformLogger.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src\main\cpp\worklets\CMakeFiles\worklets.dir\android\PlatformLogger.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/callinvoker" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/yoga" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir
  OBJECT_FILE_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir\android
  TARGET_COMPILE_PDB = src\main\cpp\worklets\CMakeFiles\worklets.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\4212f3t5\obj\armeabi-v7a\libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/android/WorkletsModule.cpp.o: CXX_COMPILER__worklets_Debug C$:/Users/<USER>/Documents/Mientior$ livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp/worklets/android/WorkletsModule.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src\main\cpp\worklets\CMakeFiles\worklets.dir\android\WorkletsModule.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/callinvoker" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/yoga" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir
  OBJECT_FILE_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir\android
  TARGET_COMPILE_PDB = src\main\cpp\worklets\CMakeFiles\worklets.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\4212f3t5\obj\armeabi-v7a\libworklets.pdb

build src/main/cpp/worklets/CMakeFiles/worklets.dir/android/WorkletsOnLoad.cpp.o: CXX_COMPILER__worklets_Debug C$:/Users/<USER>/Documents/Mientior$ livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp/worklets/android/WorkletsOnLoad.cpp || cmake_object_order_depends_target_worklets
  DEFINES = -Dworklets_EXPORTS
  DEP_FILE = src\main\cpp\worklets\CMakeFiles\worklets.dir\android\WorkletsOnLoad.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/nativemodule/core/ReactCommon" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/callinvoker" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/yoga" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir
  OBJECT_FILE_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir\android
  TARGET_COMPILE_PDB = src\main\cpp\worklets\CMakeFiles\worklets.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\4212f3t5\obj\armeabi-v7a\libworklets.pdb


# =============================================================================
# Link build statements for SHARED_LIBRARY target worklets


#############################################
# Link the shared library ..\..\..\..\build\intermediates\cxx\Debug\4212f3t5\obj\armeabi-v7a\libworklets.so

build ../../../../build/intermediates/cxx/Debug/4212f3t5/obj/armeabi-v7a/libworklets.so: CXX_SHARED_LIBRARY_LINKER__worklets_Debug src/main/cpp/worklets/CMakeFiles/worklets.dir/C_/Users/<USER>/Documents/Mientior_livraison/Mientior-livraison-new/node_modules/react-native-reanimated/Common/cpp/worklets/NativeModules/WorkletsModuleProxy.cpp.o src/main/cpp/worklets/CMakeFiles/worklets.dir/C_/Users/<USER>/Documents/Mientior_livraison/Mientior-livraison-new/node_modules/react-native-reanimated/Common/cpp/worklets/NativeModules/WorkletsModuleProxySpec.cpp.o src/main/cpp/worklets/CMakeFiles/worklets.dir/C_/Users/<USER>/Documents/Mientior_livraison/Mientior-livraison-new/node_modules/react-native-reanimated/Common/cpp/worklets/Registries/EventHandlerRegistry.cpp.o src/main/cpp/worklets/CMakeFiles/worklets.dir/C_/Users/<USER>/Documents/Mientior_livraison/Mientior-livraison-new/node_modules/react-native-reanimated/Common/cpp/worklets/Registries/WorkletRuntimeRegistry.cpp.o src/main/cpp/worklets/CMakeFiles/worklets.dir/acc6d5c989e3e398e3d79b196b996379/Shareables.cpp.o src/main/cpp/worklets/CMakeFiles/worklets.dir/f55d245126d8cdd46c0c91a7a4da027d/Tools/AsyncQueue.cpp.o src/main/cpp/worklets/CMakeFiles/worklets.dir/848e9dab9ab52d33b2228e7855dc069f/JSISerializer.cpp.o src/main/cpp/worklets/CMakeFiles/worklets.dir/f55d245126d8cdd46c0c91a7a4da027d/Tools/JSLogger.cpp.o src/main/cpp/worklets/CMakeFiles/worklets.dir/f55d245126d8cdd46c0c91a7a4da027d/Tools/JSScheduler.cpp.o src/main/cpp/worklets/CMakeFiles/worklets.dir/848e9dab9ab52d33b2228e7855dc069f/ReanimatedJSIUtils.cpp.o src/main/cpp/worklets/CMakeFiles/worklets.dir/848e9dab9ab52d33b2228e7855dc069f/ReanimatedVersion.cpp.o src/main/cpp/worklets/CMakeFiles/worklets.dir/f55d245126d8cdd46c0c91a7a4da027d/Tools/UIScheduler.cpp.o src/main/cpp/worklets/CMakeFiles/worklets.dir/C_/Users/<USER>/Documents/Mientior_livraison/Mientior-livraison-new/node_modules/react-native-reanimated/Common/cpp/worklets/Tools/WorkletEventHandler.cpp.o src/main/cpp/worklets/CMakeFiles/worklets.dir/C_/Users/<USER>/Documents/Mientior_livraison/Mientior-livraison-new/node_modules/react-native-reanimated/Common/cpp/worklets/WorkletRuntime/RNRuntimeWorkletDecorator.cpp.o src/main/cpp/worklets/CMakeFiles/worklets.dir/C_/Users/<USER>/Documents/Mientior_livraison/Mientior-livraison-new/node_modules/react-native-reanimated/Common/cpp/worklets/WorkletRuntime/ReanimatedHermesRuntime.cpp.o src/main/cpp/worklets/CMakeFiles/worklets.dir/11ac37f919a2eba3c8fcb77b57fd582f/ReanimatedRuntime.cpp.o src/main/cpp/worklets/CMakeFiles/worklets.dir/11ac37f919a2eba3c8fcb77b57fd582f/WorkletRuntime.cpp.o src/main/cpp/worklets/CMakeFiles/worklets.dir/C_/Users/<USER>/Documents/Mientior_livraison/Mientior-livraison-new/node_modules/react-native-reanimated/Common/cpp/worklets/WorkletRuntime/WorkletRuntimeDecorator.cpp.o src/main/cpp/worklets/CMakeFiles/worklets.dir/android/AndroidUIScheduler.cpp.o src/main/cpp/worklets/CMakeFiles/worklets.dir/android/PlatformLogger.cpp.o src/main/cpp/worklets/CMakeFiles/worklets.dir/android/WorkletsModule.cpp.o src/main/cpp/worklets/CMakeFiles/worklets.dir/android/WorkletsOnLoad.cpp.o | C$:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/libs/android.armeabi-v7a/libjsi.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.armeabi-v7a/libfbjni.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/libs/android.armeabi-v7a/libreactnative.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/libs/android.armeabi-v7a/libhermes.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/libs/android.armeabi-v7a/libhermestooling.so
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info
  LINK_FLAGS = -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments
  LINK_LIBRARIES = -llog  C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/libs/android.armeabi-v7a/libjsi.so  C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.armeabi-v7a/libfbjni.so  C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/libs/android.armeabi-v7a/libreactnative.so  C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/libs/android.armeabi-v7a/libhermes.so  C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/libs/android.armeabi-v7a/libhermestooling.so  -latomic -lm
  OBJECT_DIR = src\main\cpp\worklets\CMakeFiles\worklets.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  SONAME = libworklets.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_COMPILE_PDB = src\main\cpp\worklets\CMakeFiles\worklets.dir\
  TARGET_FILE = ..\..\..\..\build\intermediates\cxx\Debug\4212f3t5\obj\armeabi-v7a\libworklets.so
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\4212f3t5\obj\armeabi-v7a\libworklets.pdb


#############################################
# Utility command for edit_cache

build src/main/cpp/worklets/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D "C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-reanimated\android\.cxx\Debug\4212f3t5\armeabi-v7a\src\main\cpp\worklets" && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build src/main/cpp/worklets/edit_cache: phony src/main/cpp/worklets/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build src/main/cpp/worklets/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D "C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-reanimated\android\.cxx\Debug\4212f3t5\armeabi-v7a\src\main\cpp\worklets" && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -S"C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-reanimated\android" -B"C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-reanimated\android\.cxx\Debug\4212f3t5\armeabi-v7a""
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build src/main/cpp/worklets/rebuild_cache: phony src/main/cpp/worklets/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for SHARED_LIBRARY target reanimated


#############################################
# Order-only phony target for reanimated

build cmake_object_order_depends_target_reanimated: phony || cmake_object_order_depends_target_worklets

build src/main/cpp/reanimated/CMakeFiles/reanimated.dir/C_/Users/<USER>/Documents/Mientior_livraison/Mientior-livraison-new/node_modules/react-native-reanimated/Common/cpp/reanimated/AnimatedSensor/AnimatedSensorModule.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/Documents/Mientior$ livraison/Mientior-livraison-new/node_modules/react-native-reanimated/Common/cpp/reanimated/AnimatedSensor/AnimatedSensorModule.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\C_\Users\Elisee\Documents\Mientior_livraison\Mientior-livraison-new\node_modules\react-native-reanimated\Common\cpp\reanimated\AnimatedSensor\AnimatedSensorModule.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/callinvoker" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/yoga" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src\main\cpp\reanimated\CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\C_\Users\Elisee\Documents\Mientior_livraison\Mientior-livraison-new\node_modules\react-native-reanimated\Common\cpp\reanimated\AnimatedSensor
  TARGET_COMPILE_PDB = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\4212f3t5\obj\armeabi-v7a\libreanimated.pdb

build src/main/cpp/reanimated/CMakeFiles/reanimated.dir/8ea38f2f8737d3b26ff34b1616826e80/PropsRegistry.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/Documents/Mientior$ livraison/Mientior-livraison-new/node_modules/react-native-reanimated/Common/cpp/reanimated/Fabric/PropsRegistry.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\8ea38f2f8737d3b26ff34b1616826e80\PropsRegistry.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/callinvoker" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/yoga" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src\main\cpp\reanimated\CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\8ea38f2f8737d3b26ff34b1616826e80
  TARGET_COMPILE_PDB = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\4212f3t5\obj\armeabi-v7a\libreanimated.pdb

build src/main/cpp/reanimated/CMakeFiles/reanimated.dir/C_/Users/<USER>/Documents/Mientior_livraison/Mientior-livraison-new/node_modules/react-native-reanimated/Common/cpp/reanimated/Fabric/ReanimatedCommitHook.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/Documents/Mientior$ livraison/Mientior-livraison-new/node_modules/react-native-reanimated/Common/cpp/reanimated/Fabric/ReanimatedCommitHook.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\C_\Users\Elisee\Documents\Mientior_livraison\Mientior-livraison-new\node_modules\react-native-reanimated\Common\cpp\reanimated\Fabric\ReanimatedCommitHook.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/callinvoker" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/yoga" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src\main\cpp\reanimated\CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\C_\Users\Elisee\Documents\Mientior_livraison\Mientior-livraison-new\node_modules\react-native-reanimated\Common\cpp\reanimated\Fabric
  TARGET_COMPILE_PDB = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\4212f3t5\obj\armeabi-v7a\libreanimated.pdb

build src/main/cpp/reanimated/CMakeFiles/reanimated.dir/C_/Users/<USER>/Documents/Mientior_livraison/Mientior-livraison-new/node_modules/react-native-reanimated/Common/cpp/reanimated/Fabric/ReanimatedMountHook.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/Documents/Mientior$ livraison/Mientior-livraison-new/node_modules/react-native-reanimated/Common/cpp/reanimated/Fabric/ReanimatedMountHook.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\C_\Users\Elisee\Documents\Mientior_livraison\Mientior-livraison-new\node_modules\react-native-reanimated\Common\cpp\reanimated\Fabric\ReanimatedMountHook.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/callinvoker" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/yoga" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src\main\cpp\reanimated\CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\C_\Users\Elisee\Documents\Mientior_livraison\Mientior-livraison-new\node_modules\react-native-reanimated\Common\cpp\reanimated\Fabric
  TARGET_COMPILE_PDB = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\4212f3t5\obj\armeabi-v7a\libreanimated.pdb

build src/main/cpp/reanimated/CMakeFiles/reanimated.dir/C_/Users/<USER>/Documents/Mientior_livraison/Mientior-livraison-new/node_modules/react-native-reanimated/Common/cpp/reanimated/Fabric/ShadowTreeCloner.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/Documents/Mientior$ livraison/Mientior-livraison-new/node_modules/react-native-reanimated/Common/cpp/reanimated/Fabric/ShadowTreeCloner.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\C_\Users\Elisee\Documents\Mientior_livraison\Mientior-livraison-new\node_modules\react-native-reanimated\Common\cpp\reanimated\Fabric\ShadowTreeCloner.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/callinvoker" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/yoga" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src\main\cpp\reanimated\CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\C_\Users\Elisee\Documents\Mientior_livraison\Mientior-livraison-new\node_modules\react-native-reanimated\Common\cpp\reanimated\Fabric
  TARGET_COMPILE_PDB = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\4212f3t5\obj\armeabi-v7a\libreanimated.pdb

build src/main/cpp/reanimated/CMakeFiles/reanimated.dir/C_/Users/<USER>/Documents/Mientior_livraison/Mientior-livraison-new/node_modules/react-native-reanimated/Common/cpp/reanimated/LayoutAnimations/LayoutAnimationsManager.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/Documents/Mientior$ livraison/Mientior-livraison-new/node_modules/react-native-reanimated/Common/cpp/reanimated/LayoutAnimations/LayoutAnimationsManager.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\C_\Users\Elisee\Documents\Mientior_livraison\Mientior-livraison-new\node_modules\react-native-reanimated\Common\cpp\reanimated\LayoutAnimations\LayoutAnimationsManager.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/callinvoker" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/yoga" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src\main\cpp\reanimated\CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\C_\Users\Elisee\Documents\Mientior_livraison\Mientior-livraison-new\node_modules\react-native-reanimated\Common\cpp\reanimated\LayoutAnimations
  TARGET_COMPILE_PDB = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\4212f3t5\obj\armeabi-v7a\libreanimated.pdb

build src/main/cpp/reanimated/CMakeFiles/reanimated.dir/C_/Users/<USER>/Documents/Mientior_livraison/Mientior-livraison-new/node_modules/react-native-reanimated/Common/cpp/reanimated/LayoutAnimations/LayoutAnimationsProxy.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/Documents/Mientior$ livraison/Mientior-livraison-new/node_modules/react-native-reanimated/Common/cpp/reanimated/LayoutAnimations/LayoutAnimationsProxy.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\C_\Users\Elisee\Documents\Mientior_livraison\Mientior-livraison-new\node_modules\react-native-reanimated\Common\cpp\reanimated\LayoutAnimations\LayoutAnimationsProxy.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/callinvoker" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/yoga" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src\main\cpp\reanimated\CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\C_\Users\Elisee\Documents\Mientior_livraison\Mientior-livraison-new\node_modules\react-native-reanimated\Common\cpp\reanimated\LayoutAnimations
  TARGET_COMPILE_PDB = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\4212f3t5\obj\armeabi-v7a\libreanimated.pdb

build src/main/cpp/reanimated/CMakeFiles/reanimated.dir/C_/Users/<USER>/Documents/Mientior_livraison/Mientior-livraison-new/node_modules/react-native-reanimated/Common/cpp/reanimated/LayoutAnimations/LayoutAnimationsUtils.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/Documents/Mientior$ livraison/Mientior-livraison-new/node_modules/react-native-reanimated/Common/cpp/reanimated/LayoutAnimations/LayoutAnimationsUtils.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\C_\Users\Elisee\Documents\Mientior_livraison\Mientior-livraison-new\node_modules\react-native-reanimated\Common\cpp\reanimated\LayoutAnimations\LayoutAnimationsUtils.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/callinvoker" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/yoga" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src\main\cpp\reanimated\CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\C_\Users\Elisee\Documents\Mientior_livraison\Mientior-livraison-new\node_modules\react-native-reanimated\Common\cpp\reanimated\LayoutAnimations
  TARGET_COMPILE_PDB = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\4212f3t5\obj\armeabi-v7a\libreanimated.pdb

build src/main/cpp/reanimated/CMakeFiles/reanimated.dir/C_/Users/<USER>/Documents/Mientior_livraison/Mientior-livraison-new/node_modules/react-native-reanimated/Common/cpp/reanimated/NativeModules/ReanimatedModuleProxy.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/Documents/Mientior$ livraison/Mientior-livraison-new/node_modules/react-native-reanimated/Common/cpp/reanimated/NativeModules/ReanimatedModuleProxy.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\C_\Users\Elisee\Documents\Mientior_livraison\Mientior-livraison-new\node_modules\react-native-reanimated\Common\cpp\reanimated\NativeModules\ReanimatedModuleProxy.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/callinvoker" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/yoga" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src\main\cpp\reanimated\CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\C_\Users\Elisee\Documents\Mientior_livraison\Mientior-livraison-new\node_modules\react-native-reanimated\Common\cpp\reanimated\NativeModules
  TARGET_COMPILE_PDB = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\4212f3t5\obj\armeabi-v7a\libreanimated.pdb

build src/main/cpp/reanimated/CMakeFiles/reanimated.dir/C_/Users/<USER>/Documents/Mientior_livraison/Mientior-livraison-new/node_modules/react-native-reanimated/Common/cpp/reanimated/NativeModules/ReanimatedModuleProxySpec.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/Documents/Mientior$ livraison/Mientior-livraison-new/node_modules/react-native-reanimated/Common/cpp/reanimated/NativeModules/ReanimatedModuleProxySpec.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\C_\Users\Elisee\Documents\Mientior_livraison\Mientior-livraison-new\node_modules\react-native-reanimated\Common\cpp\reanimated\NativeModules\ReanimatedModuleProxySpec.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/callinvoker" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/yoga" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src\main\cpp\reanimated\CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\C_\Users\Elisee\Documents\Mientior_livraison\Mientior-livraison-new\node_modules\react-native-reanimated\Common\cpp\reanimated\NativeModules
  TARGET_COMPILE_PDB = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\4212f3t5\obj\armeabi-v7a\libreanimated.pdb

build src/main/cpp/reanimated/CMakeFiles/reanimated.dir/C_/Users/<USER>/Documents/Mientior_livraison/Mientior-livraison-new/node_modules/react-native-reanimated/Common/cpp/reanimated/RuntimeDecorators/RNRuntimeDecorator.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/Documents/Mientior$ livraison/Mientior-livraison-new/node_modules/react-native-reanimated/Common/cpp/reanimated/RuntimeDecorators/RNRuntimeDecorator.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\C_\Users\Elisee\Documents\Mientior_livraison\Mientior-livraison-new\node_modules\react-native-reanimated\Common\cpp\reanimated\RuntimeDecorators\RNRuntimeDecorator.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/callinvoker" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/yoga" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src\main\cpp\reanimated\CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\C_\Users\Elisee\Documents\Mientior_livraison\Mientior-livraison-new\node_modules\react-native-reanimated\Common\cpp\reanimated\RuntimeDecorators
  TARGET_COMPILE_PDB = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\4212f3t5\obj\armeabi-v7a\libreanimated.pdb

build src/main/cpp/reanimated/CMakeFiles/reanimated.dir/C_/Users/<USER>/Documents/Mientior_livraison/Mientior-livraison-new/node_modules/react-native-reanimated/Common/cpp/reanimated/RuntimeDecorators/UIRuntimeDecorator.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/Documents/Mientior$ livraison/Mientior-livraison-new/node_modules/react-native-reanimated/Common/cpp/reanimated/RuntimeDecorators/UIRuntimeDecorator.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\C_\Users\Elisee\Documents\Mientior_livraison\Mientior-livraison-new\node_modules\react-native-reanimated\Common\cpp\reanimated\RuntimeDecorators\UIRuntimeDecorator.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/callinvoker" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/yoga" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src\main\cpp\reanimated\CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\C_\Users\Elisee\Documents\Mientior_livraison\Mientior-livraison-new\node_modules\react-native-reanimated\Common\cpp\reanimated\RuntimeDecorators
  TARGET_COMPILE_PDB = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\4212f3t5\obj\armeabi-v7a\libreanimated.pdb

build src/main/cpp/reanimated/CMakeFiles/reanimated.dir/b147319084345b3534fe8072ab18a9c2/FeaturesConfig.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/Documents/Mientior$ livraison/Mientior-livraison-new/node_modules/react-native-reanimated/Common/cpp/reanimated/Tools/FeaturesConfig.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\b147319084345b3534fe8072ab18a9c2\FeaturesConfig.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/callinvoker" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/yoga" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src\main\cpp\reanimated\CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\b147319084345b3534fe8072ab18a9c2
  TARGET_COMPILE_PDB = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\4212f3t5\obj\armeabi-v7a\libreanimated.pdb

build src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/JNIHelper.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/Documents/Mientior$ livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp/reanimated/android/JNIHelper.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\android\JNIHelper.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/callinvoker" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/yoga" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src\main\cpp\reanimated\CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\android
  TARGET_COMPILE_PDB = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\4212f3t5\obj\armeabi-v7a\libreanimated.pdb

build src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/LayoutAnimations.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/Documents/Mientior$ livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp/reanimated/android/LayoutAnimations.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\android\LayoutAnimations.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/callinvoker" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/yoga" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src\main\cpp\reanimated\CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\android
  TARGET_COMPILE_PDB = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\4212f3t5\obj\armeabi-v7a\libreanimated.pdb

build src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/NativeProxy.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/Documents/Mientior$ livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp/reanimated/android/NativeProxy.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\android\NativeProxy.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/callinvoker" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/yoga" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src\main\cpp\reanimated\CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\android
  TARGET_COMPILE_PDB = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\4212f3t5\obj\armeabi-v7a\libreanimated.pdb

build src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/OnLoad.cpp.o: CXX_COMPILER__reanimated_Debug C$:/Users/<USER>/Documents/Mientior$ livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp/reanimated/android/OnLoad.cpp || cmake_object_order_depends_target_reanimated
  DEFINES = -Dreanimated_EXPORTS
  DEP_FILE = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\android\OnLoad.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info  -fPIC -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -std=gnu++20
  INCLUDES = -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/../Common/cpp" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/src/main/cpp" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/src/main/jni/react/turbomodule" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/callinvoker" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/runtimeexecutor" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/yoga" -I"C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native/ReactCommon/react/renderer/graphics/platform/cxx" -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/include
  OBJECT_DIR = src\main\cpp\reanimated\CMakeFiles\reanimated.dir
  OBJECT_FILE_DIR = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\android
  TARGET_COMPILE_PDB = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\4212f3t5\obj\armeabi-v7a\libreanimated.pdb


# =============================================================================
# Link build statements for SHARED_LIBRARY target reanimated


#############################################
# Link the shared library ..\..\..\..\build\intermediates\cxx\Debug\4212f3t5\obj\armeabi-v7a\libreanimated.so

build ../../../../build/intermediates/cxx/Debug/4212f3t5/obj/armeabi-v7a/libreanimated.so: CXX_SHARED_LIBRARY_LINKER__reanimated_Debug src/main/cpp/reanimated/CMakeFiles/reanimated.dir/C_/Users/<USER>/Documents/Mientior_livraison/Mientior-livraison-new/node_modules/react-native-reanimated/Common/cpp/reanimated/AnimatedSensor/AnimatedSensorModule.cpp.o src/main/cpp/reanimated/CMakeFiles/reanimated.dir/8ea38f2f8737d3b26ff34b1616826e80/PropsRegistry.cpp.o src/main/cpp/reanimated/CMakeFiles/reanimated.dir/C_/Users/<USER>/Documents/Mientior_livraison/Mientior-livraison-new/node_modules/react-native-reanimated/Common/cpp/reanimated/Fabric/ReanimatedCommitHook.cpp.o src/main/cpp/reanimated/CMakeFiles/reanimated.dir/C_/Users/<USER>/Documents/Mientior_livraison/Mientior-livraison-new/node_modules/react-native-reanimated/Common/cpp/reanimated/Fabric/ReanimatedMountHook.cpp.o src/main/cpp/reanimated/CMakeFiles/reanimated.dir/C_/Users/<USER>/Documents/Mientior_livraison/Mientior-livraison-new/node_modules/react-native-reanimated/Common/cpp/reanimated/Fabric/ShadowTreeCloner.cpp.o src/main/cpp/reanimated/CMakeFiles/reanimated.dir/C_/Users/<USER>/Documents/Mientior_livraison/Mientior-livraison-new/node_modules/react-native-reanimated/Common/cpp/reanimated/LayoutAnimations/LayoutAnimationsManager.cpp.o src/main/cpp/reanimated/CMakeFiles/reanimated.dir/C_/Users/<USER>/Documents/Mientior_livraison/Mientior-livraison-new/node_modules/react-native-reanimated/Common/cpp/reanimated/LayoutAnimations/LayoutAnimationsProxy.cpp.o src/main/cpp/reanimated/CMakeFiles/reanimated.dir/C_/Users/<USER>/Documents/Mientior_livraison/Mientior-livraison-new/node_modules/react-native-reanimated/Common/cpp/reanimated/LayoutAnimations/LayoutAnimationsUtils.cpp.o src/main/cpp/reanimated/CMakeFiles/reanimated.dir/C_/Users/<USER>/Documents/Mientior_livraison/Mientior-livraison-new/node_modules/react-native-reanimated/Common/cpp/reanimated/NativeModules/ReanimatedModuleProxy.cpp.o src/main/cpp/reanimated/CMakeFiles/reanimated.dir/C_/Users/<USER>/Documents/Mientior_livraison/Mientior-livraison-new/node_modules/react-native-reanimated/Common/cpp/reanimated/NativeModules/ReanimatedModuleProxySpec.cpp.o src/main/cpp/reanimated/CMakeFiles/reanimated.dir/C_/Users/<USER>/Documents/Mientior_livraison/Mientior-livraison-new/node_modules/react-native-reanimated/Common/cpp/reanimated/RuntimeDecorators/RNRuntimeDecorator.cpp.o src/main/cpp/reanimated/CMakeFiles/reanimated.dir/C_/Users/<USER>/Documents/Mientior_livraison/Mientior-livraison-new/node_modules/react-native-reanimated/Common/cpp/reanimated/RuntimeDecorators/UIRuntimeDecorator.cpp.o src/main/cpp/reanimated/CMakeFiles/reanimated.dir/b147319084345b3534fe8072ab18a9c2/FeaturesConfig.cpp.o src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/JNIHelper.cpp.o src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/LayoutAnimations.cpp.o src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/NativeProxy.cpp.o src/main/cpp/reanimated/CMakeFiles/reanimated.dir/android/OnLoad.cpp.o | ../../../../build/intermediates/cxx/Debug/4212f3t5/obj/armeabi-v7a/libworklets.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/libs/android.armeabi-v7a/libreactnative.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/libs/android.armeabi-v7a/libjsi.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.armeabi-v7a/libfbjni.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/libs/android.armeabi-v7a/libreactnative.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/libs/android.armeabi-v7a/libhermes.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/libs/android.armeabi-v7a/libhermestooling.so || ../../../../build/intermediates/cxx/Debug/4212f3t5/obj/armeabi-v7a/libworklets.so
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -march=armv7-a -mthumb -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79   -DREANIMATED_VERSION=3.17.5    -DHERMES_ENABLE_DEBUGGER=1 -fexceptions -fno-omit-frame-pointer -frtti -fstack-protector-all   -std=c++20 -Wall -Werror -DRCT_NEW_ARCH_ENABLED -DJS_RUNTIME_HERMES=1 -fno-limit-debug-info
  LINK_FLAGS = -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments
  LINK_LIBRARIES = ../../../../build/intermediates/cxx/Debug/4212f3t5/obj/armeabi-v7a/libworklets.so  -landroid  C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/libs/android.armeabi-v7a/libreactnative.so  -llog  C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/jsi/libs/android.armeabi-v7a/libjsi.so  C:/Users/<USER>/.gradle/caches/8.13/transforms/8050d15875717ad3c035882deb89d68f/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.armeabi-v7a/libfbjni.so  C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/libs/android.armeabi-v7a/libreactnative.so  C:/Users/<USER>/.gradle/caches/8.13/transforms/19b228c99a9f96c9dea6bcb5eea8dce6/transformed/hermes-android-0.79.2-debug/prefab/modules/libhermes/libs/android.armeabi-v7a/libhermes.so  C:/Users/<USER>/.gradle/caches/8.13/transforms/4921e399dc760d526b20c10474ed13ea/transformed/react-android-0.79.2-debug/prefab/modules/hermestooling/libs/android.armeabi-v7a/libhermestooling.so  -latomic -lm
  OBJECT_DIR = src\main\cpp\reanimated\CMakeFiles\reanimated.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  SONAME = libreanimated.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_COMPILE_PDB = src\main\cpp\reanimated\CMakeFiles\reanimated.dir\
  TARGET_FILE = ..\..\..\..\build\intermediates\cxx\Debug\4212f3t5\obj\armeabi-v7a\libreanimated.so
  TARGET_PDB = ..\..\..\..\build\intermediates\cxx\Debug\4212f3t5\obj\armeabi-v7a\libreanimated.pdb


#############################################
# Utility command for edit_cache

build src/main/cpp/reanimated/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D "C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-reanimated\android\.cxx\Debug\4212f3t5\armeabi-v7a\src\main\cpp\reanimated" && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build src/main/cpp/reanimated/edit_cache: phony src/main/cpp/reanimated/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build src/main/cpp/reanimated/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D "C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-reanimated\android\.cxx\Debug\4212f3t5\armeabi-v7a\src\main\cpp\reanimated" && C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -S"C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-reanimated\android" -B"C:\Users\<USER>\Documents\Mientior livraison\Mientior-livraison-new\node_modules\react-native-reanimated\android\.cxx\Debug\4212f3t5\armeabi-v7a""
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build src/main/cpp/reanimated/rebuild_cache: phony src/main/cpp/reanimated/CMakeFiles/rebuild_cache.util

# =============================================================================
# Target aliases.

build libreanimated.so: phony ../../../../build/intermediates/cxx/Debug/4212f3t5/obj/armeabi-v7a/libreanimated.so

build libworklets.so: phony ../../../../build/intermediates/cxx/Debug/4212f3t5/obj/armeabi-v7a/libworklets.so

build reanimated: phony ../../../../build/intermediates/cxx/Debug/4212f3t5/obj/armeabi-v7a/libreanimated.so

build worklets: phony ../../../../build/intermediates/cxx/Debug/4212f3t5/obj/armeabi-v7a/libworklets.so

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/.cxx/Debug/4212f3t5/armeabi-v7a

build all: phony src/main/cpp/worklets/all src/main/cpp/reanimated/all

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/.cxx/Debug/4212f3t5/armeabi-v7a/src/main/cpp/reanimated

build src/main/cpp/reanimated/all: phony ../../../../build/intermediates/cxx/Debug/4212f3t5/obj/armeabi-v7a/libreanimated.so

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/Documents/Mientior livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/.cxx/Debug/4212f3t5/armeabi-v7a/src/main/cpp/worklets

build src/main/cpp/worklets/all: phony ../../../../build/intermediates/cxx/Debug/4212f3t5/obj/armeabi-v7a/libworklets.so

# =============================================================================
# Built-in targets


#############################################
# Phony target to force glob verification run.

build C$:/Users/<USER>/Documents/Mientior$ livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/.cxx/Debug/4212f3t5/armeabi-v7a/CMakeFiles/VerifyGlobs.cmake_force: phony


#############################################
# Re-run CMake to check if globbed directories changed.

build C$:/Users/<USER>/Documents/Mientior$ livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/.cxx/Debug/4212f3t5/armeabi-v7a/CMakeFiles/cmake.verify_globs: VERIFY_GLOBS | C$:/Users/<USER>/Documents/Mientior$ livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/.cxx/Debug/4212f3t5/armeabi-v7a/CMakeFiles/VerifyGlobs.cmake_force
  pool = console
  restat = 1


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE C$:/Users/<USER>/Documents/Mientior$ livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/.cxx/Debug/4212f3t5/armeabi-v7a/CMakeFiles/cmake.verify_globs | ../../../../CMakeLists.txt ../../../../src/main/cpp/reanimated/CMakeLists.txt ../../../../src/main/cpp/worklets/CMakeLists.txt ../prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/ReactAndroid/ReactAndroidConfig.cmake ../prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/ReactAndroid/ReactAndroidConfigVersion.cmake ../prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/fbjni/fbjniConfig.cmake ../prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/fbjni/fbjniConfigVersion.cmake ../prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/hermes-engine/hermes-engineConfig.cmake ../prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/hermes-engine/hermes-engineConfigVersion.cmake C$:/Users/<USER>/Documents/Mientior$ livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/.cxx/Debug/4212f3t5/armeabi-v7a/CMakeFiles/VerifyGlobs.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompiler.cmake.in C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompilerABI.c C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompiler.cmake.in C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCompilerIdDetection.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCXXCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompileFeatures.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerABI.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerId.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineSystem.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeFindBinUtils.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitIncludeInfo.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitLinkInfo.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseLibraryArchitecture.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystem.cmake.in C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCXXCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCompilerCommon.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ADSP-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMCC-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMClang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/AppleClang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Borland-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Bruce-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompilerInternal.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-FindBinUtils.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Cray-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Embarcadero-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Fujitsu-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GHS-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IAR-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Intel-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/MSVC-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVHPC-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVIDIA-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PGI-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PathScale-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SCO-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SDCC-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TI-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Watcom-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Internal/FeatureTesting.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android/Determine-Compiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/abis.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/android-legacy.toolchain.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/android.toolchain.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/flags.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Determine.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Initialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Determine-Compiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/platforms.cmake C$:/Users/<USER>/Documents/Mientior$ livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/cmake-utils/folly-flags.cmake CMakeCache.txt CMakeFiles/3.22.1-g37088a8-dirty/CMakeCCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeCXXCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeSystem.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build ../../../../CMakeLists.txt ../../../../src/main/cpp/reanimated/CMakeLists.txt ../../../../src/main/cpp/worklets/CMakeLists.txt ../prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/ReactAndroid/ReactAndroidConfig.cmake ../prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/ReactAndroid/ReactAndroidConfigVersion.cmake ../prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/fbjni/fbjniConfig.cmake ../prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/fbjni/fbjniConfigVersion.cmake ../prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/hermes-engine/hermes-engineConfig.cmake ../prefab/armeabi-v7a/prefab/lib/arm-linux-androideabi/cmake/hermes-engine/hermes-engineConfigVersion.cmake C$:/Users/<USER>/Documents/Mientior$ livraison/Mientior-livraison-new/node_modules/react-native-reanimated/android/.cxx/Debug/4212f3t5/armeabi-v7a/CMakeFiles/VerifyGlobs.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompiler.cmake.in C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompilerABI.c C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompiler.cmake.in C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCompilerIdDetection.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCXXCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompileFeatures.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerABI.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerId.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineSystem.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeFindBinUtils.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitIncludeInfo.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitLinkInfo.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseLibraryArchitecture.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystem.cmake.in C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCXXCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCompilerCommon.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ADSP-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMCC-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMClang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/AppleClang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Borland-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Bruce-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompilerInternal.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-FindBinUtils.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Cray-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Embarcadero-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Fujitsu-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GHS-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IAR-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Intel-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/MSVC-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVHPC-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVIDIA-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PGI-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PathScale-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SCO-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SDCC-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TI-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Watcom-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-C-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Internal/FeatureTesting.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-C.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-CXX.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android/Determine-Compiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/abis.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/android-legacy.toolchain.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/android.toolchain.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/flags.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Clang.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Determine.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Initialize.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/hooks/pre/Determine-Compiler.cmake C$:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/build/cmake/platforms.cmake C$:/Users/<USER>/Documents/Mientior$ livraison/Mientior-livraison-new/node_modules/react-native/ReactAndroid/cmake-utils/folly-flags.cmake CMakeCache.txt CMakeFiles/3.22.1-g37088a8-dirty/CMakeCCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeCXXCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeSystem.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
