{"logs": [{"outputFile": "com.eliseedev.mientiorlivraison.app-mergeDebugResources-71:/values-bn/values-bn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\1b1d6e3a1eafdeb637916b2c04d0a37e\\transformed\\exoplayer-core-2.18.1\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,123,189,256,322,397,464,596,725", "endColumns": "67,65,66,65,74,66,131,128,88", "endOffsets": "118,184,251,317,392,459,591,720,809"}, "to": {"startLines": "112,113,114,115,116,117,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "9713,9781,9847,9914,9980,10055,10122,10254,10383", "endColumns": "67,65,66,65,74,66,131,128,88", "endOffsets": "9776,9842,9909,9975,10050,10117,10249,10378,10467"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\8fb4e20b948a58ead7b50b7ad3e15312\\transformed\\appcompat-1.7.0\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,319,425,514,619,740,823,905,996,1089,1183,1277,1377,1470,1565,1659,1750,1841,1927,2037,2141,2244,2352,2460,2565,2730,2835", "endColumns": "107,105,105,88,104,120,82,81,90,92,93,93,99,92,94,93,90,90,85,109,103,102,107,107,104,164,104,86", "endOffsets": "208,314,420,509,614,735,818,900,991,1084,1178,1272,1372,1465,1560,1654,1745,1836,1922,2032,2136,2239,2347,2455,2560,2725,2830,2917"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,215", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "957,1065,1171,1277,1366,1471,1592,1675,1757,1848,1941,2035,2129,2229,2322,2417,2511,2602,2693,2779,2889,2993,3096,3204,3312,3417,3582,17636", "endColumns": "107,105,105,88,104,120,82,81,90,92,93,93,99,92,94,93,90,90,85,109,103,102,107,107,104,164,104,86", "endOffsets": "1060,1166,1272,1361,1466,1587,1670,1752,1843,1936,2030,2124,2224,2317,2412,2506,2597,2688,2774,2884,2988,3091,3199,3307,3412,3577,3682,17718"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\306a6bcd4bd045cfd61a3c5eb43578e4\\transformed\\core-1.13.1\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,358,461,562,664,784", "endColumns": "98,101,101,102,100,101,119,100", "endOffsets": "149,251,353,456,557,659,779,880"}, "to": {"startLines": "56,57,58,59,60,61,62,227", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4227,4326,4428,4530,4633,4734,4836,18609", "endColumns": "98,101,101,102,100,101,119,100", "endOffsets": "4321,4423,4525,4628,4729,4831,4951,18705"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\ff199f796fece94789ea9bd928f0e62a\\transformed\\browser-1.6.0\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,161,263,372", "endColumns": "105,101,108,105", "endOffsets": "156,258,367,473"}, "to": {"startLines": "85,140,141,142", "startColumns": "4,4,4,4", "startOffsets": "7502,11679,11781,11890", "endColumns": "105,101,108,105", "endOffsets": "7603,11776,11885,11991"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\59ddc64970c204fac172d4da5009756f\\transformed\\react-android-0.79.2-debug\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,205,277,345,425,493,560,634,711,794,874,944,1023,1103,1178,1266,1353,1428,1504,1579,1674,1750,1827,1897", "endColumns": "72,76,71,67,79,67,66,73,76,82,79,69,78,79,74,87,86,74,75,74,94,75,76,69,72", "endOffsets": "123,200,272,340,420,488,555,629,706,789,869,939,1018,1098,1173,1261,1348,1423,1499,1574,1669,1745,1822,1892,1965"}, "to": {"startLines": "50,66,143,145,146,148,162,163,164,211,212,213,214,219,220,221,222,223,224,225,226,228,229,230,231", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3687,5258,11996,12134,12202,12343,13353,13420,13494,17324,17407,17487,17557,17958,18038,18113,18201,18288,18363,18439,18514,18710,18786,18863,18933", "endColumns": "72,76,71,67,79,67,66,73,76,82,79,69,78,79,74,87,86,74,75,74,94,75,76,69,72", "endOffsets": "3755,5330,12063,12197,12277,12406,13415,13489,13566,17402,17482,17552,17631,18033,18108,18196,18283,18358,18434,18509,18604,18781,18858,18928,19001"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2ab0477328e0ae05829c4cc214e49234\\transformed\\play-services-base-18.2.0\\res\\values-bn\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,298,453,577,684,816,934,1042,1142,1281,1386,1538,1662,1791,1935,1991,2054", "endColumns": "104,154,123,106,131,117,107,99,138,104,151,123,128,143,55,62,85", "endOffsets": "297,452,576,683,815,933,1041,1141,1280,1385,1537,1661,1790,1934,1990,2053,2139"}, "to": {"startLines": "67,68,69,70,71,72,73,74,76,77,78,79,80,81,82,83,84", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5335,5444,5603,5731,5842,5978,6100,6212,6468,6611,6720,6876,7004,7137,7285,7345,7412", "endColumns": "108,158,127,110,135,121,111,103,142,108,155,127,132,147,59,66,89", "endOffsets": "5439,5598,5726,5837,5973,6095,6207,6311,6606,6715,6871,6999,7132,7280,7340,7407,7497"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\f8296c347bb3d1ec471e3402800c1db5\\transformed\\material-1.12.0\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,271,369,462,545,646,738,842,959,1040,1101,1167,1258,1324,1385,1475,1539,1606,1667,1736,1798,1852,1959,2018,2079,2133,2207,2327,2412,2502,2608,2698,2782,2917,2988,3058,3190,3277,3360,3418,3474,3540,3613,3693,3764,3846,3915,3991,4071,4140,4249,4344,4427,4517,4612,4686,4760,4853,4907,4992,5059,5145,5230,5292,5356,5419,5485,5587,5686,5779,5878,5940,6000,6080,6163,6242", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,97,92,82,100,91,103,116,80,60,65,90,65,60,89,63,66,60,68,61,53,106,58,60,53,73,119,84,89,105,89,83,134,70,69,131,86,82,57,55,65,72,79,70,81,68,75,79,68,108,94,82,89,94,73,73,92,53,84,66,85,84,61,63,62,65,101,98,92,98,61,59,79,82,78,72", "endOffsets": "266,364,457,540,641,733,837,954,1035,1096,1162,1253,1319,1380,1470,1534,1601,1662,1731,1793,1847,1954,2013,2074,2128,2202,2322,2407,2497,2603,2693,2777,2912,2983,3053,3185,3272,3355,3413,3469,3535,3608,3688,3759,3841,3910,3986,4066,4135,4244,4339,4422,4512,4607,4681,4755,4848,4902,4987,5054,5140,5225,5287,5351,5414,5480,5582,5681,5774,5873,5935,5995,6075,6158,6237,6310"}, "to": {"startLines": "19,51,52,53,54,55,63,64,65,86,87,139,144,147,149,150,151,152,153,154,155,156,157,158,159,160,161,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,216,217,218", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "786,3760,3858,3951,4034,4135,4956,5060,5177,7608,7669,11588,12068,12282,12411,12501,12565,12632,12693,12762,12824,12878,12985,13044,13105,13159,13233,13571,13656,13746,13852,13942,14026,14161,14232,14302,14434,14521,14604,14662,14718,14784,14857,14937,15008,15090,15159,15235,15315,15384,15493,15588,15671,15761,15856,15930,16004,16097,16151,16236,16303,16389,16474,16536,16600,16663,16729,16831,16930,17023,17122,17184,17244,17723,17806,17885", "endLines": "22,51,52,53,54,55,63,64,65,86,87,139,144,147,149,150,151,152,153,154,155,156,157,158,159,160,161,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,216,217,218", "endColumns": "12,97,92,82,100,91,103,116,80,60,65,90,65,60,89,63,66,60,68,61,53,106,58,60,53,73,119,84,89,105,89,83,134,70,69,131,86,82,57,55,65,72,79,70,81,68,75,79,68,108,94,82,89,94,73,73,92,53,84,66,85,84,61,63,62,65,101,98,92,98,61,59,79,82,78,72", "endOffsets": "952,3853,3946,4029,4130,4222,5055,5172,5253,7664,7730,11674,12129,12338,12496,12560,12627,12688,12757,12819,12873,12980,13039,13100,13154,13228,13348,13651,13741,13847,13937,14021,14156,14227,14297,14429,14516,14599,14657,14713,14779,14852,14932,15003,15085,15154,15230,15310,15379,15488,15583,15666,15756,15851,15925,15999,16092,16146,16231,16298,16384,16469,16531,16595,16658,16724,16826,16925,17018,17117,17179,17239,17319,17801,17880,17953"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\cea641498eb0b29f919daa30d9671bd0\\transformed\\play-services-basement-18.3.0\\res\\values-bn\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "147", "endOffsets": "342"}, "to": {"startLines": "75", "startColumns": "4", "startOffsets": "6316", "endColumns": "151", "endOffsets": "6463"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4407803e7a2d8d3f17c090093cb28e53\\transformed\\exoplayer-ui-2.18.1\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,288,497,691,776,860,941,1034,1130,1206,1272,1361,1450,1517,1581,1643,1716,1832,1948,2066,2137,2220,2289,2365,2453,2540,2604,2669,2722,2784,2832,2893,2953,3015,3079,3145,3202,3266,3331,3397,3463,3516,3579,3656,3733", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,84,83,80,92,95,75,65,88,88,66,63,61,72,115,115,117,70,82,68,75,87,86,63,64,52,61,47,60,59,61,63,65,56,63,64,65,65,52,62,76,76,51", "endOffsets": "283,492,686,771,855,936,1029,1125,1201,1267,1356,1445,1512,1576,1638,1711,1827,1943,2061,2132,2215,2284,2360,2448,2535,2599,2664,2717,2779,2827,2888,2948,3010,3074,3140,3197,3261,3326,3392,3458,3511,3574,3651,3728,3780"}, "to": {"startLines": "2,11,15,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,383,592,7735,7820,7904,7985,8078,8174,8250,8316,8405,8494,8561,8625,8687,8760,8876,8992,9110,9181,9264,9333,9409,9497,9584,9648,10472,10525,10587,10635,10696,10756,10818,10882,10948,11005,11069,11134,11200,11266,11319,11382,11459,11536", "endLines": "10,14,18,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138", "endColumns": "17,12,12,84,83,80,92,95,75,65,88,88,66,63,61,72,115,115,117,70,82,68,75,87,86,63,64,52,61,47,60,59,61,63,65,56,63,64,65,65,52,62,76,76,51", "endOffsets": "378,587,781,7815,7899,7980,8073,8169,8245,8311,8400,8489,8556,8620,8682,8755,8871,8987,9105,9176,9259,9328,9404,9492,9579,9643,9708,10520,10582,10630,10691,10751,10813,10877,10943,11000,11064,11129,11195,11261,11314,11377,11454,11531,11583"}}]}]}