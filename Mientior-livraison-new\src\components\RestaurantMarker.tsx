import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface RestaurantMarkerProps {
  businessType: 'restaurant' | 'colis' | 'marchandises' | 'pharmacie' | 'epicerie' | 'autres';
  isSelected?: boolean;
  rating?: number;
  size?: 'small' | 'medium' | 'large';
}

const RestaurantMarker: React.FC<RestaurantMarkerProps> = ({
  businessType,
  isSelected = false,
  rating,
  size = 'medium',
}) => {
  // Configuration des types de restaurants
  const typeConfig = {
    restaurant: { icon: 'restaurant', color: '#0DCAA8' },
    colis: { icon: 'cube', color: '#FF6B35' },
    marchandises: { icon: 'storefront', color: '#4ECDC4' },
    pharmacie: { icon: 'medical', color: '#27AE60' },
    epicerie: { icon: 'basket', color: '#F39C12' },
    autres: { icon: 'business', color: '#7F8C8D' },
  };

  // Configuration des tailles
  const sizeConfig = {
    small: { container: 32, icon: 16, rating: 10 },
    medium: { container: 40, icon: 20, rating: 12 },
    large: { container: 48, icon: 24, rating: 14 },
  };

  const config = typeConfig[businessType] || typeConfig.autres;
  const sizes = sizeConfig[size];

  return (
    <View style={styles.container}>
      {/* Marqueur principal */}
      <View
        style={[
          styles.marker,
          {
            width: sizes.container,
            height: sizes.container,
            borderRadius: sizes.container / 2,
            backgroundColor: config.color,
            borderWidth: isSelected ? 3 : 2,
            borderColor: isSelected ? '#FFFFFF' : 'rgba(255, 255, 255, 0.8)',
          },
        ]}
      >
        <Ionicons
          name={config.icon as any}
          size={sizes.icon}
          color="#FFFFFF"
        />
      </View>

      {/* Badge de note si fourni */}
      {rating && rating > 0 && (
        <View style={[styles.ratingBadge, { top: -8 }]}>
          <Ionicons name="star" size={sizes.rating} color="#FFD700" />
          <Text style={[styles.ratingText, { fontSize: sizes.rating }]}>
            {rating.toFixed(1)}
          </Text>
        </View>
      )}

      {/* Ombre portée */}
      <View
        style={[
          styles.shadow,
          {
            width: sizes.container * 0.8,
            height: sizes.container * 0.3,
            borderRadius: sizes.container * 0.4,
          },
        ]}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  marker: {
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 6,
    elevation: 8,
  },
  ratingBadge: {
    position: 'absolute',
    right: -8,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    paddingHorizontal: 6,
    paddingVertical: 2,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  ratingText: {
    fontWeight: '600',
    color: '#2C3E50',
    marginLeft: 2,
  },
  shadow: {
    position: 'absolute',
    bottom: -4,
    backgroundColor: 'rgba(0, 0, 0, 0.2)',
  },
});

export default RestaurantMarker;
