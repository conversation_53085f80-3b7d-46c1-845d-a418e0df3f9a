// Generated by view binder compiler. Do not edit!
package expo.modules.devlauncher.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import expo.modules.devlauncher.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ErrorConsoleListItemBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextView rnFrameFile;

  @NonNull
  public final TextView rnFrameMethod;

  private ErrorConsoleListItemBinding(@NonNull LinearLayout rootView, @NonNull TextView rnFrameFile,
      @NonNull TextView rnFrameMethod) {
    this.rootView = rootView;
    this.rnFrameFile = rnFrameFile;
    this.rnFrameMethod = rnFrameMethod;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ErrorConsoleListItemBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ErrorConsoleListItemBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.error_console_list_item, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ErrorConsoleListItemBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.rn_frame_file;
      TextView rnFrameFile = ViewBindings.findChildViewById(rootView, id);
      if (rnFrameFile == null) {
        break missingId;
      }

      id = R.id.rn_frame_method;
      TextView rnFrameMethod = ViewBindings.findChildViewById(rootView, id);
      if (rnFrameMethod == null) {
        break missingId;
      }

      return new ErrorConsoleListItemBinding((LinearLayout) rootView, rnFrameFile, rnFrameMethod);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
