/**
 * Service de gestion des fonctionnalités communautaires
 * Innovation révolutionnaire pour le marché africain
 */

import { supabase } from './supabase';
import {
  CommunityPoint,
  CommunityDeliverer,
  CommunityDelivery,
  TrustScore,
  CommunityStats,
  CommunityFilters,
  CommunitySearchResult,
  CommunityEvent
} from '../types/community';
import { Location } from '../types';

class CommunityService {
  private static instance: CommunityService;
  private eventListeners: Map<string, Function[]> = new Map();

  static getInstance(): CommunityService {
    if (!CommunityService.instance) {
      CommunityService.instance = new CommunityService();
    }
    return CommunityService.instance;
  }

  // ==================== POINTS DE COLLECTE ====================

  /**
   * Récupère tous les points de collecte actifs dans une zone
   */
  async getCommunityPoints(
    userLocation: Location,
    radiusKm: number = 10
  ): Promise<CommunityPoint[]> {
    try {
      console.log('🏘️ Fetching community points near:', userLocation);

      if (!supabase) {
        return this.getMockCommunityPoints();
      }

      const { data, error } = await supabase
        .from('community_points')
        .select('*')
        .eq('is_active', true)
        .order('total_deliveries', { ascending: false });

      if (error) throw error;

      // Calculer la distance et filtrer par rayon
      const pointsWithDistance = (data || []).map(point => ({
        ...point,
        distance: this.calculateDistance(
          userLocation,
          point.coordinates
        )
      })).filter(point => point.distance <= radiusKm);

      console.log(`🏘️ Found ${pointsWithDistance.length} community points`);
      return pointsWithDistance;

    } catch (error) {
      console.error('❌ Error fetching community points:', error);
      return this.getMockCommunityPoints();
    }
  }

  /**
   * Crée un nouveau point de collecte
   */
  async createCommunityPoint(pointData: Omit<CommunityPoint, 'id' | 'created_at' | 'updated_at'>): Promise<CommunityPoint> {
    try {
      if (!supabase) {
        throw new Error('Supabase not available');
      }

      const { data, error } = await supabase
        .from('community_points')
        .insert({
          ...pointData,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) throw error;

      console.log('✅ Community point created:', data.id);
      return data;

    } catch (error) {
      console.error('❌ Error creating community point:', error);
      throw error;
    }
  }

  // ==================== LIVREURS COMMUNAUTAIRES ====================

  /**
   * Récupère les livreurs communautaires disponibles
   */
  async getAvailableDeliverers(
    location: Location,
    filters?: CommunityFilters
  ): Promise<CommunityDeliverer[]> {
    try {
      console.log('🚚 Fetching available community deliverers');

      if (!supabase) {
        return this.getMockDeliverers();
      }

      let query = supabase
        .from('community_deliverers')
        .select('*')
        .eq('is_active', true)
        .eq('is_available', true)
        .eq('is_verified', true);

      // Appliquer les filtres
      if (filters?.min_trust_score) {
        query = query.gte('trust_score', filters.min_trust_score);
      }

      if (filters?.min_rating) {
        query = query.gte('community_rating', filters.min_rating);
      }

      const { data, error } = await query
        .order('trust_score', { ascending: false })
        .limit(20);

      if (error) throw error;

      // Calculer la distance et filtrer par zone de service
      const deliverersWithDistance = (data || []).map(deliverer => ({
        ...deliverer,
        distance: this.calculateDistance(location, deliverer.home_coordinates)
      })).filter(deliverer => 
        deliverer.distance <= deliverer.service_radius_km
      );

      console.log(`🚚 Found ${deliverersWithDistance.length} available deliverers`);
      return deliverersWithDistance;

    } catch (error) {
      console.error('❌ Error fetching deliverers:', error);
      return this.getMockDeliverers();
    }
  }

  /**
   * Inscription d'un nouveau livreur communautaire
   */
  async registerCommunityDeliverer(
    userId: string,
    delivererData: Omit<CommunityDeliverer, 'id' | 'user_id' | 'created_at' | 'updated_at'>
  ): Promise<CommunityDeliverer> {
    try {
      if (!supabase) {
        throw new Error('Supabase not available');
      }

      const newDeliverer = {
        user_id: userId,
        ...delivererData,
        trust_score: 50, // Score initial
        is_verified: false, // Nécessite vérification
        total_deliveries: 0,
        successful_deliveries: 0,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      const { data, error } = await supabase
        .from('community_deliverers')
        .insert(newDeliverer)
        .select()
        .single();

      if (error) throw error;

      console.log('✅ Community deliverer registered:', data.id);
      
      // Déclencher le processus de vérification
      await this.initiateVerificationProcess(data.id);

      return data;

    } catch (error) {
      console.error('❌ Error registering deliverer:', error);
      throw error;
    }
  }

  // ==================== LIVRAISONS COMMUNAUTAIRES ====================

  /**
   * Recherche la meilleure option de livraison communautaire
   */
  async findBestCommunityOption(
    pickupLocation: Location,
    deliveryLocation: Location,
    filters?: CommunityFilters
  ): Promise<CommunitySearchResult> {
    try {
      console.log('🔍 Finding best community delivery option');

      // Récupérer les points de collecte proches
      const nearbyPoints = await this.getCommunityPoints(pickupLocation, 5);
      
      // Récupérer les livreurs disponibles
      const availableDeliverers = await this.getAvailableDeliverers(deliveryLocation, filters);

      // Calculer les économies potentielles
      const standardFee = await this.calculateStandardDeliveryFee(pickupLocation, deliveryLocation);
      const communityFee = standardFee * 0.6; // 40% de réduction
      const estimatedSavings = standardFee - communityFee;

      // Trouver la meilleure combinaison point + livreur
      const bestOption = this.findOptimalCombination(
        nearbyPoints,
        availableDeliverers,
        pickupLocation,
        deliveryLocation
      );

      const result: CommunitySearchResult = {
        points: nearbyPoints,
        deliverers: availableDeliverers,
        estimated_savings: estimatedSavings,
        estimated_delivery_time: bestOption?.estimated_time || 60,
        recommended_option: bestOption || {
          point_id: nearbyPoints[0]?.id || '',
          total_cost: communityFee,
          savings: estimatedSavings,
          estimated_time: 60,
          trust_score: 75
        }
      };

      console.log('✅ Best community option found:', result.recommended_option);
      return result;

    } catch (error) {
      console.error('❌ Error finding community option:', error);
      throw error;
    }
  }

  /**
   * Crée une nouvelle livraison communautaire
   */
  async createCommunityDelivery(deliveryData: Omit<CommunityDelivery, 'id' | 'created_at' | 'updated_at'>): Promise<CommunityDelivery> {
    try {
      if (!supabase) {
        throw new Error('Supabase not available');
      }

      const newDelivery = {
        ...deliveryData,
        status: 'pending' as const,
        tracking_code: this.generateTrackingCode(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      const { data, error } = await supabase
        .from('community_deliveries')
        .insert(newDelivery)
        .select()
        .single();

      if (error) throw error;

      console.log('✅ Community delivery created:', data.id);

      // Notifier les livreurs disponibles
      await this.notifyAvailableDeliverers(data);

      // Émettre un événement
      this.emitEvent({
        type: 'new_delivery_request',
        data: data,
        timestamp: new Date().toISOString()
      });

      return data;

    } catch (error) {
      console.error('❌ Error creating community delivery:', error);
      throw error;
    }
  }

  // ==================== SYSTÈME DE CONFIANCE ====================

  /**
   * Calcule et met à jour le score de confiance d'un livreur
   */
  async updateTrustScore(delivererId: string, deliveryResult: {
    onTime: boolean;
    customerRating: number;
    successful: boolean;
    customerFeedback?: string;
  }): Promise<TrustScore> {
    try {
      console.log('⭐ Updating trust score for deliverer:', delivererId);

      // Récupérer le score actuel
      const currentScore = await this.getTrustScore(delivererId);
      
      // Calculer le nouveau score
      const newScore = this.calculateNewTrustScore(currentScore, deliveryResult);

      if (!supabase) {
        console.warn('Supabase client not available for trust score update');
        return newScore;
      }

      // Sauvegarder en base
      const { data, error } = await supabase
        .from('trust_scores')
        .upsert({
          ...newScore,
          user_id: delivererId,
          last_calculated: new Date().toISOString()
        })
        .select()
        .single();

      if (error) throw error;

      // Mettre à jour le profil du livreur
      await supabase
        .from('community_deliverers')
        .update({
          trust_score: newScore.overall_score,
          updated_at: new Date().toISOString()
        })
        .eq('id', delivererId);

      console.log('✅ Trust score updated:', newScore.overall_score);
      return data;

    } catch (error) {
      console.error('❌ Error updating trust score:', error);
      throw error;
    }
  }

  // ==================== STATISTIQUES COMMUNAUTAIRES ====================

  /**
   * Récupère les statistiques globales de la communauté
   */
  async getCommunityStats(): Promise<CommunityStats> {
    try {
      console.log('📊 Fetching community statistics');

      if (!supabase) {
        return this.getMockStats();
      }

      // Récupérer les données depuis plusieurs tables
      const [pointsData, deliverersData, deliveriesData] = await Promise.all([
        supabase.from('community_points').select('*'),
        supabase.from('community_deliverers').select('*'),
        supabase.from('community_deliveries').select('*')
      ]);

      const stats: CommunityStats = {
        total_points: pointsData.data?.length || 0,
        active_points: pointsData.data?.filter(p => p.is_active).length || 0,
        total_deliverers: deliverersData.data?.length || 0,
        active_deliverers: deliverersData.data?.filter(d => d.is_active).length || 0,
        total_deliveries: deliveriesData.data?.length || 0,
        successful_deliveries: deliveriesData.data?.filter(d => d.status === 'delivered').length || 0,
        
        // Calculer les moyennes
        average_delivery_time: this.calculateAverageDeliveryTime(deliveriesData.data || []),
        on_time_rate: this.calculateOnTimeRate(deliveriesData.data || []),
        customer_satisfaction: this.calculateCustomerSatisfaction(deliveriesData.data || []),
        cost_savings_total: this.calculateTotalSavings(deliveriesData.data || []),
        
        // Impact communautaire
        jobs_created: deliverersData.data?.filter(d => d.is_active).length || 0,
        neighborhoods_served: this.countUniqueNeighborhoods(pointsData.data || []),
        local_economy_impact: this.calculateEconomicImpact(deliveriesData.data || []),
        carbon_footprint_reduction: this.calculateCarbonReduction(deliveriesData.data || []),
        
        // Tendances (à implémenter avec données historiques)
        growth_rate: 15.5, // Placeholder
        retention_rate: 85.2, // Placeholder
        adoption_rate: 23.8 // Placeholder
      };

      console.log('📊 Community stats calculated:', stats);
      return stats;

    } catch (error) {
      console.error('❌ Error fetching community stats:', error);
      return this.getMockStats();
    }
  }

  // ==================== MÉTHODES UTILITAIRES ====================

  private calculateDistance(point1: Location, point2: Location): number {
    const R = 6371; // Rayon de la Terre en km
    const dLat = this.deg2rad(point2.latitude - point1.latitude);
    const dLon = this.deg2rad(point2.longitude - point1.longitude);
    const a = 
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(this.deg2rad(point1.latitude)) * Math.cos(this.deg2rad(point2.latitude)) * 
      Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  }

  private deg2rad(deg: number): number {
    return deg * (Math.PI/180);
  }

  private generateTrackingCode(): string {
    return `COM-${Date.now()}-${Math.random().toString(36).substr(2, 6).toUpperCase()}`;
  }

  private async calculateStandardDeliveryFee(pickup: Location, delivery: Location): Promise<number> {
    const distance = this.calculateDistance(pickup, delivery);
    const baseFee = 1000; // 1000 FCFA de base
    const perKmFee = 200; // 200 FCFA par km
    return baseFee + (distance * perKmFee);
  }

  private findOptimalCombination(
    points: CommunityPoint[],
    deliverers: CommunityDeliverer[],
    pickup: Location,
    delivery: Location
  ) {
    if (points.length === 0 || deliverers.length === 0) return null;

    // Algorithme simple : meilleur point + meilleur livreur
    const bestPoint = points[0]; // Déjà trié par performance
    const bestDeliverer = deliverers[0]; // Déjà trié par trust_score

    return {
      point_id: bestPoint.id,
      deliverer_id: bestDeliverer.id,
      total_cost: 2400, // 40% moins cher que 4000 FCFA standard
      savings: 1600,
      estimated_time: 45,
      trust_score: bestDeliverer.trust_score
    };
  }

  // ==================== DONNÉES MOCK POUR DÉVELOPPEMENT ====================

  private getMockCommunityPoints(): CommunityPoint[] {
    return [
      {
        id: 'point-1',
        name: 'Point Collecte Cocody',
        description: 'Point de collecte principal du quartier Cocody',
        address: 'Rue des Jardins, Cocody, Abidjan',
        coordinates: { latitude: 5.3364, longitude: -4.0267 },
        neighborhood: 'Cocody',
        city: 'Abidjan',
        region: 'Lagunes',
        is_active: true,
        capacity: 50,
        current_load: 12,
        operating_hours: {
          monday: { open: '08:00', close: '18:00', is_open: true },
          tuesday: { open: '08:00', close: '18:00', is_open: true },
          wednesday: { open: '08:00', close: '18:00', is_open: true },
          thursday: { open: '08:00', close: '18:00', is_open: true },
          friday: { open: '08:00', close: '18:00', is_open: true },
          saturday: { open: '09:00', close: '17:00', is_open: true },
          sunday: { open: '10:00', close: '16:00', is_open: false }
        },
        contact_person: 'Kouassi Jean',
        contact_phone: '+225 07 12 34 56 78',
        security_features: ['camera', 'guard', 'locked_storage'],
        accessibility_features: ['parking', 'public_transport'],
        total_deliveries: 245,
        success_rate: 96.7,
        average_rating: 4.6,
        total_ratings: 89,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        created_by: 'admin-1'
      }
    ];
  }

  private getMockDeliverers(): CommunityDeliverer[] {
    return [
      {
        id: 'deliverer-1',
        user_id: 'user-123',
        full_name: 'Koné Mamadou',
        phone: '+225 05 12 34 56 78',
        email: '<EMAIL>',
        photo_url: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
        home_address: 'Yopougon, Abidjan',
        home_coordinates: { latitude: 5.3364, longitude: -4.0267 },
        service_neighborhoods: ['Yopougon', 'Abobo', 'Adjamé'],
        service_radius_km: 15,
        is_active: true,
        is_available: true,
        is_verified: true,
        verification_date: new Date().toISOString(),
        verification_documents: ['id_card.jpg', 'address_proof.jpg'],
        transport_type: 'motorcycle',
        max_package_weight: 20,
        max_package_size: 'large',
        has_insulated_bag: true,
        has_smartphone: true,
        working_hours: {
          monday: { is_working: true, start_time: '08:00', end_time: '18:00' },
          tuesday: { is_working: true, start_time: '08:00', end_time: '18:00' },
          wednesday: { is_working: true, start_time: '08:00', end_time: '18:00' },
          thursday: { is_working: true, start_time: '08:00', end_time: '18:00' },
          friday: { is_working: true, start_time: '08:00', end_time: '18:00' },
          saturday: { is_working: true, start_time: '09:00', end_time: '17:00' },
          sunday: { is_working: false, start_time: '', end_time: '' }
        },
        trust_score: 87,
        community_rating: 4.8,
        total_deliveries: 156,
        successful_deliveries: 152,
        on_time_rate: 94.2,
        customer_ratings: 4.7,
        community_endorsements: 23,
        warnings_count: 0,
        suspensions_count: 0,
        last_active: new Date().toISOString(),
        earnings_total: 245000,
        earnings_current_month: 45000,
        commission_rate: 15,
        payment_method: 'mobile_money',
        payment_details: { operator: 'orange_money', number: '+225 07 12 34 56 78' },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        onboarded_by: 'admin-1'
      }
    ];
  }

  private getMockStats(): CommunityStats {
    return {
      total_points: 25,
      active_points: 23,
      total_deliverers: 156,
      active_deliverers: 142,
      total_deliveries: 2847,
      successful_deliveries: 2756,
      average_delivery_time: 42,
      on_time_rate: 89.3,
      customer_satisfaction: 4.6,
      cost_savings_total: 1247000,
      jobs_created: 142,
      neighborhoods_served: 18,
      local_economy_impact: 3456000,
      carbon_footprint_reduction: 1250,
      growth_rate: 15.5,
      retention_rate: 85.2,
      adoption_rate: 23.8
    };
  }

  // Méthodes de calcul des statistiques (à implémenter)
  private calculateAverageDeliveryTime(deliveries: any[]): number { return 42; }
  private calculateOnTimeRate(deliveries: any[]): number { return 89.3; }
  private calculateCustomerSatisfaction(deliveries: any[]): number { return 4.6; }
  private calculateTotalSavings(deliveries: any[]): number { return 1247000; }
  private countUniqueNeighborhoods(points: any[]): number { return 18; }
  private calculateEconomicImpact(deliveries: any[]): number { return 3456000; }
  private calculateCarbonReduction(deliveries: any[]): number { return 1250; }

  // Méthodes pour le système de confiance (à implémenter)
  private async getTrustScore(delivererId: string): Promise<TrustScore> {
    // Implémentation à venir
    return {} as TrustScore;
  }

  private calculateNewTrustScore(currentScore: TrustScore, result: any): TrustScore {
    // Implémentation à venir
    return currentScore;
  }

  // Méthodes pour les notifications et événements
  private async notifyAvailableDeliverers(delivery: CommunityDelivery): Promise<void> {
    // Implémentation à venir
  }

  private async initiateVerificationProcess(delivererId: string): Promise<void> {
    // Implémentation à venir
  }

  private emitEvent(event: CommunityEvent): void {
    // Implémentation à venir
  }
}

export const communityService = CommunityService.getInstance();
