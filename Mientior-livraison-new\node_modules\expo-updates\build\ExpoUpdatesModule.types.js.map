{"version": 3, "file": "ExpoUpdatesModule.types.js", "sourceRoot": "", "sources": ["../src/ExpoUpdatesModule.types.ts"], "names": [], "mappings": "", "sourcesContent": ["import { NativeModule } from 'expo-modules-core';\n\nimport {\n  Manifest,\n  UpdateCheckResultAvailable,\n  UpdateCheckResultNotAvailable,\n  UpdateCheckResultRollBack,\n  UpdateFetchResultRollBackToEmbedded,\n  UpdateFetchResultFailure,\n  UpdateFetchResultSuccess,\n  UpdatesLogEntry,\n  UpdatesNativeStateMachineContext,\n} from './Updates.types';\n\nexport type UpdatesEvents = {\n  'Expo.nativeUpdatesStateChangeEvent': (params: any) => void;\n};\n\nexport type UpdatesCheckAutomaticallyNativeValue =\n  | 'ALWAYS'\n  | 'ERROR_RECOVERY_ONLY'\n  | 'NEVER'\n  | 'WIFI_ONLY';\n\n/**\n * Common interface for all native module implementations (android, ios, web).\n *\n * @internal\n */\nexport interface UpdatesModuleInterface {\n  isEmergencyLaunch: boolean;\n  emergencyLaunchReason: string | null;\n  launchDuration: number | null;\n  isEmbeddedLaunch: boolean;\n  isEnabled: boolean;\n  isUsingEmbeddedAssets?: boolean;\n  /**\n   * Can be empty string\n   */\n  runtimeVersion: string;\n  checkAutomatically: UpdatesCheckAutomaticallyNativeValue;\n  /**\n   * Can be empty string\n   */\n  channel: string;\n  shouldDeferToNativeForAPIMethodAvailabilityInDevelopment: boolean;\n  updateId?: string;\n  commitTime?: string;\n  /**\n   * @platform android\n   */\n  manifestString?: string;\n  /**\n   * @platform ios\n   */\n  manifest?: Manifest;\n  localAssets?: Record<string, string>;\n\n  initialContext: UpdatesNativeStateMachineContext & {\n    latestManifestString?: string;\n    downloadedManifestString?: string;\n    lastCheckForUpdateTimeString?: string;\n    rollbackString?: string;\n  };\n\n  reload: () => Promise<void>;\n  checkForUpdateAsync: () => Promise<\n    | UpdateCheckResultRollBack\n    | (Omit<UpdateCheckResultAvailable, 'manifest'> &\n        ({ manifestString: string } | { manifest: Manifest }))\n    | UpdateCheckResultNotAvailable\n  >;\n  getExtraParamsAsync: () => Promise<Record<string, string>>;\n  setExtraParamAsync: (key: string, value: string | null) => Promise<void>;\n  readLogEntriesAsync: (maxAge: number) => Promise<UpdatesLogEntry[]>;\n  clearLogEntriesAsync: () => Promise<void>;\n  fetchUpdateAsync: () => Promise<\n    | (Omit<UpdateFetchResultSuccess, 'manifest'> &\n        ({ manifestString: string } | { manifest: Manifest }))\n    | UpdateFetchResultFailure\n    | UpdateFetchResultRollBackToEmbedded\n  >;\n}\n\n/**\n * @internal\n */\nexport declare class ExpoUpdatesModule\n  extends NativeModule<UpdatesEvents>\n  implements UpdatesModuleInterface\n{\n  isEmergencyLaunch: boolean;\n  emergencyLaunchReason: string | null;\n  launchDuration: number | null;\n  isEmbeddedLaunch: boolean;\n  isEnabled: boolean;\n  isUsingEmbeddedAssets?: boolean;\n  runtimeVersion: string;\n  checkAutomatically: UpdatesCheckAutomaticallyNativeValue;\n  channel: string;\n  shouldDeferToNativeForAPIMethodAvailabilityInDevelopment: boolean;\n  updateId?: string;\n  commitTime?: string;\n  manifestString?: string;\n  manifest?: Manifest;\n  localAssets?: Record<string, string>;\n\n  initialContext: UpdatesNativeStateMachineContext & {\n    latestManifestString?: string;\n    downloadedManifestString?: string;\n    lastCheckForUpdateTimeString?: string;\n    rollbackString?: string;\n  };\n\n  reload: () => Promise<void>;\n  checkForUpdateAsync: () => Promise<\n    | UpdateCheckResultRollBack\n    | (Omit<UpdateCheckResultAvailable, 'manifest'> &\n        ({ manifestString: string } | { manifest: Manifest }))\n    | UpdateCheckResultNotAvailable\n  >;\n  getExtraParamsAsync: () => Promise<Record<string, string>>;\n  setExtraParamAsync: (key: string, value: string | null) => Promise<void>;\n  readLogEntriesAsync: (maxAge: number) => Promise<UpdatesLogEntry[]>;\n  clearLogEntriesAsync: () => Promise<void>;\n  fetchUpdateAsync: () => Promise<\n    | (Omit<UpdateFetchResultSuccess, 'manifest'> &\n        ({ manifestString: string } | { manifest: Manifest }))\n    | UpdateFetchResultFailure\n    | UpdateFetchResultRollBackToEmbedded\n  >;\n}\n"]}