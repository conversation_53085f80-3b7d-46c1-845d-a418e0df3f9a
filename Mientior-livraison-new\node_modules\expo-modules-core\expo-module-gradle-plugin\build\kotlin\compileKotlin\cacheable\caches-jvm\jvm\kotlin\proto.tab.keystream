*expo/modules/plugin/AutolinkingIntegration+expo/modules/plugin/ExpoModulesGradlePlugin.expo/modules/plugin/ExtraPropertiesExtensionKt-expo/modules/plugin/ExpoModulesGradlePluginKt*expo/modules/plugin/ProjectConfigurationKtexpo/modules/plugin/Version%expo/modules/plugin/Version$Companionexpo/modules/plugin/WarningsKt5expo/modules/plugin/android/AndroidLibraryExtensionKt+expo/modules/plugin/android/PublicationInfo7expo/modules/plugin/android/MavenPublicationExtensionKt4expo/modules/plugin/gradle/ExpoGradleHelperExtension.expo/modules/plugin/gradle/ExpoModuleExtension.expo/modules/plugin/AutolinkingIntegrationImpl.kotlin_module                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    