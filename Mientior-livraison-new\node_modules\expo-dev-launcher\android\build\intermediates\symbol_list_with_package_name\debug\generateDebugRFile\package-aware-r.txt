expo.modules.devlauncher
color dev_launcher_backgroundColor
color dev_launcher_colorAccentDark
color dev_launcher_colorPrimaryDark
color dev_launcher_errorLogButton
color dev_launcher_errorMessage
color dev_launcher_primary
color dev_launcher_secondaryBackgroundColor
color dev_launcher_white
drawable __node_modules_reactnavigation_elements_lib_module_assets_backicon
drawable __node_modules_reactnavigation_elements_lib_module_assets_backiconmask
drawable __node_modules_reactnavigation_elements_lib_module_assets_clearicon
drawable __node_modules_reactnavigation_elements_lib_module_assets_closeicon
drawable __node_modules_reactnavigation_elements_lib_module_assets_searchicon
drawable _expodevclientcomponents_assets_branchicon
drawable _expodevclientcomponents_assets_branchiconlight
drawable _expodevclientcomponents_assets_buildingicon
drawable _expodevclientcomponents_assets_checkicon
drawable _expodevclientcomponents_assets_checkiconlight
drawable _expodevclientcomponents_assets_chevronrighticon
drawable _expodevclientcomponents_assets_clipboardicon
drawable _expodevclientcomponents_assets_debugicon
drawable _expodevclientcomponents_assets_extensionsfilledactiveicon
drawable _expodevclientcomponents_assets_extensionsfilledactiveiconlight
drawable _expodevclientcomponents_assets_extensionsfilledinactiveicon
drawable _expodevclientcomponents_assets_extensionsfilledinactiveiconlight
drawable _expodevclientcomponents_assets_extensionsicon
drawable _expodevclientcomponents_assets_extensionsiconlight
drawable _expodevclientcomponents_assets_homefilledactiveicon
drawable _expodevclientcomponents_assets_homefilledactiveiconlight
drawable _expodevclientcomponents_assets_homefilledinactiveicon
drawable _expodevclientcomponents_assets_homefilledinactiveiconlight
drawable _expodevclientcomponents_assets_infoicon
drawable _expodevclientcomponents_assets_infoiconlight
drawable _expodevclientcomponents_assets_inspectelementicon
drawable _expodevclientcomponents_assets_loadingindicatoricon
drawable _expodevclientcomponents_assets_logoicon
drawable _expodevclientcomponents_assets_performanceicon
drawable _expodevclientcomponents_assets_questionmarkicon
drawable _expodevclientcomponents_assets_refreshicon
drawable _expodevclientcomponents_assets_runicon
drawable _expodevclientcomponents_assets_settingsfilledactiveicon
drawable _expodevclientcomponents_assets_settingsfilledactiveiconlight
drawable _expodevclientcomponents_assets_settingsfilledinactiveicon
drawable _expodevclientcomponents_assets_settingsfilledinactiveiconlight
drawable _expodevclientcomponents_assets_shakedeviceicon
drawable _expodevclientcomponents_assets_shakedeviceiconlight
drawable _expodevclientcomponents_assets_showmenuatlaunchicon
drawable _expodevclientcomponents_assets_showmenuatlaunchiconlight
drawable _expodevclientcomponents_assets_terminalicon
drawable _expodevclientcomponents_assets_threefingerlongpressicon
drawable _expodevclientcomponents_assets_threefingerlongpressiconlight
drawable _expodevclientcomponents_assets_toolbaroverlayicon
drawable _expodevclientcomponents_assets_updateicon
drawable _expodevclientcomponents_assets_updateiconlight
drawable _expodevclientcomponents_assets_usericon
drawable _expodevclientcomponents_assets_usericonlight
drawable _expodevclientcomponents_assets_warningtriangleicon
drawable _expodevclientcomponents_assets_warningtriangleiconlight
drawable _expodevclientcomponents_assets_xicon
drawable _expodevclientcomponents_assets_xiconlight
drawable dev_laucher_ic_home_white_36dp
drawable dev_launcher_ic_refresh_white_36dp
id console_home_button
id console_reload_button
id errorDetails
id error_footer
id error_footer_content
id error_main_content
id error_stack
id error_title
id error_viewPager
id homeButton
id list_view
id reloadButton
id rn_frame_file
id rn_frame_method
layout error_activity_content_view
layout error_console_fragment
layout error_console_list_item
layout error_fragment
raw keep
string dev_launcher_error_details
string dev_launcher_error_header
string dev_launcher_go_to_home
string dev_launcher_reload
string splash_screen_text
style Theme_DevLauncher_ErrorActivity
style Theme_DevLauncher_LauncherActivity
